@echo off
echo 🔧 Fixing Gradle Cache Issues...

echo.
echo 1. Clearing Gradle cache...
if exist "C:\Users\<USER>\.gradle\caches" (
    rmdir /s /q "C:\Users\<USER>\.gradle\caches"
    echo ✅ Gradle cache cleared
) else (
    echo ⚠️  Gradle cache not found
)

echo.
echo 2. Clearing Gradle daemon...
if exist "C:\Users\<USER>\.gradle\daemon" (
    rmdir /s /q "C:\Users\<USER>\.gradle\daemon"
    echo ✅ Gradle daemon cleared
) else (
    echo ⚠️  Gradle daemon not found
)

echo.
echo 3. Cleaning Android project...
cd android
if exist "build" (
    rmdir /s /q "build"
    echo ✅ Android build folder cleared
)

if exist "app\build" (
    rmdir /s /q "app\build"
    echo ✅ App build folder cleared
)

echo.
echo 4. Running Gradle clean...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ Gradle clean failed
    goto :error
)

echo.
echo 5. Syncing Capacitor...
cd ..
call npx cap sync android
if %errorlevel% neq 0 (
    echo ❌ Capacitor sync failed
    goto :error
)

echo.
echo 6. Building Android...
call npx cap build android
if %errorlevel% neq 0 (
    echo ❌ Android build failed
    goto :error
)

echo.
echo ✅ Gradle issues fixed successfully!
echo 🚀 You can now run: npx cap run android
goto :end

:error
echo.
echo ❌ Error occurred during fix process
echo 💡 Try running Android Studio and:
echo    1. File ^> Invalidate Caches and Restart
echo    2. Build ^> Clean Project
echo    3. Build ^> Rebuild Project

:end
pause

System.register(["./networkOptimizer-legacy-Beqb1gSF.js"],function(t,s){"use strict";var e;return{setters:[t=>{e=t.f}],execute:function(){t("fetchAndStoreLokasi",async function(){try{const t=JSON.parse(localStorage.getItem("user")||"{}").lokasi_id;if(!t)return;const s=await e("https://absensiku.trunois.my.id/api/lokasi.php?api_key=absensiku_api_key_2023",{},18e5);if("success"===s.status&&Array.isArray(s.data)){const e=s.data.filter(s=>s.id==t);localStorage.setItem("lokasi_list",JSON.stringify(e))}}catch(t){}})}}});

System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(i,e){"use strict";var n,t,a,s,r,o,d,l,c,g,u,x,p,m,h,j,f,b,k,y,z,v,S,w,_,I,D,C,T,O;return{setters:[i=>{n=i.r,t=i.j,a=i.I,s=i.J,r=i.K,o=i.M,d=i.P,l=i.L,c=i.h,g=i.k,u=i.p,x=i.C,p=i.o,m=i.s,h=i.t,j=i.S,f=i.u,b=i.W,k=i.v,y=i.w,z=i.O,v=i.H,S=i.N},i=>{w=i.j,_=i.s,I=i.h,D=i.d,C=i.g,T=i.r},i=>{O=i.u},null,null],execute:function(){var e=document.createElement("style");e.textContent=".izin-dinas-page{--background: linear-gradient(135deg, #f0fdfa 0%, #e0e7ff 100%)}.izin-dinas-header{background:linear-gradient(135deg,#1a65eb,#1a65eb);color:#fff;padding:20px 0}.izin-dinas-content{padding:16px}.izin-dinas-card{margin-bottom:16px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,.1)}.izin-dinas-form-section{margin-bottom:24px}.izin-dinas-form-title{font-size:1.2rem;font-weight:700;color:#333;margin-bottom:8px}.izin-dinas-form-subtitle{font-size:.9rem;color:#666;margin-bottom:16px}.izin-dinas-required{color:#e74c3c}.izin-dinas-photo-container{text-align:center;margin:16px 0}.izin-dinas-photo-preview{border-radius:8px;border:2px solid #ddd;margin-bottom:12px}.izin-dinas-photo-preview.circular{border-radius:50%}.izin-dinas-photo-button{margin-top:8px}.izin-dinas-submit-section{margin-top:24px}.izin-dinas-submit-button{height:50px;font-size:1.1rem;font-weight:700;border-radius:12px}.izin-dinas-submit-note{text-align:center;font-size:.8rem;color:#666;margin-top:12px}.izin-dinas-status-card{border-left:4px solid #1a65eb;margin-bottom:20px}.izin-dinas-status-header{display:flex;align-items:center;margin-bottom:12px}.izin-dinas-status-icon{font-size:1.5rem;margin-right:12px}.izin-dinas-status-title{font-weight:700;font-size:1.1rem;color:#333}.izin-dinas-status-subtitle{font-size:.9rem;color:#666}.izin-dinas-info-box{padding:12px;border-radius:8px;margin-top:12px}.izin-dinas-info-box.warning{background-color:#fff3cd;border:1px solid #ffeaa7}.izin-dinas-info-title{font-weight:700;margin-bottom:4px}.izin-dinas-info-text{font-size:.85rem}.izin-dinas-loading{text-align:center;padding:20px}.izin-dinas-loading ion-spinner{margin-bottom:10px}.izin-dinas-form ion-item{--border-radius: 8px;--background: #fff;margin-bottom:12px;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.1)}.izin-dinas-form ion-label{font-weight:500;color:#333}.izin-dinas-form ion-input,.izin-dinas-form ion-textarea,.izin-dinas-form ion-datetime{--color: #333;--placeholder-color: #999}.izin-dinas-photo-section{background:#f8f9fa;border-radius:12px;padding:20px;margin:16px 0}.izin-dinas-photo-title{font-size:1.1rem;font-weight:700;color:#333;margin-bottom:8px;text-align:center}.izin-dinas-photo-subtitle{font-size:.9rem;color:#666;margin-bottom:16px;text-align:center}.izin-dinas-photo-item{margin-bottom:24px}.izin-dinas-photo-label{display:block;font-weight:700;color:#333;margin-bottom:8px}.izin-dinas-button-primary{--background: #1a65eb;--background-activated: #1557d1;--color: white;--border-radius: 12px;font-weight:700}.izin-dinas-button-outline{--border-color: #1a65eb;--color: #1a65eb;--border-radius: 8px}@media (max-width: 768px){.izin-dinas-content{padding:12px}.izin-dinas-photo-preview{max-width:100%;height:auto}.izin-dinas-form-title{font-size:1.1rem}}.izin-dinas-card{animation:slideInUp .3s ease-out}@keyframes slideInUp{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}.izin-dinas-status-success{color:#4caf50}.izin-dinas-status-warning{color:#ff9800}.izin-dinas-status-info{color:#2196f3}.izin-dinas-status-danger{color:#f44336}\n",document.head.appendChild(e),i("default",()=>{const i=JSON.parse(localStorage.getItem("user")||"{}"),e=O(),F=n.useRef(null),A=n.useRef(null),[M,R]=n.useState(""),[B,W]=n.useState(""),[P,U]=n.useState(""),[H,$]=n.useState(""),[J,N]=n.useState(""),[E,K]=n.useState(""),[L,G]=n.useState(!1),[Y,q]=n.useState(null),[Q,V]=n.useState(""),[X,Z]=n.useState(""),[ii,ei]=n.useState(!1),[ni,ti]=n.useState(!1),[ai,si]=n.useState(""),[ri,oi]=n.useState("success"),[di,li]=n.useState(!1),[ci,gi]=n.useState(""),[ui,xi]=n.useState(null),[pi,mi]=n.useState(!0),hi=i=>{q(i),V(""),Z(""),G(!0)},ji=async()=>{Z(""),V("");try{const e="wajah"===Y?"user":"environment";try{const i=await navigator.mediaDevices.getUserMedia({video:{facingMode:{exact:e},width:{ideal:1280},height:{ideal:720}}});F.current&&(F.current.srcObject=i)}catch(i){const n=await navigator.mediaDevices.getUserMedia({video:{facingMode:e,width:{ideal:1280},height:{ideal:720}}});F.current&&(F.current.srcObject=n),Z(`Kamera ${"user"===e?"depan":"belakang"} tidak tersedia. Menggunakan kamera yang tersedia.`)}}catch(e){Z("Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.")}},fi=()=>{F.current&&F.current.srcObject&&(F.current.srcObject.getTracks().forEach(i=>i.stop()),F.current.srcObject=null)},bi=()=>{fi(),G(!1),q(null),V(""),Z("")};n.useEffect(()=>(L&&ji(),()=>{L&&fi()}),[L]);const ki=(i,e)=>{si(i),oi(e),ti(!0)},yi=()=>{R(""),W(""),U(""),$(""),N(""),K("")},zi=i=>i?i.substring(0,5):"-";return n.useEffect(()=>{(async()=>{if(i.id||i.nik){mi(!0);try{const e=(new Date).toISOString().split("T")[0],n=i.id||i.nik,t=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${n}&tanggal=${e}`),a=await t.json();"success"===a.status&&a.data.length>0?xi({jam_masuk:a.data[0].jam_masuk,jam_pulang:a.data[0].jam_pulang}):xi(null)}catch(e){xi(null)}finally{mi(!1)}}else mi(!1)})()},[]),t.jsxs(a,{children:[t.jsx(s,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},children:t.jsxs(r,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[t.jsx(o,{slot:"start",children:t.jsx(d,{defaultHref:"/histori-izin-dinas",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),t.jsx(l,{style:{color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center"},children:"Izin Dinas"})]})}),t.jsxs(c,{className:"ion-padding",children:[pi?t.jsx(g,{children:t.jsx(u,{children:t.jsxs("div",{style:{textAlign:"center",padding:"20px"},children:[t.jsx(x,{name:"crescent"}),t.jsx("p",{style:{margin:"10px 0 0 0",color:"#666"},children:"Memuat status absensi..."})]})})}):ui?.jam_masuk?t.jsx(g,{style:{marginBottom:"20px",border:ui.jam_pulang?"2px solid #4caf50":"2px solid #ff9800"},children:t.jsxs(u,{children:[t.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"},children:[t.jsx(p,{icon:ui.jam_pulang?w:_,style:{fontSize:"1.5rem",color:ui.jam_pulang?"#4caf50":"#ff9800",marginRight:"12px"}}),t.jsxs("div",{children:[t.jsx("div",{style:{fontWeight:"bold",fontSize:"1.1rem",color:"#333"},children:"Status Absensi Hari Ini"}),t.jsxs("div",{style:{fontSize:"0.9rem",color:"#666"},children:["Masuk: ",zi(ui.jam_masuk)," | Pulang: ",zi(ui.jam_pulang)]})]})]}),!ui.jam_pulang&&t.jsxs("div",{style:{padding:"12px",backgroundColor:"#fff3cd",borderRadius:"8px",border:"1px solid #ffeaa7"},children:[t.jsx("div",{style:{fontSize:"0.9rem",color:"#856404",fontWeight:"bold",marginBottom:"4px"},children:"ℹ️ Informasi Penting"}),t.jsx("div",{style:{fontSize:"0.85rem",color:"#856404"},children:"Anda sudah absen masuk hari ini. Izin dinas ini akan digunakan sebagai pengganti absen pulang."})]})]})}):null,t.jsx(g,{children:t.jsxs(u,{children:[t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx("h2",{style:{margin:"0 0 8px 0",color:"#333"},children:"Form Pengajuan Izin Dinas"}),t.jsx("p",{style:{margin:"0",fontSize:"0.9rem",color:"#666"},children:"Lengkapi form di bawah untuk mengajukan izin dinas"})]}),t.jsxs(m,{children:[t.jsxs(h,{position:"stacked",children:["Tanggal Mulai ",t.jsx("span",{style:{color:"red"},children:"*"})]}),t.jsx(j,{value:M,onIonChange:i=>R(i.detail.value),presentation:"date",locale:"id-ID",min:(new Date).toISOString().split("T")[0]})]}),t.jsxs(m,{children:[t.jsxs(h,{position:"stacked",children:["Tanggal Selesai ",t.jsx("span",{style:{color:"red"},children:"*"})]}),t.jsx(j,{value:B,onIonChange:i=>W(i.detail.value),presentation:"date",locale:"id-ID",min:M||(new Date).toISOString().split("T")[0]})]}),t.jsxs(m,{children:[t.jsxs(h,{position:"stacked",children:["Tujuan Dinas ",t.jsx("span",{style:{color:"red"},children:"*"})]}),t.jsx(f,{value:P,onIonInput:i=>U(i.detail.value),placeholder:"Contoh: Rapat koordinasi di kantor pusat"})]}),t.jsxs(m,{children:[t.jsxs(h,{position:"stacked",children:["Keterangan ",t.jsx("span",{style:{color:"red"},children:"*"})]}),t.jsx(b,{value:H,onIonInput:i=>$(i.detail.value),placeholder:"Jelaskan detail kegiatan dinas yang akan dilakukan",rows:3})]})]})}),t.jsx(g,{children:t.jsxs(u,{children:[t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx("h3",{style:{margin:"0 0 8px 0",color:"#333"},children:"Upload Dokumen"}),t.jsx("p",{style:{margin:"0",fontSize:"0.9rem",color:"#666"},children:"Ambil foto surat tugas dan foto wajah untuk verifikasi"})]}),t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx(h,{children:t.jsxs("strong",{children:["Foto Surat Tugas ",t.jsx("span",{style:{color:"red"},children:"*"})]})}),t.jsx("div",{style:{marginTop:"8px"},children:J?t.jsxs("div",{style:{textAlign:"center"},children:[t.jsx("img",{src:J,alt:"Foto Surat Tugas",style:{width:"200px",height:"150px",objectFit:"cover",borderRadius:"8px",border:"2px solid #ddd",marginBottom:"12px"}}),t.jsx("div",{children:t.jsxs(k,{size:"small",fill:"outline",onClick:()=>hi("surat"),children:[t.jsx(p,{icon:I,slot:"start"}),"Ambil Ulang"]})})]}):t.jsxs(k,{expand:"block",fill:"outline",onClick:()=>hi("surat"),children:[t.jsx(p,{icon:I,slot:"start"}),"Ambil Foto Surat Tugas"]})})]}),t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx(h,{children:t.jsxs("strong",{children:["Foto Wajah ",t.jsx("span",{style:{color:"red"},children:"*"})]})}),t.jsx("div",{style:{marginTop:"8px"},children:E?t.jsxs("div",{style:{textAlign:"center"},children:[t.jsx("img",{src:E,alt:"Foto Wajah",style:{width:"150px",height:"150px",objectFit:"cover",borderRadius:"50%",border:"2px solid #ddd",marginBottom:"12px"}}),t.jsx("div",{children:t.jsxs(k,{size:"small",fill:"outline",onClick:()=>hi("wajah"),children:[t.jsx(p,{icon:I,slot:"start"}),"Ambil Ulang"]})})]}):t.jsxs(k,{expand:"block",fill:"outline",onClick:()=>hi("wajah"),children:[t.jsx(p,{icon:I,slot:"start"}),"Ambil Foto Wajah"]})})]})]})}),t.jsx(g,{children:t.jsxs(u,{children:[t.jsx(k,{expand:"block",onClick:async()=>{if(i.id||i.nik)if(M&&B&&P&&H)if(J&&E){ei(!0);try{const t=i=>new Date(i).toISOString().split("T")[0],a={api_key:"absensiku_api_key_2023",user_id:i.id||i.nik,tanggal_mulai:t(M),tanggal_selesai:t(B),tujuan:P,keterangan:H,foto_surat_tugas_base64:J,foto_wajah_base64:E,status:"pending",is_notified:"0"},s=await fetch("https://absensiku.trunois.my.id/api/izin_dinas.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok){const i=await s.text();return void ki(`Server error (${s.status}): ${i.substring(0,100)}`,"danger")}const r=await s.text();let o;try{o=JSON.parse(r)}catch(n){return void ki("Server mengembalikan response yang tidak valid","danger")}"success"===o.status?(ki("Izin dinas berhasil diajukan","success"),yi(),setTimeout(()=>{e.push("/histori-izin-dinas")},2e3)):ki(o.message||"Gagal mengajukan izin dinas","danger")}catch(t){ki("Terjadi kesalahan koneksi","danger")}finally{ei(!1)}}else ki("Mohon ambil foto surat tugas dan foto wajah","danger");else ki("Mohon lengkapi semua field yang wajib diisi","danger");else ki("Data user tidak ditemukan. Silakan login ulang.","danger")},disabled:ii,style:{height:"50px",fontSize:"1.1rem",fontWeight:"bold"},children:ii?t.jsxs(t.Fragment,{children:[t.jsx(x,{name:"crescent",style:{marginRight:"8px"}}),"Mengirim..."]}):t.jsxs(t.Fragment,{children:[t.jsx(p,{icon:D,slot:"start"}),"Ajukan Izin Dinas"]})}),t.jsx("div",{style:{marginTop:"12px",textAlign:"center",fontSize:"0.8rem",color:"#666"},children:"Pastikan semua data sudah benar sebelum mengirim"})]})}),t.jsx(y,{isOpen:ni,onDidDismiss:()=>ti(!1),message:ai,duration:3e3,color:ri}),t.jsx(z,{isOpen:di,onDidDismiss:()=>li(!1),header:"Informasi",message:ci,buttons:["OK"]}),t.jsxs(v,{isOpen:L,onDidDismiss:bi,children:[t.jsxs(s,{children:[t.jsxs(r,{children:[t.jsxs(l,{children:["Ambil Foto ","surat"===Y?"Surat Tugas":"Wajah"]}),t.jsx(o,{slot:"end",children:t.jsx(k,{onClick:bi,children:t.jsx(p,{icon:C})})})]}),t.jsxs("div",{style:{padding:"8px 16px",backgroundColor:"rgba(26, 101, 235, 0.1)",fontSize:"0.85rem",color:"#1a65eb",textAlign:"center"},children:["📷 Menggunakan kamera ","surat"===Y?"belakang":"depan","surat"===Y?" untuk foto dokumen":" untuk foto wajah"]})]}),t.jsx(c,{children:t.jsxs("div",{style:{padding:"16px",display:"flex",flexDirection:"column",height:"100%"},children:[t.jsxs("div",{style:{flex:1,display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"16px"},children:[Q?t.jsx("img",{src:Q,alt:"Preview Foto",style:{width:"100%",maxHeight:"70vh",borderRadius:"12px",objectFit:"cover"}}):t.jsx("video",{ref:F,autoPlay:!0,playsInline:!0,style:{width:"100%",maxHeight:"70vh",borderRadius:"12px",background:"#000",objectFit:"cover"}}),t.jsx("canvas",{ref:A,style:{display:"none"}})]}),X&&t.jsx(S,{color:"danger",children:t.jsx("p",{style:{textAlign:"center",margin:"8px 0"},children:X})}),t.jsx("div",{style:{display:"flex",justifyContent:"center",gap:"12px",marginTop:"auto",paddingBottom:"16px"},children:Q?t.jsxs(t.Fragment,{children:[t.jsxs(k,{color:"medium",onClick:()=>{V(""),ji()},size:"large",shape:"round",children:[t.jsx(p,{icon:T,slot:"start"}),"Ulangi"]}),t.jsxs(k,{color:"success",onClick:()=>{Q&&Y&&("surat"===Y?N(Q):K(Q),(new Date).toISOString(),ki(`Foto ${"surat"===Y?"surat tugas":"wajah"} berhasil diambil`,"success"),bi())},size:"large",shape:"round",children:[t.jsx(p,{icon:w,slot:"start"}),"Gunakan"]})]}):t.jsxs(k,{color:"primary",onClick:()=>{if(F.current&&A.current){const i=F.current,e=A.current;e.width=i.videoWidth,e.height=i.videoHeight;const n=e.getContext("2d");n&&(n.drawImage(i,0,0,e.width,e.height),V(e.toDataURL("image/jpeg",.85))),fi()}},size:"large",shape:"round",disabled:!!X,children:[t.jsx(p,{icon:I,slot:"start"}),"Ambil Foto"]})})]})})]})]})]})})}}});

System.register([],function(a,t){"use strict";return{execute:function(){a("fetchAndStoreJamKerjaBidang",async function(){try{const a=JSON.parse(localStorage.getItem("user")||"{}").bidang_id;if(!a)return;const t=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]").map(a=>a.id),e=await fetch("https://absensiku.trunois.my.id/api/jam_kerja_bidang.php?api_key=absensiku_api_key_2023"),i=await e.json();if("success"===i.status&&Array.isArray(i.data)){const e=i.data.filter(e=>e.bidang_id==a&&t.includes(e.jam_kerja_id));localStorage.setItem("jam_kerja_bidang_list",JSON.stringify(e))}}catch(a){}})}}});

import { fetchWithCache } from './networkOptimizer';

export async function fetchAndStoreHariLibur() {
  try {
    // Gunakan cache yang lebih lama untuk hari libur karena data ini jarang berubah
    const data = await fetchWithCache(
      'https://absensiku.trunois.my.id/api/hari_libur.php?api_key=absensiku_api_key_2023',
      {},
      24 * 60 * 60 * 1000 // Cache 24 jam karena hari libur jarang berubah
    );

    if (data.status === 'success' && Array.isArray(data.data)) {
      localStorage.setItem('hari_libur_list', JSON.stringify(data.data));
    }
  } catch (err) {
    console.error('Error fetching hari libur:', err);
    // Fallback ke data yang sudah ada di localStorage jika ada
  }
}

export function isTodayLibur(): { libur: boolean; nama?: string } {
  const list = JSON.parse(localStorage.getItem('hari_libur_list') || '[]');
  const today = new Date();
  const todayStr = today.toISOString().slice(0, 10); // yyyy-mm-dd
  const found = list.find((item: any) => item.tanggal === todayStr);
  if (found) {
    return { libur: true, nama: found.nama_libur };
  }
  return { libur: false };
} 
System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./networkOptimizer-legacy-Beqb1gSF.js","./jamKerja-legacy-BJSQBBXU.js","./jamKerjaBidang-legacy-D-Z9PwNM.js","./bidang-legacy-BYcpxj0T.js","./lokasi-legacy-Bdcnjewv.js","./capacitor-legacy-cVgeOc-7.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js"],function(e,a){"use strict";var t,n,o,i,s,r,l,c,d,m,p,h,u,g,x,f,b,w,j,y,k,_,v,S,N,z,I,T,A,C,D,O,L,E,J,K,M,B;return{setters:[e=>{t=e.r,n=e.D,o=e.d,i=e.j,s=e.I,r=e.h,l=e.E,c=e.F,d=e.G,m=e.v,p=e.o,h=e.C,u=e.H,g=e.J,x=e.K,f=e.L,b=e.M,w=e.N,j=e.O},e=>{y=e.l,k=e.c,_=e.d,v=e.p,S=e.a,N=e.b,z=e.t,I=e.e,T=e.f,A=e.g,C=e.h},e=>{D=e.f,O=e.b},e=>{L=e.fetchAndStoreJamKerja},e=>{E=e.fetchAndStoreJamKerjaBidang},e=>{J=e.fetchAndStoreBidang},e=>{K=e.fetchAndStoreLokasi},e=>{M=e.C},e=>{B=e.u},null],execute:function(){var $=document.createElement("style");async function H(){try{const e=await D("https://absensiku.trunois.my.id/api/hari_libur.php?api_key=absensiku_api_key_2023",{},864e5);"success"===e.status&&Array.isArray(e.data)&&localStorage.setItem("hari_libur_list",JSON.stringify(e.data))}catch(e){}}function F(){const e=JSON.parse(localStorage.getItem("hari_libur_list")||"[]"),a=(new Date).toISOString().slice(0,10),t=e.find(e=>e.tanggal===a);return t?{libur:!0,nama:t.nama_libur}:{libur:!1}}$.textContent='.home-bg{--background: linear-gradient(135deg, #f0fdfa 0%, #e0e7ff 100%);min-height:100vh;display:flex;align-items:flex-start;justify-content:center}.home-container{width:100%;max-width:420px;margin:0 auto;padding-top:32px;display:flex;flex-direction:column;align-items:center}.home-profile-card{width:100%;border-radius:18px;box-shadow:0 4px 24px rgba(0,0,0,.07);margin-bottom:24px;background:#fff}.home-profile-header{display:flex;align-items:center;padding:24px 18px 10px;border-bottom:1px solid #f1f5f9;background:transparent}.home-avatar{width:72px;height:72px;margin-right:18px;border-radius:50%;box-shadow:0 2px 12px rgba(0,0,0,.08);background:#f3f4f6}.home-profile-info{display:flex;flex-direction:column;justify-content:center}.home-nama{font-size:1.3rem;font-weight:700;margin-bottom:2px}.home-jabatan{font-size:1.05rem;color:#64748b}.home-card-content{padding:18px 18px 10px;text-align:center}.home-desc{color:#64748b;font-size:1rem;margin:10px 0 18px}.home-logout-btn{margin-top:12px;font-weight:600;border-radius:24px;letter-spacing:.5px;font-size:1.1rem}.home2-bg{--background: #fff;min-height:100vh;padding:0;animation:fadeIn .8s}@keyframes fadeIn{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:none}}.home2-header,.home2-header-content,.home2-nama,.home2-jabatan{animation:fadeIn 1s}.home2-camera-btn{transition:transform .15s,box-shadow .2s;position:relative;overflow:hidden;animation:cameraPulse 1.5s infinite}@keyframes cameraPulse{0%{box-shadow:0 0 rgba(24,128,255,.18)}70%{box-shadow:0 0 0 24px rgba(24,128,255,.01)}to{box-shadow:0 0 rgba(24,128,255,.18)}}.home2-camera-btn:active{transform:scale(.93);box-shadow:0 2px 8px rgba(24,128,255,.18)}.home2-camera-btn:after{content:"";position:absolute;left:50%;top:50%;width:0;height:0;background:rgba(255,255,255,.25);border-radius:50%;transform:translate(-50%,-50%);transition:width .3s,height .3s;pointer-events:none}.home2-camera-btn:active:after{width:180px;height:180px}.home2-menu-btn{transition:box-shadow .2s,transform .15s,background .2s;will-change:transform;user-select:none}.home2-menu-btn:active{box-shadow:0 8px 32px rgba(24,128,255,.18);background:#e0e7ff;transform:scale(1.07)}.home2-menu-btn ion-icon{transition:transform .18s}.home2-menu-btn:active ion-icon{animation:bounce .4s}@keyframes bounce{0%{transform:scale(1)}30%{transform:scale(1.25) translateY(-6px)}60%{transform:scale(.95) translateY(2px)}to{transform:scale(1)}}.home2-status-box{transition:box-shadow .2s,background .2s,color .2s}.home2-status-box.status-highlight{background:#e0e7ff;color:#1880ff;box-shadow:0 0 0 4px rgba(24,128,255,.2);animation:highlightFade 1.2s}@keyframes highlightFade{0%{background:#1880ff;color:#fff}to{background:#e0e7ff;color:#1880ff}}.home2-status-box[title]:hover:after{content:attr(title);position:absolute;left:50%;top:0;transform:translate(-50%,-120%);background:#222;color:#fff;padding:4px 12px;border-radius:8px;font-size:.9rem;white-space:nowrap;z-index:10;opacity:.95}.home2-main,.home2-menu-row,.home2-status-row,.home2-status-title{transition:all .3s}.home2-header-content{width:100%;max-width:420px;padding:32px 0 18px;text-align:center}.home2-nama{color:#fff;font-size:2rem;font-weight:700;margin:0 0 2px}.home2-jabatan{color:#e0e7ff;font-size:1.1rem;margin:0 0 20px;font-weight:500}.home2-main{width:100%;max-width:420px;margin:0 auto;display:flex;flex-direction:column;align-items:center;padding-top:18px}.home2-clock-row{display:flex;align-items:center;justify-content:center;gap:12px;margin-bottom:22px}.home2-clock-box{position:relative;background:linear-gradient(135deg,#0ea5e9,#2563eb);border-radius:22px;box-shadow:0 14px 34px rgba(24,128,255,.25),inset 0 1px rgba(255,255,255,.3);font-size:4rem;font-weight:800;color:#fff;letter-spacing:2px;width:140px;height:112px;display:flex;align-items:center;justify-content:center;padding-top:2px;transition:transform .18s ease,box-shadow .25s ease,filter .25s ease;will-change:transform,box-shadow}.home2-clock-box:after{content:"";position:absolute;top:0;left:0;right:0;height:48%;border-top-left-radius:22px;border-top-right-radius:22px;background:linear-gradient(180deg,rgba(255,255,255,.35),rgba(255,255,255,.05));pointer-events:none}.home2-clock-box:active{transform:translateY(1px) scale(.99);box-shadow:0 8px 24px rgba(24,128,255,.25)}.home2-clock-sep{font-size:3.8rem;font-weight:800;color:#1880ff;text-shadow:0 2px 10px rgba(24,128,255,.35);animation:clockBlink 1s steps(2,start) infinite}@keyframes clockBlink{0%{opacity:1}50%{opacity:.25}to{opacity:1}}.home2-menu-row{display:flex;justify-content:center;gap:18px;margin-bottom:28px}.home2-menu-btn{position:relative;border-radius:22px;width:200px;height:200px;display:flex;flex-direction:column;align-items:center;justify-content:center;font-weight:700;font-size:1.2rem;color:#fff;cursor:pointer;overflow:hidden;transition:transform .18s,box-shadow .2s,opacity .2s;box-shadow:0 8px 28px rgba(0,0,0,.12)}.home2-menu-btn:active{transform:translateY(2px) scale(.98);box-shadow:0 2px 12px rgba(0,0,0,.12)}.menu-icon-wrap{width:70px;height:70px;border-radius:20px;display:flex;align-items:center;justify-content:center;margin-bottom:10px;background:rgba(255,255,255,.18);box-shadow:inset 0 0 0 2px rgba(255,255,255,.25)}.home2-menu-btn ion-icon{font-size:2rem}.menu-label{letter-spacing:.3px;margin-top:-5px}.menu-histori,.menu-izin,.menu-rapat,.menu-laporan,.menu-profil,.menu-lembur,.menu-download{background:linear-gradient(135deg,#4f46e5,#0ea5e9)}.download-horizontal-wrap{width:70%;display:flex;justify-content:center;margin:10px 0 20px}.download-horizontal-btn{width:70%;max-width:420px;display:flex;align-items:center;justify-content:flex-start;gap:12px;padding:14px 16px;border-radius:16px;border:1px solid rgba(24,128,255,.15);background:linear-gradient(135deg,#0ea5e9,#2563eb);color:#fff;font-weight:700;letter-spacing:.2px;box-shadow:0 8px 24px rgba(24,128,255,.18);transition:transform .15s,box-shadow .2s,filter .2s}.download-horizontal-btn:hover{box-shadow:0 12px 30px rgba(24,128,255,.28)}.download-horizontal-btn:active{transform:translateY(1px) scale(.99)}.download-horizontal-btn.disabled{opacity:.6;cursor:not-allowed}.download-icon ion-icon{font-size:22px}.download-text{font-size:1rem}.download-spinner{margin-left:auto}.menu-disabled{background:linear-gradient(135deg,#4f46e5,#0ea5e9);opacity:.4;pointer-events:none}.home2-status-wrap{width:100%;margin-top:10px;display:flex;flex-direction:column;align-items:center}.home2-status-title{background:#1880ff;border-radius:32px;padding:16px 80px;font-size:1.5rem;font-weight:700;color:#fff;margin-bottom:5px;box-shadow:0 2px 8px rgba(0,0,0,.04);min-width:340px;width:max-content;max-width:90vw;text-align:center}.home2-status-row{display:flex;gap:18px;justify-content:center;width:100%}.home2-status-box{position:relative;overflow:hidden;background:rgba(255,255,255,.85);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(24,128,255,.12);border-radius:22px;box-shadow:0 10px 24px rgba(0,0,0,.08);width:180px;min-height:180px;padding:14px 12px;display:flex;flex-direction:column;align-items:center;justify-content:center;font-size:1.2rem;font-weight:700;color:#1f2937;transition:transform .18s ease,box-shadow .25s ease,background .25s ease}.home2-status-box:before{content:"";position:absolute;top:0;left:0;right:0;height:6px;background:linear-gradient(90deg,#0ea5e9,#2563eb);opacity:.9}.home2-status-box:after{content:"";position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none;border-radius:inherit;box-shadow:inset 0 1px rgba(255,255,255,.6)}.home2-status-box:hover{transform:translateY(-2px) scale(1.02);box-shadow:0 16px 32px rgba(24,128,255,.2)}.status-icon{position:absolute;top:10px;right:10px;font-size:18px;color:#9aa7b1}.status-masuk,.status-pulang{background:#fff}.home-header-avatar{display:flex;justify-content:center;margin-bottom:10px}.home-header-avatar ion-avatar,.home-header-avatar .home-avatar-initial{width:72px;height:72px;border:3px solid rgba(255,255,255,.85);border-radius:50%;box-shadow:0 6px 22px rgba(0,0,0,.18);overflow:hidden}.home-avatar-initial{display:flex;align-items:center;justify-content:center;color:#fff;background:linear-gradient(135deg,#1880ff,#005be7);font-weight:700;font-size:28px;text-shadow:0 2px 4px rgba(0,0,0,.2);letter-spacing:.5px}.home-avatar-icon{font-size:42px;color:#1880ff}.holiday-banner{background:#ffe8e8;color:#c62828;padding:12px 14px;border-radius:12px;margin:12px 0;text-align:center;font-weight:600;display:inline-flex;align-items:center}.download-wrap{display:flex;justify-content:center;margin:16px 0}.download-btn{border-radius:14px;font-weight:600}.home2-status-label{font-size:.9rem;font-weight:800;text-transform:uppercase;letter-spacing:.08em;color:#64748b;margin-bottom:6px}.home2-status-time{font-size:2.4rem;font-weight:800;color:#111827;background:linear-gradient(180deg,#111827,#334155);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent}.home2-header{width:100vw;background:linear-gradient(135deg,#1880ff 60%,#005be7);min-height:200px;border-bottom-left-radius:80px;border-bottom-right-radius:80px;display:flex;align-items:flex-end;justify-content:center;box-shadow:0 8px 32px rgba(24,128,255,.13);position:relative;z-index:2;animation:fadeInHeader 1.2s}@keyframes fadeInHeader{0%{opacity:0;transform:translateY(-40px)}to{opacity:1;transform:none}}.home2-header-content{width:100%;max-width:420px;padding:38px 0 22px;text-align:center;animation:fadeIn 1.2s}@media (max-width: 480px){.home-container{max-width:98vw;padding-top:8vw}.home-profile-card{border-radius:12px}.home-avatar{width:56px;height:56px;margin-right:12px}.home-nama{font-size:1.05rem}.home-jabatan{font-size:.95rem}.home2-header{min-height:120px;border-bottom-left-radius:38px;border-bottom-right-radius:38px}.home2-header-content{padding:18px 0 8px}.home2-nama{font-size:1.2rem}.home2-jabatan{font-size:.95rem}.home2-main{padding-top:10px}.home2-clock-box{width:100px;height:60px;font-size:2.1rem}.home2-clock-sep{font-size:1.6rem}.home2-menu-btn{width:100px;height:100px;font-size:.95rem}.home2-menu-btn ion-icon{font-size:2.3rem}.home2-status-box{width:200px;height:200px;font-size:.95rem}.home2-status-title{font-size:1rem;padding:10px 18px;min-width:0;width:100%;max-width:100vw}.home2-status-label{font-size:1rem}.home2-status-time{font-size:1.2rem}}.floating-camera-container{position:fixed;bottom:30px;left:50%;transform:translate(-50%);z-index:1000}.floating-camera-btn{background:#1880ff;border:none;border-radius:50%;width:200px;height:200px;display:flex;align-items:center;justify-content:center;box-shadow:0 8px 24px rgba(24,128,255,.3);cursor:pointer;transition:all .3s ease;animation:floatingPulse 2s infinite}.floating-camera-btn:hover{transform:scale(1.1);box-shadow:0 12px 32px rgba(24,128,255,.4)}.floating-camera-btn:active{transform:scale(.95);background:#0d5ec2}.floating-camera-icon{color:#fff;font-size:2rem}@keyframes floatingPulse{0%{box-shadow:0 8px 24px rgba(24,128,255,.3)}50%{box-shadow:0 8px 24px rgba(24,128,255,.5),0 0 0 10px rgba(24,128,255,.1)}to{box-shadow:0 8px 24px rgba(24,128,255,.3)}}@media (max-width: 768px){.floating-camera-container{bottom:20px}.floating-camera-btn{width:100px;height:100px}.floating-camera-icon{font-size:3rem}}\n',document.head.appendChild($);const P=Object.freeze(Object.defineProperty({__proto__:null,fetchAndStoreHariLibur:H,isTodayLibur:F},Symbol.toStringTag,{value:"Module"}));e("default",()=>{const e=t.useMemo(()=>{try{const e=localStorage.getItem("user"),a=e?JSON.parse(e):{};return Array.isArray(a)?a[0]||{}:a}catch{return{}}},[]),[D,$]=t.useState({loading:!1,downloading:!1,isModalOpen:!1,showDownloadAlert:!1,showStatusAlert:!1}),[W,R]=t.useState({h:"--",m:"--"}),[Y,q]=t.useState(null),[G,U]=t.useState(null),[Q,V]=t.useState({libur:!1}),[X,Z]=t.useState(null),[ee]=n(),ae=t.useRef(null),te=B(),ne=t.useMemo(()=>(new Date).toISOString().split("T")[0],[]),oe=t.useMemo(()=>e.id||e.nik,[e.id,e.nik]),ie=t.useCallback(()=>{try{const e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]").filter(e=>new Date(e.timestamp).toISOString().split("T")[0]===ne&&e.user_id===oe),a=JSON.parse(localStorage.getItem("absensi_backup")||"[]").filter(e=>new Date(e.timestamp).toISOString().split("T")[0]===ne&&e.user_id===oe),t=[...e,...a];if(t.length>0){const e={id:"offline_"+ne,user_id:oe,tanggal:ne,jam_masuk:null,foto_masuk:null,lokasi_masuk:null,jam_pulang:null,foto_pulang:null,lokasi_pulang:null,status:"Hadir",keterangan:"Data offline"};return t.forEach(a=>{("masuk"===a.jenisAbsensi||a.jam_masuk)&&(e.jam_masuk=a.jam_masuk||a.timestamp.split("T")[1].substring(0,8),e.foto_masuk=a.foto_masuk_base64||null,e.lokasi_masuk=a.lokasi_masuk||null,e.status=a.status||"Hadir"),("pulang"===a.jenisAbsensi||a.jam_pulang)&&(e.jam_pulang=a.jam_pulang||a.timestamp.split("T")[1].substring(0,8),e.foto_pulang=a.foto_pulang_base64||null,e.lokasi_pulang=a.lokasi_pulang||null)}),e}return null}catch(e){return null}},[ne,oe]),se=t.useCallback(async()=>{if(oe){$(e=>({...e,loading:!0}));try{let a=null;try{const e=new AbortController,t=setTimeout(()=>e.abort(),5e3),n=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${oe}&tanggal=${ne}`,{signal:e.signal});clearTimeout(t);const o=await n.json();"success"===o.status&&o.data.length>0&&(a=o.data[0])}catch(e){}const t=ie();q(a||t||null)}catch(e){const a=ie();q(a)}finally{$(e=>({...e,loading:!1}))}}},[oe,ne,ie]);t.useEffect(()=>{const e=()=>{const e=new Date,a={h:e.getHours().toString().padStart(2,"0"),m:e.getMinutes().toString().padStart(2,"0")};R(e=>e.h!==a.h||e.m!==a.m?a:e)};e();const a=setInterval(e,3e4);return()=>clearInterval(a)},[]);const re=t.useCallback(async()=>{const e=localStorage.getItem("sync_in_progress");if(e){const a=parseInt(e);if(!(Date.now()-a>3e5))return;localStorage.removeItem("sync_in_progress")}try{localStorage.setItem("sync_in_progress",Date.now().toString());let e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]");if(0===e.length)return void localStorage.removeItem("sync_in_progress");let t=0;for(let n=e.length-1;n>=0;n--){const o=e[n];if(o.synced)e.splice(n,1);else try{let a;const i={...o};if(delete i.id,delete i.timestamp,delete i.synced,delete i.jenisAbsensi,"masuk"===o.jenisAbsensi)a=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});else{const e=o.tanggal,t=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${o.user_id}&tanggal=${e}`),n=await t.json();if(!("success"===n.status&&n.data.length>0))continue;i.id=n.data[0].id,a=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})}if("success"===(await a.json()).status){e.splice(n,1),t++;const a={...o,synced:!0,syncedAt:(new Date).toISOString()},i=JSON.parse(localStorage.getItem("absensi_backup")||"[]");i.push(a),i.length>30&&i.splice(0,i.length-30),localStorage.setItem("absensi_backup",JSON.stringify(i))}}catch(a){}}localStorage.setItem("offline_absensi_queue",JSON.stringify(e)),t>0&&(ee({message:`${t} data offline berhasil disinkronkan`,color:"success",duration:3e3,position:"top"}),se())}catch(a){}finally{localStorage.removeItem("sync_in_progress")}},[]),le=t.useCallback(()=>{ae.current&&clearTimeout(ae.current),ae.current=setTimeout(()=>{re()},3e3)},[re]),ce=t.useCallback(()=>{const e=localStorage.getItem("sync_in_progress");e&&Date.now()-parseInt(e)>3e4&&localStorage.removeItem("sync_in_progress")},[]),de=t.useCallback(async e=>{try{await se(),navigator.onLine&&await re();const{fetchAndStoreHariLibur:e,isTodayLibur:a}=await o(()=>Promise.resolve().then(()=>P),void 0);await e(),V(a()),ee({message:"Data berhasil diperbarui",color:"success",duration:2e3,position:"top"})}catch(a){ee({message:"Gagal memperbarui data",color:"danger",duration:2e3,position:"top"})}finally{e.detail.complete()}},[se,re,ee]);t.useEffect(()=>{se(),ce(),navigator.onLine&&setTimeout(()=>{le()},1e3)},[se,ce,le]);const me=t.useCallback(()=>{document.hidden||(se(),navigator.onLine&&le())},[se,le]);t.useEffect(()=>(document.addEventListener("visibilitychange",me),()=>{document.removeEventListener("visibilitychange",me)}),[me]),t.useEffect(()=>{const e=()=>{le()},a=()=>{};return window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a),ae.current&&clearTimeout(ae.current)}},[]),t.useEffect(()=>{(async()=>{try{await H(),V(F());const e=localStorage.getItem("jam_kerja_list"),t=localStorage.getItem("jam_kerja_bidang_list");if(!e||!t){const{fetchAndStoreJamKerja:e}=await o(()=>a.import("./jamKerja-legacy-BJSQBBXU.js"),void 0),{fetchAndStoreJamKerjaBidang:t}=await o(()=>a.import("./jamKerjaBidang-legacy-D-Z9PwNM.js"),void 0);await e(),await t()}}catch(e){}})()},[]),t.useEffect(()=>{V(F())},[D.loading]),t.useEffect(()=>{(async()=>{try{await M.requestPermissions()}catch(e){}})()},[]);const pe=t.useCallback(async()=>{$(e=>({...e,downloading:!0}));const e=await async function(){const e={hariLibur:!1,jamKerja:!1,jamKerjaBidang:!1,bidang:!1,lokasi:!1},a=[async()=>{try{return await H(),e.hariLibur=!!localStorage.getItem("hari_libur_list"),!0}catch{return e.hariLibur=!1,!1}},async()=>{try{return await L(),e.jamKerja=!!localStorage.getItem("jam_kerja_list"),!0}catch{return e.jamKerja=!1,!1}},async()=>{try{return await E(),e.jamKerjaBidang=!!localStorage.getItem("jam_kerja_bidang_list"),!0}catch{return e.jamKerjaBidang=!1,!1}},async()=>{try{return await J(),e.bidang=!!localStorage.getItem("bidang_list"),!0}catch{return e.bidang=!1,!1}},async()=>{try{return await K(),e.lokasi=!!localStorage.getItem("lokasi_list"),!0}catch{return e.lokasi=!1,!1}}];return await O(a,2),e}();$(e=>({...e,downloading:!1,showStatusAlert:!0})),Z(e)},[]),he=e=>e?`https://absensiku.trunois.my.id/uploads/${e}`:null,ue=e=>e?e.substring(0,5):"- : -",ge=(e,a)=>{if(!e)return"Belum Absen";try{const a=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),t=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),n=(new Date).toLocaleDateString("id-ID",{weekday:"long"}),o=t.find(e=>e.hari===n&&e.jam_kerja_id);if(!o||!o.jam_kerja_id)return"Tepat Waktu";const i=a.find(e=>e.id==o.jam_kerja_id);if(!i)return"Tepat Waktu";const s=new Date(`2000-01-01T${e}`);return s>new Date(`2000-01-01T${i.jam_masuk}`)?"Terlambat":"Tepat Waktu"}catch(t){return"Tepat Waktu"}},xe=t.useCallback((e,a)=>{U({url:e,title:a}),$(e=>({...e,isModalOpen:!0}))},[]),fe=t.useCallback(()=>{$(e=>({...e,isModalOpen:!1})),U(null)},[]);return i.jsxs(s,{children:[i.jsxs(r,{fullscreen:!0,className:"home2-bg",children:[i.jsx(l,{slot:"fixed",onIonRefresh:de,children:i.jsx(c,{pullingIcon:"chevron-down-circle-outline",pullingText:"Tarik untuk memperbarui...",refreshingSpinner:"circles",refreshingText:"Memperbarui data..."})}),i.jsx("div",{className:"home2-header",children:i.jsxs("div",{className:"home2-header-content",style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[i.jsx("div",{style:{flex:1,display:"flex",justifyContent:"flex-start"}}),i.jsxs("div",{style:{flex:2,textAlign:"center"},children:[i.jsx("div",{className:"home-header-avatar",children:i.jsx(d,{children:e.foto_profil?i.jsx("img",{src:`https://absensiku.trunois.my.id/uploads/${e.foto_profil}`,alt:"Avatar"}):i.jsx("div",{className:"home-avatar-initial",children:(e.nama||"?").charAt(0).toUpperCase()})})}),i.jsx("h1",{className:"home2-nama",style:{margin:0},children:e.nama||"Nama user"}),i.jsx("p",{className:"home2-jabatan",style:{margin:0},children:e.jabatan||"Jabatan"})]}),i.jsx("div",{style:{flex:1,display:"flex",justifyContent:"flex-end"},children:i.jsx(m,{onClick:()=>{localStorage.removeItem("user"),window.location.replace("/login")},color:"medium",size:"small",fill:"clear",style:{minWidth:0,padding:0,marginRight:12},children:i.jsx(p,{icon:y,slot:"icon-only",style:{fontSize:30,color:"#fff"}})})})]})}),i.jsxs("div",{className:"home2-main",children:[i.jsxs("div",{className:"home2-clock-row",children:[i.jsx("div",{className:"home2-clock-box",children:W.h}),i.jsx("div",{className:"home2-clock-sep",children:":"}),i.jsx("div",{className:"home2-clock-box",children:W.m})]}),Q.libur&&i.jsxs("div",{className:"holiday-banner",children:[i.jsx(p,{icon:k,style:{marginRight:8}}),"Hari ini libur: ",Q.nama]}),i.jsxs("div",{className:"home2-menu-row",children:[i.jsxs("div",{className:"home2-menu-btn menu-histori",onClick:()=>te.push("/histori"),children:[i.jsx("div",{className:"menu-icon-wrap",children:i.jsx(p,{icon:_})}),i.jsx("span",{className:"menu-label",children:"Histori"})]}),i.jsxs("div",{className:"home2-menu-btn menu-izin",onClick:()=>te.push("/histori-izin-dinas"),children:[i.jsx("div",{className:"menu-icon-wrap",children:i.jsx(p,{icon:k})}),i.jsx("span",{className:"menu-label",children:"Izin"})]}),i.jsxs("div",{className:"home2-menu-btn menu-rapat",onClick:()=>te.push("/rapat"),children:[i.jsx("div",{className:"menu-icon-wrap",children:i.jsx(p,{icon:v})}),i.jsx("span",{className:"menu-label",children:"Rapat/Apel"})]})]}),i.jsxs("div",{className:"home2-menu-row",style:{marginTop:"12px"},children:[i.jsxs("div",{className:"home2-menu-btn menu-laporan",onClick:()=>te.push("/laporan-harian"),children:[i.jsx("div",{className:"menu-icon-wrap",children:i.jsx(p,{icon:S})}),i.jsx("span",{className:"menu-label",children:"Lap. Harian"})]}),i.jsxs("div",{className:"home2-menu-btn menu-profil",onClick:()=>te.push("/profile"),children:[i.jsx("div",{className:"menu-icon-wrap",children:i.jsx(p,{icon:N})}),i.jsx("span",{className:"menu-label",children:"Profil"})]}),i.jsxs("div",{className:"home2-menu-btn menu-lembur",onClick:()=>te.push("/lembur"),children:[i.jsx("div",{className:"menu-icon-wrap",children:i.jsx(p,{icon:z})}),i.jsx("span",{className:"menu-label",children:"Lembur"})]})]}),i.jsx("div",{className:"download-horizontal-wrap",children:i.jsxs("button",{className:"download-horizontal-btn"+(D.downloading?" disabled":""),onClick:D.downloading?void 0:pe,children:[i.jsx("span",{className:"download-icon",children:i.jsx(p,{icon:I})}),i.jsx("span",{className:"download-text",children:D.downloading?"Memproses...":"Download Data"}),D.downloading&&i.jsx(h,{name:"crescent",className:"download-spinner"})]})}),i.jsxs("div",{className:"home2-status-wrap",children:[i.jsx("div",{className:"home2-status-title",children:"Status Absen Hari Ini"}),D.loading?i.jsxs("div",{style:{textAlign:"center",padding:"20px"},children:[i.jsx(h,{name:"crescent"}),i.jsx("p",{style:{margin:"10px 0 0 0",color:"#666"},children:"Memuat data..."})]}):i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"home2-status-row",children:[i.jsxs("div",{className:"home2-status-box",style:{display:"flex",flexDirection:"column",alignItems:"center",padding:"12px",minHeight:"180px"},children:[i.jsx("span",{className:"home2-status-label",children:"Masuk"}),i.jsx("span",{className:"home2-status-time",children:ue(Y?.jam_masuk)}),Y?.jam_masuk&&i.jsxs("div",{style:{fontSize:"0.7rem",color:(e=>{switch(e){case"Tepat Waktu":return"#4caf50";case"Terlambat":return"#f44336";case"Pulang Awal":return"#ff9800";default:return"#9e9e9e"}})(ge(Y.jam_masuk,Y.jam_pulang)),fontWeight:"bold",marginTop:"2px",marginBottom:"8px"},children:[ge(Y.jam_masuk,Y.jam_pulang),Y.id?.startsWith("offline_")&&i.jsx("span",{style:{fontSize:"0.6rem",color:"#ff9800",marginLeft:"4px",fontWeight:"normal"},children:"(Offline)"})]}),Y?.foto_masuk?i.jsx("div",{style:{marginTop:"8px"},children:he(Y.foto_masuk)&&i.jsx("img",{src:he(Y.foto_masuk),alt:"Foto Absen Masuk",style:{width:"80px",height:"80px",objectFit:"cover",borderRadius:"8px",border:"2px solid #ddd",cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease"},onClick:()=>xe(he(Y.foto_masuk),`Foto Absen Masuk - ${ue(Y.jam_masuk)} (${ge(Y.jam_masuk,Y.jam_pulang)})`),onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.05)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none"},onError:e=>{e.target.style.display="none"}})}):Y?.jam_masuk&&i.jsx("div",{style:{marginTop:"8px",fontSize:"0.7rem",color:"#999",textAlign:"center"},children:"📸 Foto tersedia"})]}),i.jsxs("div",{className:"home2-status-box",style:{display:"flex",flexDirection:"column",alignItems:"center",padding:"12px",minHeight:"180px"},children:[i.jsx("span",{className:"home2-status-label",children:"Pulang"}),i.jsx("span",{className:"home2-status-time",children:ue(Y?.jam_pulang)}),Y?.jam_pulang&&i.jsx("div",{style:{fontSize:"0.7rem",color:"#4caf50",fontWeight:"bold",marginTop:"2px",marginBottom:"8px"},children:"Selesai"}),Y?.foto_pulang?i.jsx("div",{style:{marginTop:"8px"},children:he(Y.foto_pulang)&&i.jsx("img",{src:he(Y.foto_pulang),alt:"Foto Absen Pulang",style:{width:"80px",height:"80px",objectFit:"cover",borderRadius:"8px",border:"2px solid #ddd",cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease"},onClick:()=>xe(he(Y.foto_pulang),`Foto Absen Pulang - ${ue(Y.jam_pulang)}`),onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.05)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none"},onError:e=>{e.target.style.display="none"}})}):Y?.jam_pulang&&i.jsx("div",{style:{marginTop:"8px",fontSize:"0.7rem",color:"#999",textAlign:"center"},children:"📸 Foto tersedia"})]})]}),!Y&&i.jsxs("div",{style:{textAlign:"center",padding:"20px",color:"#666"},children:[i.jsx(p,{icon:T,style:{fontSize:"2rem",color:"#ff9800",marginBottom:"8px"}}),i.jsx("p",{style:{margin:"0",fontSize:"0.9rem"},children:"Belum ada absensi hari ini"})]})]})]})]})]}),i.jsxs(u,{isOpen:D.isModalOpen,onDidDismiss:fe,children:[i.jsx(g,{children:i.jsxs(x,{children:[i.jsx(f,{children:G?.title||"Foto Absensi"}),i.jsx(b,{slot:"end",children:i.jsx(m,{onClick:fe,children:i.jsx(p,{icon:A})})})]})}),i.jsx(r,{className:"ion-padding",children:i.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"},children:G&&i.jsxs(i.Fragment,{children:[i.jsx("img",{src:G.url,alt:G.title,style:{maxWidth:"100%",maxHeight:"70vh",objectFit:"contain",borderRadius:"12px",boxShadow:"0 4px 20px rgba(0,0,0,0.1)"},onError:e=>{e.target.style.display="none"}}),i.jsx("div",{style:{marginTop:"16px",textAlign:"center",padding:"12px",backgroundColor:"#f8f9fa",borderRadius:"8px",maxWidth:"300px"},children:i.jsx(w,{color:"medium",children:i.jsx("p",{style:{margin:"0",fontSize:"0.9rem"},children:G.title})})})]})})})]}),i.jsx(j,{isOpen:D.showDownloadAlert,onDidDismiss:()=>$(e=>({...e,showDownloadAlert:!1})),header:"Download Data",message:"Data berhasil di-download dan disimpan di perangkat.",buttons:["OK"]}),i.jsx(j,{isOpen:D.showStatusAlert,onDidDismiss:()=>$(e=>({...e,showStatusAlert:!1})),header:"Status Data Anda",message:`\n          Hari Libur: ${X?.hariLibur?"✅":"❌"}\n          Jam Kerja: ${X?.jamKerja?"✅":"❌"}\n          Jam Kerja Bidang: ${X?.jamKerjaBidang?"✅":"❌"}\n          Bidang: ${X?.bidang?"✅":"❌"}\n          Lokasi: ${X?.lokasi?"✅":"❌"}\n        `,buttons:["OK"]}),i.jsx("div",{className:"floating-camera-container",children:i.jsx("button",{className:"floating-camera-btn",onClick:()=>te.push("/absensi"),disabled:Q.libur,style:Q.libur?{opacity:.5,cursor:"not-allowed"}:{},children:i.jsx(p,{icon:C,className:"floating-camera-icon"})})})]})})}}});

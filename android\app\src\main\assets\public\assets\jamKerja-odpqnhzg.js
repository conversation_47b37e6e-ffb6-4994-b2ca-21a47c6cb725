import{f as t}from"./networkOptimizer-CUziElFO.js";async function a(){try{const a=JSON.parse(localStorage.getItem("user")||"{}").bidang_id;if(!a)return;const i=await t("https://absensiku.trunois.my.id/api/jam_kerja.php?api_key=absensiku_api_key_2023",{},18e5);if("success"===i.status&&Array.isArray(i.data)){const t=i.data.filter(t=>t.bidang_id==a);localStorage.setItem("jam_kerja_list",JSON.stringify(t))}}catch(a){}}export{a as fetchAndStoreJamKerja};

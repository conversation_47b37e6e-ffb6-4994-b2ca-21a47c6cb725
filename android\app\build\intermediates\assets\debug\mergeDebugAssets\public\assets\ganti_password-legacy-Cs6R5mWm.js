System.register(["./ionic-legacy-DbGqp7zN.js","./react-vendor-legacy-wCcNgjsd.js"],function(s,a){"use strict";var e,i,n,r,t,o,d,l,u,c,p,h,j;return{setters:[s=>{e=s.r,i=s.j,n=s.I,r=s.J,t=s.K,o=s.L,d=s.h,l=s.s,u=s.t,c=s.u,p=s.v,h=s.x,j=s.O},null],execute:function(){s("default",()=>{const[s,a]=e.useState(""),[g,m]=e.useState(""),[k,x]=e.useState(""),[y,w]=e.useState(!1),[f,v]=e.useState(""),[S,b]=e.useState(!1);return i.jsxs(n,{children:[i.jsx(r,{children:i.jsx(t,{children:i.jsx(o,{children:"Ganti Password"})})}),i.jsxs(d,{className:"ion-padding",children:[i.jsxs(l,{children:[i.jsx(u,{position:"floating",children:"Password Lama"}),i.jsx(c,{type:"password",value:s,onIonChange:s=>a(s.detail.value)})]}),i.jsxs(l,{children:[i.jsx(u,{position:"floating",children:"Password Baru"}),i.jsx(c,{type:"password",value:g,onIonChange:s=>m(s.detail.value)})]}),i.jsxs(l,{children:[i.jsx(u,{position:"floating",children:"Konfirmasi Password Baru"}),i.jsx(c,{type:"password",value:k,onIonChange:s=>x(s.detail.value)})]}),i.jsx(p,{expand:"block",color:"primary",style:{marginTop:"20px"},onClick:async()=>{if(!s||!g||!k)return v("Semua field wajib diisi"),void w(!0);if(g!==k)return v("Password baru dan konfirmasi password tidak sama"),void w(!0);b(!0);try{const e=localStorage.getItem("user");if(!e)return v("Data pengguna tidak ditemukan, silakan login kembali"),w(!0),void b(!1);const i=JSON.parse(e),n=Array.isArray(i)?i[0]:i,r=await fetch(`https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(n.nik)}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({nik:n.nik,old_password:s,new_password:g})}),t=await r.json();"success"===t.status?(v("Password berhasil diubah"),w(!0),a(""),m(""),x("")):(v(t.message||"Gagal mengganti password"),w(!0))}catch(e){v("Terjadi kesalahan koneksi ke server"),w(!0)}finally{b(!1)}},children:"Simpan Perubahan"}),i.jsx(h,{isOpen:S,message:"Memproses..."}),i.jsx(j,{isOpen:y,onDidDismiss:()=>w(!1),header:"Informasi",message:f,buttons:["OK"]})]})]})})}}});

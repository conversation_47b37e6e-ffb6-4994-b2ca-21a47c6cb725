import{a6 as t,a7 as e,a8 as n}from"./ionic-CJlrxXsE.js";import"./react-vendor-DCX9i6UF.js";
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const o=(o,r,a,i,s)=>{const c=o.ownerDocument.defaultView;let d=t(o);const l=t=>d?-t.deltaX:t.deltaX;return e({el:o,gestureName:"goback-swipe",gesturePriority:101,threshold:10,canStart:e=>(d=t(o),(t=>{const{startX:e}=t;return d?e>=c.innerWidth-50:e<=50})(e)&&r()),onStart:a,onMove:t=>{const e=l(t)/c.innerWidth;i(e)},onEnd:t=>{const e=l(t),o=c.innerWidth,r=e/o,a=(t=>d?-t.velocityX:t.velocityX)(t),i=a>=0&&(a>.2||e>o/2),h=(i?1-r:r)*o;let m=0;if(h>5){const t=h/Math.abs(a);m=Math.min(t,540)}s(i,r<=0?.01:n(0,r,.9999),m)}})};export{o as createSwipeBackGesture};

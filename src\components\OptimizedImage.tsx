import React, { useState, useCallback, memo } from 'react';
import { IonSpinner } from '@ionic/react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
}

// Komponen gambar yang dioptimasi untuk perangkat spesifikasi rendah
const OptimizedImage: React.FC<OptimizedImageProps> = memo(({
  src,
  alt,
  className,
  style,
  placeholder,
  onLoad,
  onError
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');

  // Lazy loading dengan intersection observer
  const imgRef = useCallback((node: HTMLImageElement | null) => {
    if (!node) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Load image ketika masuk viewport
            setImageSrc(src);
            observer.unobserve(node);
          }
        });
      },
      {
        rootMargin: '50px', // Load 50px sebelum masuk viewport
        threshold: 0.1
      }
    );

    observer.observe(node);

    return () => {
      observer.unobserve(node);
    };
  }, [src]);

  const handleLoad = useCallback(() => {
    setLoading(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setLoading(false);
    setError(true);
    onError?.();
  }, [onError]);

  return (
    <div 
      className={`optimized-image-container ${className || ''}`}
      style={{
        position: 'relative',
        display: 'inline-block',
        ...style
      }}
    >
      {loading && !error && (
        <div 
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1
          }}
        >
          <IonSpinner name="crescent" />
        </div>
      )}
      
      {error ? (
        <div 
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f0f0f0',
            color: '#666',
            minHeight: '100px',
            ...style
          }}
        >
          Gagal memuat gambar
        </div>
      ) : (
        <img
          ref={imgRef}
          src={imageSrc || placeholder || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNmMGYwZjAiLz48L3N2Zz4='}
          alt={alt}
          className={className}
          style={{
            ...style,
            opacity: loading ? 0.3 : 1,
            transition: 'opacity 0.3s ease'
          }}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy" // Native lazy loading sebagai fallback
        />
      )}
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

export default OptimizedImage;

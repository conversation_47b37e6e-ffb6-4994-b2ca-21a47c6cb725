async function a(){try{const a=JSON.parse(localStorage.getItem("user")||"{}").bidang_id;if(!a)return;const t=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]").map(a=>a.id),i=await fetch("https://absensiku.trunois.my.id/api/jam_kerja_bidang.php?api_key=absensiku_api_key_2023"),e=await i.json();if("success"===e.status&&Array.isArray(e.data)){const i=e.data.filter(i=>i.bidang_id==a&&t.includes(i.jam_kerja_id));localStorage.setItem("jam_kerja_bidang_list",JSON.stringify(i))}}catch(a){}}export{a as fetchAndStoreJamKerjaBidang};

System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(e,a){"use strict";var i,s,r,n,t,o,l,c,d,m,u,h,p,x,g,j,b,f,y,k,v,w,_,S,N,D,L,C,M,z,T,A,F;return{setters:[e=>{i=e.r,s=e.j,r=e.I,n=e.J,t=e.K,o=e.M,l=e.P,c=e.L,d=e.v,m=e.o,u=e.h,h=e.k,p=e.p,x=e.s,g=e.t,j=e.u,b=e.W,f=e.C,y=e.q,k=e.Q,v=e.N,w=e.H,_=e.w,S=e.O},e=>{N=e.r,D=e.t,L=e.d,C=e.g,M=e.h,z=e.j,T=e.c,A=e.s},e=>{F=e.u},null,null],execute:function(){var a=document.createElement("style");a.textContent=".lembur-active-card{margin:16px;border-left:4px solid var(--ion-color-danger);background:linear-gradient(135deg,var(--ion-color-primary-tint) 0%,var(--ion-color-success-shade) 100%);color:#fff}.lembur-status-header{display:flex;align-items:center;gap:12px;margin-bottom:16px}.status-icon{font-size:24px}.status-icon.active{color:var(--ion-color-light);animation:pulse 2s infinite}@keyframes pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.lembur-status-header h3{margin:0;font-size:18px;font-weight:600}.lembur-status-header p{margin:4px 0 0;font-size:14px;opacity:.9}.lembur-details{background:rgba(255,255,255,.1);padding:12px;border-radius:8px}.lembur-details p{margin:8px 0;font-size:14px}.form-header{display:flex;align-items:center;gap:12px;margin-bottom:20px;padding-bottom:12px;border-bottom:1px solid var(--ion-color-light)}.form-icon{font-size:24px;color:var(--ion-color-primary)}.form-icon.finish{color:var(--ion-color-success)}.form-header h2{margin:0;font-size:20px;font-weight:600;color:var(--ion-color-dark)}.readonly-input{background-color:var(--ion-color-light);opacity:.7}.photo-section{margin:20px 0}.photo-section ion-label{display:block;margin-bottom:12px;font-weight:500;color:var(--ion-color-dark)}.photo-preview{position:relative;display:inline-block;margin-top:8px}.photo-preview img{width:200px;height:150px;object-fit:cover;border-radius:8px;border:2px solid var(--ion-color-light)}.remove-photo-btn{position:absolute;top:-8px;right:-8px;background:var(--ion-color-danger);color:#fff;border-radius:50%;width:32px;height:32px;--padding-start: 0;--padding-end: 0}.camera-btn{margin-top:8px;--border-color: var(--ion-color-primary);--color: var(--ion-color-primary)}.submit-btn{margin-top:24px;--background: var(--ion-color-primary);font-weight:600}.submit-btn.finish{--background: var(--ion-color-success)}.submit-btn:disabled{opacity:.5}.history-item{--padding-start: 0;--padding-end: 0;--inner-padding-end: 0;margin-bottom:12px;border-bottom:1px solid var(--ion-color-light)}.history-content{width:100%;padding:12px 0}.history-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.history-header h3{margin:0;font-size:16px;font-weight:600;color:var(--ion-color-dark)}.history-content p{margin:8px 0;font-size:14px;color:var(--ion-color-medium)}.time-info{display:flex;gap:16px;flex-wrap:wrap;margin-top:8px}.time-info span{font-size:12px;color:var(--ion-color-medium);background:var(--ion-color-light);padding:4px 8px;border-radius:4px}.camera-container{display:flex;flex-direction:column;height:100%;background:#000}.camera-video{flex:1;width:100%;object-fit:cover}.camera-controls{padding:20px;background:rgba(0,0,0,.8)}.capture-btn{--background: var(--ion-color-primary);--color: white;font-weight:600}@media (max-width: 768px){.time-info{flex-direction:column;gap:8px}.time-info span{text-align:center}.photo-preview img{width:100%;max-width:300px;height:auto}}@media (prefers-color-scheme: dark){.form-header h2,.history-header h3{color:var(--ion-color-light)}.photo-section ion-label{color:var(--ion-color-light)}}.loading-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:9999}ion-card{animation:slideInUp .3s ease-out}@keyframes slideInUp{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}ion-badge{font-size:12px;font-weight:600}ion-item.item-has-error{--border-color: var(--ion-color-danger)}.error-message{color:var(--ion-color-danger);font-size:12px;margin-top:4px;margin-left:16px}\n",document.head.appendChild(a),e("default",()=>{const e=JSON.parse(localStorage.getItem("user")||"{}");F();const a=i.useRef(null),I=i.useRef(null),[O,$]=i.useState(""),[J,K]=i.useState(""),[G,U]=i.useState(""),[E,R]=i.useState(""),[P,H]=i.useState(""),[Y,B]=i.useState(!1),[W,q]=i.useState("mulai"),[Q,V]=i.useState(null),[X,Z]=i.useState(!1),[ee,ae]=i.useState(!1),[ie,se]=i.useState(""),[re,ne]=i.useState("success"),[te,oe]=i.useState(!1),[le,ce]=i.useState(""),[de,me]=i.useState(null),[ue,he]=i.useState([]),[pe,xe]=i.useState([]),[ge,je]=i.useState(!1),be=()=>{const e=new Date;return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`},fe=()=>(new Date).toTimeString().slice(0,5),ye=()=>{Q&&(Q.getTracks().forEach(e=>e.stop()),V(null))},ke=e=>{q(e),B(!0),(async()=>{try{const e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});V(e),a.current&&(a.current.srcObject=e)}catch(e){we("Gagal mengakses kamera","danger")}})()},ve=()=>{B(!1),ye()},we=(e,a="success")=>{se(e),ne(a),ae(!0)},_e=async()=>{try{je(!0);const a=encodeURIComponent(e.nama||e.name||""),i="https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023"+(a?`&nama_karyawan=${a}`:""),s=await fetch(i,{method:"GET",headers:{"x-api-key":"absensiku_api_key_2023","Content-Type":"application/json"}});if(s.ok){const a=be(),i=await s.json(),r=Array.isArray(i)?i:Array.isArray(i?.data)?i.data:Array.isArray(i?.lembur)?i.lembur:[];if(!Array.isArray(r))return we("Format data lembur tidak dikenali","danger"),me(null),void he([]);const n=r.filter(a=>!e?.nama&&!e?.name||a.nama_karyawan===(e.nama||e.name)),t=n.find(e=>{const i=(e.status||"").toLowerCase();return!(e.jam_selesai&&""!==e.jam_selesai||"pending"!==i&&"menunggu"!==i||e.tanggal_lembur!==a)});me(t||null),he(n.filter(e=>{const a=(e.status||"").toLowerCase();return"selesai"===a||"selesai"===a}));const o=n.filter(e=>{const i=(e.status||"").toLowerCase();return!(e.jam_selesai&&""!==e.jam_selesai||"pending"!==i&&"menunggu"!==i||e.tanggal_lembur===a)});xe(o),o.length>0&&we("Lembur sebelumnya melewati batas hari dan telah direset. Silakan mulai lembur baru.","warning")}else await s.text(),we("Gagal memuat data lembur","danger")}catch(a){we("Gagal memuat data lembur","danger")}finally{je(!1)}},Se=e=>e?e.slice(0,5):"--:--",Ne=e=>new Date(e).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),De=(e,a)=>{if(!e||!a)return"--:--";const i=new Date(`2000-01-01 ${e}`),s=new Date(`2000-01-01 ${a}`).getTime()-i.getTime();return`${Math.floor(s/36e5)}:${Math.floor(s%36e5/6e4).toString().padStart(2,"0")}`};return i.useEffect(()=>{_e()},[]),i.useEffect(()=>{J||de||K(fe()),U(de?fe():"")},[de]),s.jsxs(r,{children:[s.jsx(n,{children:s.jsxs(t,{children:[s.jsx(o,{slot:"start",children:s.jsx(l,{defaultHref:"/home"})}),s.jsx(c,{children:"Lembur"}),s.jsx(o,{slot:"end",children:s.jsx(d,{fill:"clear",onClick:_e,disabled:ge,children:s.jsx(m,{icon:N})})})]})}),s.jsxs(u,{fullscreen:!0,children:[de&&s.jsx(h,{className:"lembur-active-card",children:s.jsxs(p,{children:[s.jsxs("div",{className:"lembur-status-header",children:[s.jsx(m,{icon:D,className:"status-icon active"}),s.jsxs("div",{children:[s.jsx("h3",{children:"Lembur Sedang Berlangsung"}),s.jsxs("p",{children:["Dimulai: ",Se(de.jam_mulai)," - ",Ne(de.tanggal_lembur)]})]})]}),s.jsxs("div",{className:"lembur-details",children:[s.jsxs("p",{children:[s.jsx("strong",{children:"Keterangan:"})," ",de.keterangan]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Durasi:"})," ",De(de.jam_mulai,fe())]})]})]})}),!de&&s.jsx(h,{children:s.jsxs(p,{children:[s.jsxs("div",{className:"form-header",children:[s.jsx(m,{icon:L,className:"form-icon"}),s.jsx("h2",{children:"Mulai Lembur"})]}),s.jsxs(x,{children:[s.jsx(g,{position:"stacked",children:"Tanggal Lembur"}),s.jsx(j,{value:be(),readonly:!0,className:"readonly-input"})]}),s.jsxs(x,{children:[s.jsx(g,{position:"stacked",children:"Keterangan Lembur *"}),s.jsx(b,{value:O,onIonInput:e=>$(e.detail.value),placeholder:"Masukkan keterangan pekerjaan lembur...",rows:3})]}),s.jsxs(x,{children:[s.jsx(g,{position:"stacked",children:"Jam Mulai *"}),s.jsx(j,{type:"time",value:J,readonly:!0,className:"readonly-input"})]}),s.jsxs("div",{className:"photo-section",children:[s.jsx(g,{children:"Foto Mulai Lembur *"}),E?s.jsxs("div",{className:"photo-preview",children:[s.jsx("img",{src:`data:image/jpeg;base64,${E}`,alt:"Foto Mulai"}),s.jsx(d,{fill:"clear",size:"small",onClick:()=>R(""),className:"remove-photo-btn",children:s.jsx(m,{icon:C})})]}):s.jsxs(d,{expand:"block",fill:"outline",onClick:()=>ke("mulai"),className:"camera-btn",children:[s.jsx(m,{icon:M,slot:"start"}),"Ambil Foto Mulai"]})]}),s.jsx(d,{expand:"block",onClick:async()=>{if(O.trim())if(J)if(E)try{Z(!0);const a={nama_karyawan:e.nama||e.name,keterangan:O.trim(),tanggal_lembur:be(),jam_mulai:J,foto_mulai_base64:E,status:"Menunggu"},i=await fetch("https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"x-api-key":"absensiku_api_key_2023","Content-Type":"application/json"},body:JSON.stringify(a)}),s=await i.json();"success"===s.status?(we("Lembur berhasil dimulai","success"),$(""),K(""),R(""),_e()):we(s.message||"Gagal memulai lembur","danger")}catch(a){we("Terjadi kesalahan saat memulai lembur","danger")}finally{Z(!1)}else we("Foto mulai harus diambil","warning");else we("Jam mulai harus diisi","warning");else we("Keterangan harus diisi","warning")},disabled:X||!O.trim()||!J||!E,className:"submit-btn",children:X?s.jsx(f,{name:"crescent"}):"Mulai Lembur"})]})}),de&&s.jsx(h,{children:s.jsxs(p,{children:[s.jsxs("div",{className:"form-header",children:[s.jsx(m,{icon:z,className:"form-icon finish"}),s.jsx("h2",{children:"Selesai Lembur"})]}),s.jsxs(x,{children:[s.jsx(g,{position:"stacked",children:"Jam Selesai *"}),s.jsx(j,{type:"time",value:G,readonly:!0,className:"readonly-input"})]}),s.jsxs("div",{className:"photo-section",children:[s.jsx(g,{children:"Foto Selesai Lembur *"}),P?s.jsxs("div",{className:"photo-preview",children:[s.jsx("img",{src:`data:image/jpeg;base64,${P}`,alt:"Foto Selesai"}),s.jsx(d,{fill:"clear",size:"small",onClick:()=>H(""),className:"remove-photo-btn",children:s.jsx(m,{icon:C})})]}):s.jsxs(d,{expand:"block",fill:"outline",onClick:()=>ke("selesai"),className:"camera-btn",children:[s.jsx(m,{icon:M,slot:"start"}),"Ambil Foto Selesai"]})]}),s.jsx(d,{expand:"block",onClick:async()=>{if(de)if(G)if(P)try{Z(!0);const e={id:de.id,jam_selesai:G,foto_selesai_base64:P,status:"Selesai"},a=await fetch("https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023",{method:"PUT",headers:{"x-api-key":"absensiku_api_key_2023","Content-Type":"application/json"},body:JSON.stringify(e)}),i=await a.json();"success"===i.status?(we("Lembur berhasil diselesaikan","success"),U(""),H(""),_e()):we(i.message||"Gagal menyelesaikan lembur","danger")}catch(e){we("Terjadi kesalahan saat menyelesaikan lembur","danger")}finally{Z(!1)}else we("Foto selesai harus diambil","warning");else we("Jam selesai harus diisi","warning");else we("Tidak ada lembur aktif","warning")},disabled:X||!G||!P,className:"submit-btn finish",children:X?s.jsx(f,{name:"crescent"}):"Selesai Lembur"})]})}),ue.length>0&&s.jsx(h,{children:s.jsxs(p,{children:[s.jsxs("div",{className:"form-header",children:[s.jsx(m,{icon:T,className:"form-icon"}),s.jsx("h2",{children:"Riwayat Lembur"})]}),s.jsx(y,{children:ue.map((e,a)=>s.jsx(x,{className:"history-item",children:s.jsxs("div",{className:"history-content",children:[s.jsxs("div",{className:"history-header",children:[s.jsx("h3",{children:Ne(e.tanggal_lembur)}),s.jsx(k,{color:"success",children:e.status})]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Keterangan:"})," ",e.keterangan]}),s.jsxs("div",{className:"time-info",children:[s.jsxs("span",{children:["Mulai: ",Se(e.jam_mulai)]}),s.jsxs("span",{children:["Selesai: ",Se(e.jam_selesai||"")]}),s.jsxs("span",{children:["Durasi: ",De(e.jam_mulai,e.jam_selesai||"")]})]})]})},a))})]})}),pe.length>0&&s.jsx(h,{children:s.jsxs(p,{children:[s.jsxs("div",{className:"form-header",children:[s.jsx(m,{icon:A,className:"form-icon"}),s.jsx("h2",{children:"Lembur Belum Diselesaikan"})]}),s.jsx(y,{children:pe.map((e,a)=>s.jsx(x,{className:"history-item",children:s.jsxs("div",{className:"history-content",children:[s.jsxs("div",{className:"history-header",children:[s.jsx("h3",{children:Ne(e.tanggal_lembur)}),s.jsx(k,{color:"warning",children:"Menunggu"})]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Keterangan:"})," ",e.keterangan]}),s.jsxs("div",{className:"time-info",children:[s.jsxs("span",{children:["Mulai: ",Se(e.jam_mulai)]}),s.jsx("span",{children:"Selesai: --:--"}),s.jsx("span",{children:"Durasi: --:--"})]}),s.jsx(v,{color:"danger",children:"Anda tidak melakukan update selesai pada lembur"})]})},a))})]})}),s.jsxs(w,{isOpen:Y,onDidDismiss:ve,children:[s.jsx(n,{children:s.jsxs(t,{children:[s.jsxs(c,{children:["Ambil Foto ","mulai"===W?"Mulai":"Selesai"]}),s.jsx(o,{slot:"end",children:s.jsx(d,{fill:"clear",onClick:ve,children:s.jsx(m,{icon:C})})})]})}),s.jsx(u,{children:s.jsxs("div",{className:"camera-container",children:[s.jsx("video",{ref:a,autoPlay:!0,playsInline:!0,className:"camera-video"}),s.jsx("canvas",{ref:I,style:{display:"none"}}),s.jsx("div",{className:"camera-controls",children:s.jsxs(d,{expand:"block",onClick:()=>{if(a.current&&I.current){const e=I.current,i=a.current,s=e.getContext("2d");if(e.width=i.videoWidth,e.height=i.videoHeight,s){s.drawImage(i,0,0);const a=e.toDataURL("image/jpeg",.8).split(",")[1];"mulai"===W?R(a):H(a),B(!1),ye(),we("Foto berhasil diambil","success")}}},className:"capture-btn",children:[s.jsx(m,{icon:M,slot:"start"}),"Ambil Foto"]})})]})})]}),s.jsx(_,{isOpen:ee,onDidDismiss:()=>ae(!1),message:ie,duration:3e3,color:re}),s.jsx(S,{isOpen:te,onDidDismiss:()=>oe(!1),header:"Informasi",message:le,buttons:["OK"]})]})]})})}}});

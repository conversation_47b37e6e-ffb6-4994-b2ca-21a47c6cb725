import{ab as e,ac as t,ad as o,ae as n,af as r}from"./ionic-CJlrxXsE.js";import"./react-vendor-DCX9i6UF.js";
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const s=()=>{const s=window;s.addEventListener("statusTap",()=>{e(()=>{const e=s.innerWidth,a=s.innerHeight,i=document.elementFromPoint(e/2,a/2);if(!i)return;const c=t(i);c&&new Promise(e=>o(c,e)).then(()=>{n(async()=>{c.style.setProperty("--overflow","hidden"),await r(c,300),c.style.removeProperty("--overflow")})})})})};export{s as startStatusTap};

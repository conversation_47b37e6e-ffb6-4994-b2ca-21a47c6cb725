System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./utils-legacy-DvNNcox0.js","./react-vendor-legacy-wCcNgjsd.js","./capacitor-legacy-cVgeOc-7.js"],function(a,e){"use strict";var t,n,r,s,i,o,c,l,d,p,u,g,m,h,x,f,b,y,j,w,k,v,S,_,N,R,A,I,z,D,B,T,O;return{setters:[a=>{t=a.r,n=a.j,r=a.I,s=a.J,i=a.K,o=a.M,c=a.P,l=a.L,d=a.v,p=a.o,u=a.h,g=a.E,m=a.F,h=a.C,x=a.N,f=a.k,b=a.m,y=a.n,j=a.Q,w=a.p,k=a.s,v=a.t,S=a.O,_=a.w,N=a.H},a=>{R=a.v,A=a.p,I=a.c,z=a.t,D=a.i,B=a.f},a=>{T=a.B,O=a.N},null,null],execute:function(){var e=document.createElement("style");e.textContent='ion-content.rapat-content{--background: linear-gradient(135deg, #f0fdfa 0%, #e0e7ff 100%)}.rapat-container{padding:16px 16px 32px;max-width:420px;margin:0 auto}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px;gap:16px}.empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;height:300px;text-align:center;color:var(--ion-color-medium);gap:16px}.empty-state ion-icon{font-size:64px;opacity:.5}.empty-state h3{margin:0;font-size:1.2rem;font-weight:600}.empty-state p{margin:0;font-size:.9rem}.rapat-card{margin-bottom:16px;border-radius:18px;box-shadow:0 4px 24px rgba(0,0,0,.07);background:#fff;border:none}.rapat-header{display:flex;justify-content:space-between;align-items:flex-start;gap:12px}.badge-container{display:flex;flex-direction:column;gap:6px;align-items:flex-end}.rapat-header ion-card-title{flex:1;font-size:1.1rem;font-weight:600;line-height:1.3}.status-badge{flex-shrink:0;font-size:.85rem;padding:6px 12px;border-radius:12px;font-weight:600}.rapat-details{margin-top:8px}.detail-item{--padding-start: 0;--padding-end: 0;--inner-padding-end: 0;margin-bottom:8px}.detail-item ion-icon{color:var(--ion-color-primary);margin-right:12px;font-size:1.1rem}.detail-item ion-label h3{font-size:.85rem;font-weight:600;color:var(--ion-color-medium);margin:0 0 2px}.detail-item ion-label p{font-size:.95rem;color:var(--ion-color-dark);margin:0;font-weight:500}.scan-button{margin-top:16px;--border-radius: 12px;font-weight:600;--background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);--color: white;height:48px;font-size:1rem}.not-registered{margin-top:16px;text-align:center;font-style:italic}.not-registered p{margin:0;font-size:.9rem}.scanner-modal-content{--background: #f5f5f5}.scanner-container{padding:20px;display:flex;flex-direction:column;align-items:center;height:100%;justify-content:space-between}.scanner-preview{width:100%;max-width:350px;height:350px;background:#000;border-radius:16px;position:relative;overflow:hidden;margin:20px 0;display:flex;align-items:center;justify-content:center}.scanner-video{width:100%;height:100%;object-fit:cover;border-radius:16px}.scanner-canvas{position:absolute;top:0;left:0}.scanner-frame{width:280px;height:280px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);border:2px solid rgba(255,255,255,.5);border-radius:12px;z-index:10}.scanner-corners{position:absolute;top:0;left:0;right:0;bottom:0}.corner{position:absolute;width:30px;height:30px;border:3px solid #00ff00}.corner.top-left{top:10px;left:10px;border-right:none;border-bottom:none;border-radius:8px 0 0}.corner.top-right{top:10px;right:10px;border-left:none;border-bottom:none;border-radius:0 8px 0 0}.corner.bottom-left{bottom:10px;left:10px;border-right:none;border-top:none;border-radius:0 0 0 8px}.corner.bottom-right{bottom:10px;right:10px;border-left:none;border-top:none;border-radius:0 0 8px}.scanner-instructions{text-align:center;margin:20px 0;padding:0 20px}.scanner-instructions h3{color:#333;margin-bottom:8px;font-size:18px;font-weight:600}.scanner-instructions p{color:#666;font-size:14px;margin:0}.scanner-actions{width:100%;max-width:300px;display:flex;flex-direction:column;gap:12px;margin-top:auto;padding-bottom:20px}.scanner-modal-content.scanner-active{--background: #f5f5f5}.scanner-modal-content.scanner-active .scanner-preview{background:transparent!important}.scanner-modal-content.scanner-active .scanner-instructions,.scanner-modal-content.scanner-active .scanner-actions{opacity:.7}.scanner-preview.scanning{position:relative;overflow:hidden}.scanner-preview.scanning:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:transparent;z-index:1}.scanning-indicator{position:absolute;top:0;left:0;right:0;bottom:0;z-index:2}.scan-line{position:absolute;left:20px;right:20px;height:2px;background:linear-gradient(90deg,transparent,#00ff00,transparent);animation:scan 2s linear infinite}@keyframes scan{0%{top:20px;opacity:1}50%{opacity:1}to{top:calc(100% - 22px);opacity:0}}.scanner-preview.scanning .corner{border-color:#0f0;animation:pulse 1.5s ease-in-out infinite}@keyframes pulse{0%,to{opacity:1}50%{opacity:.5}}@media (prefers-color-scheme: dark){.rapat-card{box-shadow:0 2px 8px rgba(0,0,0,.3)}.detail-item ion-label p{color:var(--ion-color-light)}}@media (max-width: 768px){.rapat-container{padding:12px}.rapat-card{margin-bottom:12px}.rapat-header{flex-direction:column;align-items:flex-start;gap:8px}.status-badge{align-self:flex-start}}.loading-container ion-spinner{--color: var(--ion-color-primary)}.scan-button:hover{transform:translateY(-1px);transition:transform .2s ease}ion-badge[color=success]{--background: var(--ion-color-success);--color: white}ion-badge[color=medium]{--background: var(--ion-color-medium);--color: white}.rapat-card{transition:transform .2s ease,box-shadow .2s ease}.rapat-card:hover{transform:translateY(-2px);box-shadow:0 4px 16px rgba(0,0,0,.15)}ion-refresher-content{--color: var(--ion-color-primary)}ion-header ion-toolbar{--background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);--color: white}ion-header ion-title{font-weight:600;font-size:1.2rem}ion-header ion-button{--color: white}.loading-container,.empty-state{background:rgba(255,255,255,.9);border-radius:12px;margin:20px;padding:40px 20px}ion-toast{--border-radius: 12px}ion-alert{--border-radius: 12px}.active-rapat{border-left:4px solid var(--ion-color-warning);box-shadow:0 4px 24px rgba(245,158,11,.15)}.active-rapat ion-card-title{color:var(--ion-color-warning-shade)}ion-badge[color=warning]{--background: var(--ion-color-warning);--color: white;animation:pulse 2s infinite}@keyframes pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}\n',document.head.appendChild(e);const $="https://absensiku.trunois.my.id/api",P="absensiku_api_key_2023";class L{static async getAllRapat(){try{const a=await fetch(`${$}/api_rapat.php?api_key=${P}`),e=await a.json();return"success"===e.status&&Array.isArray(e.data)?e.data:[]}catch(a){return[]}}static async getRapatById(a){try{const e=await fetch(`${$}/api_rapat.php?api_key=${P}&id=${a}`),t=await e.json();return"success"===t.status&&Array.isArray(t.data)&&t.data.length>0?t.data[0]:null}catch(e){return null}}static async getAllRapatPeserta(){try{const a=await fetch(`${$}/api_rapat_peserta.php?api_key=${P}`),e=await a.json();return"success"===e.status&&Array.isArray(e.data)?e.data:[]}catch(a){return[]}}static async getRapatPesertaByUserId(a){try{const e=await fetch(`${$}/api_rapat_peserta.php?api_key=${P}`),t=await e.json();return"success"===t.status&&Array.isArray(t.data)?t.data.filter(e=>e.user_id==a):[]}catch(e){return[]}}static async getRapatPesertaByRapatId(a){try{const e=await fetch(`${$}/api_rapat_peserta.php?api_key=${P}&rapat_id=${a}`),t=await e.json();return"success"===t.status&&Array.isArray(t.data)?t.data:[]}catch(e){return[]}}static async updateStatusKehadiran(a,e,t,n){try{const s=n||(new Date).toISOString().slice(0,19).replace("T"," "),i={api_key:P,rapat_id:a,user_id:e,status:t,waktu_hadir:s},o=await fetch(`${$}/api_rapat_peserta.php`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!o.ok)return await o.text(),!1;const c=await o.text();try{const a=JSON.parse(c);return"success"===a.status?(localStorage.removeItem("rapat_peserta_cache"),!0):"warning"===a.status}catch(r){return!1}}catch(s){return!1}}static async getRapatByBarcode(a){try{const e=await this.getAllRapat();return e.find(e=>e.barcode_value===a)||null}catch(e){return null}}static async isUserRegisteredForRapat(a,e){try{const t=await this.getRapatPesertaByRapatId(e);return t.find(e=>e.user_id==a)||null}catch(t){return null}}static async processRapatAbsensi(a,e){try{const t=await this.getRapatByBarcode(a);if(!t)return{success:!1,message:"Barcode rapat tidak ditemukan"};const n=await this.isUserRegisteredForRapat(e,t.id);return n?"hadir"===n.status?{success:!1,message:"Anda sudah melakukan absensi untuk rapat ini"}:await this.updateStatusKehadiran(t.id,e,"hadir")?{success:!0,message:`Berhasil absen untuk rapat: ${t.judul}`,rapat:t}:{success:!1,message:"Gagal mengupdate status kehadiran. Silakan coba lagi."}:{success:!1,message:"Anda tidak terdaftar sebagai peserta rapat ini"}}catch(t){return{success:!1,message:"Terjadi kesalahan saat memproses absensi: "+t}}}static formatTanggal(a){return new Date(a).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}static formatWaktu(a){return a.substring(0,5)}static isRapatActive(a){const e=new Date,t=new Date(a.tanggal),[n,r]=a.waktu_mulai.split(":").map(Number),[s,i]=a.waktu_selesai.split(":").map(Number),o=new Date(t);o.setHours(n,r,0,0);const c=new Date(t);return c.setHours(s,i,0,0),e>=o&&e<=c}static async saveRapatToLocalStorage(){try{const a=await this.getAllRapat(),e=await this.getAllRapatPeserta();localStorage.setItem("rapat_list",JSON.stringify(a)),localStorage.setItem("rapat_peserta_list",JSON.stringify(e)),localStorage.setItem("rapat_last_sync",(new Date).toISOString())}catch(a){}}static getRapatFromLocalStorage(){try{const a=JSON.parse(localStorage.getItem("rapat_list")||"[]"),e=JSON.parse(localStorage.getItem("rapat_peserta_list")||"[]");return{rapatList:a,rapatPesertaList:e,lastSync:localStorage.getItem("rapat_last_sync")}}catch(a){return{rapatList:[],rapatPesertaList:[],lastSync:null}}}}a("default",()=>{const a=JSON.parse(localStorage.getItem("user")||"{}"),[e,$]=t.useState([]),[P,C]=t.useState([]),[J,H]=t.useState(!1),[E,F]=t.useState(!1),[K,U]=t.useState(!1),W=t.useRef(null),[G,M]=t.useState(null),[Y,Q]=t.useState(null),[V,q]=t.useState(!1),[X,Z]=t.useState(""),[aa,ea]=t.useState(!1),[ta,na]=t.useState(""),[ra,sa]=t.useState("success"),[ia,oa]=t.useState(!1),ca=async()=>{H(!0);try{const a=await L.getAllRapat();$(a)}catch(a){$([])}finally{H(!1)}},la=async()=>{try{const e=a.id||a.nik,t=await L.getRapatPesertaByUserId(e);C(t)}catch(e){C([])}},da=a=>L.formatWaktu(a),pa=async()=>{try{U(!0)}catch(a){na("Gagal membuka scanner"),sa("danger"),ea(!0)}};t.useEffect(()=>{const a=new T;return Q(a),()=>{a.reset()}},[]),t.useEffect(()=>(K&&Y?ua():ga(),()=>{ga()}),[K,Y]);const ua=async()=>{if(Y&&W.current)try{F(!0);const a=await Y.decodeOnceFromVideoDevice(void 0,W.current);a&&(F(!1),await xa(a.getText()),U(!1))}catch(a){a instanceof O?E&&setTimeout(()=>{ua()},100):(na("Gagal melakukan scan barcode"),sa("danger"),ea(!0),F(!1))}},ga=()=>{Y&&Y.reset(),F(!1)},ma=()=>{U(!1),ga()},ha=a=>{const e=new Date,t=e.toISOString().split("T")[0];return!!(a.tanggal&&a.tanggal<t)||!(a.tanggal&&a.tanggal>t)&&(!!a.waktu_selesai&&e>new Date(`${t}T${a.waktu_selesai}`))},xa=async e=>{try{const t=a.id||a.nik,n=await L.processRapatAbsensi(e,t);n.success?(na(n.message),sa("success"),ea(!0),localStorage.removeItem("rapat_peserta_cache"),setTimeout(async()=>{await la(),C(a=>[...a])},1e3)):(na(n.message),sa(n.message.includes("tidak terdaftar")?"warning":"danger"),ea(!0))}catch(t){na("Terjadi kesalahan saat memproses barcode"),sa("danger"),ea(!0)}};return t.useEffect(()=>{ca(),la()},[]),n.jsxs(r,{children:[n.jsx(s,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:"80px"},children:n.jsxs(i,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[n.jsx(o,{slot:"start",children:n.jsx(c,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),n.jsx(l,{style:{color:"#fff",fontWeight:"600",fontSize:"1.2rem"},children:"Rapat"}),n.jsx(o,{slot:"end",children:n.jsx(d,{onClick:pa,disabled:E,style:{color:"#fff"},children:n.jsx(p,{icon:R})})})]})}),n.jsxs(u,{fullscreen:!0,className:"rapat-content",children:[n.jsx(g,{slot:"fixed",onIonRefresh:async a=>{oa(!0);try{await Promise.all([ca(),la()]),await L.saveRapatToLocalStorage()}catch(e){}finally{oa(!1),a.detail.complete()}},children:n.jsx(m,{})}),J?n.jsxs("div",{className:"loading-container",children:[n.jsx(h,{name:"crescent"}),n.jsx(x,{children:"Memuat data rapat..."})]}):n.jsx("div",{className:"rapat-container",children:0===e.length?n.jsxs("div",{className:"empty-state",children:[n.jsx(p,{icon:A,size:"large"}),n.jsxs(x,{children:[n.jsx("h3",{children:"Tidak ada rapat"}),n.jsx("p",{children:"Belum ada rapat yang tersedia saat ini"})]})]}):e.filter(e=>{const t=a.id||a.nik;return P.some(a=>a.rapat_id==e.id&&a.user_id==t)}).map(e=>{const t=a.id||a.nik,r=(e=>{const t=a.id||a.nik,n=P.find(a=>a.rapat_id==e&&a.user_id==t);return n?n.status:null})(e.id),s=P.some(a=>a.rapat_id==e.id&&a.user_id==t),i=L.isRapatActive(e);return n.jsxs(f,{className:"rapat-card "+(i?"active-rapat":""),children:[n.jsx(b,{children:n.jsxs("div",{className:"rapat-header",children:[n.jsx(y,{children:e.judul}),n.jsxs("div",{className:"badge-container",children:[i&&n.jsx(j,{color:"warning",className:"status-badge",children:"Sedang Berlangsung"}),s&&n.jsx(j,{color:"hadir"===r?"success":"medium",className:"status-badge",children:"hadir"===r?"Hadir":"Belum Absen"})]})]})}),n.jsxs(w,{children:[n.jsxs("div",{className:"rapat-details",children:[n.jsxs(k,{lines:"none",className:"detail-item",children:[n.jsx(p,{icon:I,slot:"start"}),n.jsxs(v,{children:[n.jsx("h3",{children:"Tanggal"}),n.jsx("p",{children:(o=e.tanggal,L.formatTanggal(o))})]})]}),n.jsxs(k,{lines:"none",className:"detail-item",children:[n.jsx(p,{icon:z,slot:"start"}),n.jsxs(v,{children:[n.jsx("h3",{children:"Waktu"}),n.jsxs("p",{children:[da(e.waktu_mulai)," - ",da(e.waktu_selesai)]})]})]}),n.jsxs(k,{lines:"none",className:"detail-item",children:[n.jsx(p,{icon:D,slot:"start"}),n.jsxs(v,{children:[n.jsx("h3",{children:"Lokasi"}),n.jsx("p",{children:e.lokasi})]})]}),e.deskripsi&&n.jsx(k,{lines:"none",className:"detail-item",children:n.jsxs(v,{children:[n.jsx("h3",{children:"Deskripsi"}),n.jsx("p",{children:e.deskripsi})]})})]}),s&&"hadir"!==r&&!ha(e)&&n.jsxs(d,{expand:"block",onClick:pa,disabled:E,className:"scan-button",children:[n.jsx(p,{icon:R,slot:"start"}),E?"Scanning...":"Scan Barcode Absensi"]}),s&&"hadir"!==r&&ha(e)&&n.jsx(x,{color:"medium",className:"time-expired",children:n.jsx("p",{children:"Waktu absensi telah berakhir"})})]})]},e.id);var o})}),n.jsx(S,{isOpen:V,onDidDismiss:()=>q(!1),header:"Informasi",message:X,buttons:["OK"]}),n.jsx(_,{isOpen:aa,onDidDismiss:()=>ea(!1),message:ta,duration:3e3,color:ra})]}),n.jsxs(N,{isOpen:K,onDidDismiss:ma,children:[n.jsx(s,{children:n.jsxs(i,{children:[n.jsx(l,{children:"Scan Barcode Rapat"}),n.jsx(o,{slot:"end",children:n.jsx(d,{onClick:ma,children:n.jsx(p,{icon:B})})})]})}),n.jsx(u,{className:"scanner-modal-content "+(E?"scanner-active":""),children:n.jsxs("div",{className:"scanner-container",children:[n.jsxs("div",{className:"scanner-preview "+(E?"scanning":""),id:"scanner-preview",children:[n.jsx("video",{ref:W,className:"scanner-video",autoPlay:!0,playsInline:!0,muted:!0}),n.jsxs("div",{className:"scanner-frame",children:[n.jsxs("div",{className:"scanner-corners",children:[n.jsx("div",{className:"corner top-left"}),n.jsx("div",{className:"corner top-right"}),n.jsx("div",{className:"corner bottom-left"}),n.jsx("div",{className:"corner bottom-right"})]}),E&&n.jsx("div",{className:"scanning-indicator",children:n.jsx("div",{className:"scan-line"})})]})]}),n.jsx("div",{className:"scanner-instructions",children:n.jsxs(x,{children:[n.jsx("h3",{children:E?"Scanning...":"Arahkan kamera ke barcode rapat"}),n.jsx("p",{children:E?"Pastikan barcode berada di dalam kotak dan terlihat jelas":"Kamera akan aktif secara otomatis setelah menekan tombol mulai scan"})]})}),n.jsxs("div",{className:"scanner-actions",children:[!E&&n.jsxs(d,{expand:"block",onClick:()=>{ua()},color:"primary",children:[n.jsx(p,{icon:R,slot:"start"}),"Mulai Scan"]}),n.jsxs(d,{expand:"block",fill:"outline",onClick:ma,color:"medium",children:[n.jsx(p,{icon:B,slot:"start"}),"Tutup Scanner"]})]})]})})]})]})})}}});

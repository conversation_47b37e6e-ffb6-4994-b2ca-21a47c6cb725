<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\absensi\absensipdam\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\absensi\absensipdam\android\app\src\main\res"><file name="ic_launcher_background" path="E:\absensi\absensipdam\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable\splash.png" qualifiers="" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-land-hdpi\splash.png" qualifiers="land-hdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-land-mdpi\splash.png" qualifiers="land-mdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-land-xhdpi\splash.png" qualifiers="land-xhdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-land-xxhdpi\splash.png" qualifiers="land-xxhdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-land-xxxhdpi\splash.png" qualifiers="land-xxxhdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-port-hdpi\splash.png" qualifiers="port-hdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-port-mdpi\splash.png" qualifiers="port-mdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-port-xhdpi\splash.png" qualifiers="port-xhdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-port-xxhdpi\splash.png" qualifiers="port-xxhdpi-v4" type="drawable"/><file name="splash" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-port-xxxhdpi\splash.png" qualifiers="port-xxxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="E:\absensi\absensipdam\android\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_main" path="E:\absensi\absensipdam\android\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\absensi\absensipdam\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="masuk" path="E:\absensi\absensipdam\android\app\src\main\res\raw\masuk.mp3" qualifiers="" type="raw"/><file name="pulang" path="E:\absensi\absensipdam\android\app\src\main\res\raw\pulang.mp3" qualifiers="" type="raw"/><file path="E:\absensi\absensipdam\android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="E:\absensi\absensipdam\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Absensi PDAM</string><string name="title_activity_main">Absensi PDAM</string><string name="package_name">com.absensi.pdam</string><string name="custom_url_scheme">com.absensi.pdam</string></file><file path="E:\absensi\absensipdam\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
    </style><style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:background">@drawable/splash</item>
    </style></file><file name="config" path="E:\absensi\absensipdam\android\app\src\main\res\xml\config.xml" qualifiers="" type="xml"/><file name="file_paths" path="E:\absensi\absensipdam\android\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\absensi\absensipdam\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\absensi\absensipdam\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\absensi\absensipdam\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\absensi\absensipdam\android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
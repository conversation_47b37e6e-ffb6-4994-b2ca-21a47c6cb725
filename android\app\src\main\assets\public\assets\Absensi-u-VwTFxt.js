import{r as a,D as e,j as t,I as n,J as i,K as s,M as r,P as o,L as l,h as d,N as c,v as u,o as m,x as g}from"./ionic-CJlrxXsE.js";import{i as p,h as f,r as k,j as h,k as b,m as j,c as x}from"./index-BZ7jmVXp.js";import{G as _}from"./capacitor-DGgumwVn.js";import{S as y}from"./utils-W2Gk7u7g.js";import{fetchAndStoreJamKerja as S}from"./jamKerja-odpqnhzg.js";import{fetchAndStoreJamKerjaBidang as w}from"./jamKerjaBidang-UCleSGcG.js";import"./react-vendor-DCX9i6UF.js";import"./networkOptimizer-CUziElFO.js";const v=new y;v.create();const O={background:"linear-gradient(135deg, #1880ff 60%, #005be7 100%)",boxShadow:"0 4px 18px rgba(24, 128, 255, 0)",borderBottomLeftRadius:32,borderBottomRightRadius:32,minHeight:80,padding:"0 0 8px 0"},I={fontSize:"1.5rem",fontWeight:700,letterSpacing:.5,color:"#fff",textShadow:"0 2px 8px rgba(24,128,255,0.13)"};function D(){return["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][(new Date).getDay()]}function N(a=new Date){const e=a.getFullYear(),t=String(a.getMonth()+1).padStart(2,"0"),n=String(a.getDate()).padStart(2,"0");return"".concat(e,"-").concat(t,"-").concat(n)}function A(a,e,t){const n=a=>a*Math.PI/180,i=n(a),s=n(Number(t.latitude)),r=n(Number(t.latitude)-a),o=n(Number(t.longitude)-e),l=Math.sin(r/2)*Math.sin(r/2)+Math.cos(i)*Math.cos(s)*Math.sin(o/2)*Math.sin(o/2);return 6371e3*(2*Math.atan2(Math.sqrt(l),Math.sqrt(1-l)))<=Number(t.radius)}const J=()=>{const y=JSON.parse(localStorage.getItem("user")||"{}"),J=1===(null==y?void 0:y.allow_flexible_schedule)||"1"===(null==y?void 0:y.allow_flexible_schedule)||!0===(null==y?void 0:y.allow_flexible_schedule),M=a.useRef(null),R=a.useRef(null),[L,T]=a.useState(null),[W,C]=a.useState(""),[E,q]=a.useState(!1),[z,P]=a.useState(""),[B,G]=a.useState(""),[H]=e(),{getLocation:F}=function(){const[e,t]=a.useState(null),[n,i]=a.useState(!1),[s,r]=a.useState(null);return{coords:e,loading:n,error:s,getLocation:async()=>{i(!0),r(null);try{if("granted"!==(await _.checkPermissions()).location&&"granted"!==(await _.requestPermissions()).location)throw new Error("Izin lokasi ditolak");const a=await _.getCurrentPosition({enableHighAccuracy:!0}),e=a.coords.latitude,n=a.coords.longitude;t({lat:e,lng:n});const i={lat:e,lng:n,timestamp:Date.now()};if(await v.set("last_gps",i),!navigator.onLine){let a=await v.get("gps_queue")||[];a.push(i),await v.set("gps_queue",a)}return i}catch(a){throw r(a.message||"Gagal mendapatkan lokasi"),a}finally{i(!1)}},syncData:async()=>{if(!navigator.onLine)return;const a=await v.get("gps_queue")||[];for(const t of a)try{await fetch("https://your-server.com/api/sync_gps",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})}catch(e){continue}await v.set("gps_queue",[])}}}(),[K,U]=a.useState(!1),[X,Y]=a.useState(!1),[Q,V]=a.useState(!1),[Z,$]=a.useState(navigator.onLine),[aa,ea]=a.useState(0),[ta,na]=a.useState(""),[ia,sa]=a.useState(!1),[ra,oa]=a.useState(!0),[la,da]=a.useState(0),ca=JSON.parse(localStorage.getItem("lokasi_list")||"[]")[0],ua=()=>{try{const a=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]");ea(a.length)}catch(a){}},ma=a=>{try{const e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),t={...a,id:Date.now().toString()+"_"+Math.random().toString(36).substring(2,11),timestamp:(new Date).toISOString()};return e.push(t),localStorage.setItem("offline_absensi_queue",JSON.stringify(e)),ua(),t}catch(e){return null}},[ga,pa]=a.useState(!1),[fa,ka]=a.useState("masuk"),ha=a=>{const e={jenis:a,tanggal:N(),timestamp:(new Date).toISOString()};localStorage.setItem("status_absen",JSON.stringify(e))},ba=()=>{try{const a=N(),e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]").filter(e=>e.tanggal===a),t=JSON.parse(localStorage.getItem("absensi_backup")||"[]").filter(e=>e.tanggal===a),n=[...e,...t],i=n.some(a=>"masuk"===a.jenisAbsensi||a.jam_masuk),s=n.some(a=>"pulang"===a.jenisAbsensi||a.jam_pulang);return i&&!s?"pulang":"masuk"}catch(a){return"masuk"}},ja=()=>{N(),JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),JSON.parse(localStorage.getItem("absensi_backup")||"[]"),localStorage.getItem("status_absen")};a.useEffect(()=>(window.debugStatusAbsen=ja,()=>{delete window.debugStatusAbsen}),[fa,Z]),a.useEffect(()=>{const a=()=>{if($(!0),y.id||y.nik){(async()=>{try{const a=N(),e=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(y.id||y.nik,"&tanggal=").concat(a)),t=await e.json();if("success"===t.status&&t.data.length>0){const a=t.data[0];a.jam_masuk&&!a.jam_pulang?(ka("pulang"),ha("masuk")):a.jam_masuk&&a.jam_pulang&&(ka("masuk"),ha("pulang"))}}catch(a){}})()}},e=()=>{$(!1);const a=ba();ka(a)};if(window.addEventListener("online",a),window.addEventListener("offline",e),ua(),(()=>{try{const a=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),e=new Date;e.setDate(e.getDate()-7);const t=a.filter(a=>new Date(a.timestamp)>e);t.length<a.length&&(localStorage.setItem("offline_absensi_queue",JSON.stringify(t)),ua())}catch(a){}})(),!navigator.onLine&&(y.id||y.nik)){const a=ba();ka(a)}return()=>{window.removeEventListener("online",a),window.removeEventListener("offline",e)}},[y]),a.useEffect(()=>{(y.id||y.nik)&&(async()=>{try{if(navigator.onLine){const a=N(),e=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(y.id||y.nik,"&tanggal=").concat(a)),t=await e.json();if("success"===t.status&&t.data.length>0){const a=t.data[0];a.jam_masuk&&!a.jam_pulang?(ka("pulang"),ha("masuk")):a.jam_masuk&&a.jam_pulang?(ka("masuk"),ha("pulang")):ka("masuk")}else ka("masuk")}else{const a=ba();ka(a)}}catch(a){const e=ba();ka(e)}})()},[y]),a.useEffect(()=>{ca&&!ga&&(async()=>{if(!ga&&ca){G("Mengecek lokasi...");try{const a=await F();A(a.lat,a.lng,ca)?(G("Lokasi valid, dalam radius."),U(!0),Y(!1),H({message:"Lokasi valid, dalam radius.",color:"success",duration:2e3,position:"top"})):(G("Anda di luar radius lokasi!"),U(!1),Y(!0),H({message:"Anda di luar radius lokasi!",color:"danger",duration:2e3,position:"top"})),pa(!0)}catch(a){G("Gagal mendapatkan lokasi"),U(!1),Y(!0),H({message:"Gagal mendapatkan lokasi",color:"danger",duration:2e3,position:"top"}),pa(!0)}}})()},[ca,ga]);const xa=async()=>{C(""),T(null);try{const a=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});M.current&&(M.current.srcObject=a)}catch(a){C("Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.")}},_a=()=>{if(M.current&&M.current.srcObject){M.current.srcObject.getTracks().forEach(a=>a.stop()),M.current.srcObject=null}},ya=a=>{if(J)return{valid:!0,error:"",info:"Mode fleksibel aktif: Anda dapat absen kapan saja."};const e=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),t=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),n=D(),i=t.find(a=>a.hari===n&&a.jam_kerja_id);if(!i||!i.jam_kerja_id)return{valid:!1,error:"Tidak ada jadwal jam kerja untuk hari ini.",info:""};const s=e.find(a=>a.id==i.jam_kerja_id);if(!s)return{valid:!1,error:"Data jam kerja tidak ditemukan.",info:""};const r=new Date,o=a=>a.toString().padStart(2,"0"),l=o(r.getHours())+":"+o(r.getMinutes()),d="Jam kerja: ".concat(s.awal_jam_masuk," - ").concat(s.akhir_jam_pulang);if("masuk"===a){if(!s.awal_jam_masuk||!s.akhir_jam_masuk)return{valid:!1,error:"Data jam masuk tidak lengkap.",info:d};if(l<s.awal_jam_masuk)return{valid:!1,error:"Absen masuk belum bisa dilakukan. Waktu absen masuk: ".concat(s.awal_jam_masuk," - ").concat(s.akhir_jam_masuk),info:d};if(l>s.akhir_jam_masuk)return{valid:!1,error:"Waktu absen masuk sudah berakhir. Waktu absen masuk: ".concat(s.awal_jam_masuk," - ").concat(s.akhir_jam_masuk),info:d}}else{if(!s.jam_pulang||!s.akhir_jam_pulang)return{valid:!1,error:"Data jam pulang tidak lengkap.",info:d};if(l<s.jam_pulang)return{valid:!1,error:"Absen pulang belum bisa dilakukan. Waktu absen pulang: ".concat(s.jam_pulang," - ").concat(s.akhir_jam_pulang),info:d};if(l>s.akhir_jam_pulang)return{valid:!1,error:"Waktu absen pulang sudah berakhir. Waktu absen pulang: ".concat(s.jam_pulang," - ").concat(s.akhir_jam_pulang),info:d}}return{valid:!0,error:"",info:d}},Sa=a=>{if(J)return"Fleksibel (kapan saja)";const e=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),t=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),n=D(),i=t.find(a=>a.hari===n&&a.jam_kerja_id);if(!i||!i.jam_kerja_id)return"Data tidak tersedia";const s=e.find(a=>a.id==i.jam_kerja_id);return s?"masuk"===a?"".concat(s.awal_jam_masuk||"--:--"," - ").concat(s.akhir_jam_masuk||"--:--"):"".concat(s.jam_pulang||"--:--"," - ").concat(s.akhir_jam_pulang||"--:--"):"Data tidak tersedia"};a.useEffect(()=>{C(""),q(!1),P("");const a=ya(fa);if(P(a.info),!a.valid)return C(a.error),void q(!1);q(!0),xa()},[fa,la,J]),a.useEffect(()=>{(async()=>{if(J)return;const a=localStorage.getItem("jam_kerja_list"),e=localStorage.getItem("jam_kerja_bidang_list");try{a||await S(),e||await w()}catch(t){}finally{da(a=>a+1)}})()},[J]),a.useEffect(()=>{const a=()=>{const a=new Date,e=a=>a.toString().padStart(2,"0"),t=e(a.getHours())+":"+e(a.getMinutes())+":"+e(a.getSeconds());na(t)};a();const e=setInterval(a,1e3);return()=>clearInterval(e)},[]);return a.useEffect(()=>()=>_a(),[]),t.jsxs(n,{children:[t.jsx("style",{children:"\n  @keyframes pulse {\n    0% {\n      border-color: #1880ff;\n      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(24, 128, 255, 0.7);\n      transform: scale(1);\n    }\n    50% {\n      border-color: #00d4ff;\n      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 10px rgba(24, 128, 255, 0.3);\n      transform: scale(1.02);\n    }\n    100% {\n      border-color: #1880ff;\n      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(24, 128, 255, 0.7);\n      transform: scale(1);\n    }\n  }\n\n  @keyframes fadeInOut {\n    0%, 100% { opacity: 0.8; }\n    50% { opacity: 1; }\n  }\n\n  @keyframes cornerBlink {\n    0%, 100% { opacity: 1; background: #1880ff; }\n    50% { opacity: 0.6; background: #00d4ff; }\n  }\n"}),t.jsx(i,{style:O,children:t.jsxs(s,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[t.jsx(r,{slot:"start",children:t.jsx(o,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),t.jsxs(l,{style:I,children:["Absensi ","masuk"===fa?"Masuk":"Pulang"]})]})}),t.jsxs(d,{className:"ion-padding",fullscreen:!0,children:[t.jsxs("div",{style:{maxWidth:400,margin:"0 auto",textAlign:"center"},children:[t.jsx("div",{style:{margin:"16px 0 24px 0",padding:"16px",borderRadius:"16px",backgroundColor:"masuk"===fa?"#e8f5e8":"#fff3cd",border:"2px solid ".concat("masuk"===fa?"#4caf50":"#ff9800")},children:t.jsx(c,{color:"masuk"===fa?"success":"warning",children:t.jsx("h2",{style:{margin:"0",fontSize:"1.5rem",fontWeight:"bold"},children:"masuk"===fa?"📥 Absensi Masuk":"📤 Absensi Pulang"})})}),B&&t.jsx(c,{color:B.includes("valid")?"success":"danger",children:t.jsx("p",{children:B})}),X&&t.jsxs(u,{color:"tertiary",onClick:async()=>{Y(!1),G("Mengecek ulang lokasi..."),pa(!1);try{const a=await F();ca&&!A(a.lat,a.lng,ca)?(G("Anda di luar radius lokasi!"),U(!1),Y(!0),H({message:"Anda di luar radius lokasi!",color:"danger",duration:2e3,position:"top"})):(G("Lokasi valid, dalam radius."),U(!0),Y(!1),H({message:"Lokasi valid, dalam radius.",color:"success",duration:2e3,position:"top"})),pa(!0)}catch(a){G("Gagal mendapatkan lokasi"),U(!1),Y(!0),H({message:"Gagal mendapatkan lokasi",color:"danger",duration:2e3,position:"top"}),pa(!0)}},style:{margin:"12px 0"},children:[t.jsx(m,{icon:p,slot:"start"})," Refresh Lokasi"]}),!L&&t.jsx("div",{style:{textAlign:"center",margin:"12px 0"}}),t.jsxs("div",{style:{margin:"18px 0",position:"relative"},children:[L?t.jsx("img",{src:L,alt:"Foto Wajah",style:{width:"100%",borderRadius:18,objectFit:"cover"}}):t.jsxs("div",{style:{position:"relative",borderRadius:18,overflow:"hidden"},children:[t.jsx("video",{ref:M,autoPlay:!0,playsInline:!0,style:{width:"100%",height:"300px",borderRadius:18,background:"#000",objectFit:"cover"}}),ra&&t.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,pointerEvents:"none",display:"flex",alignItems:"center",justifyContent:"center"},children:t.jsxs("div",{style:{width:"min(200px, 50vw)",height:"min(260px, 65vw)",maxWidth:"250px",maxHeight:"320px",border:"3px solid #1880ff",borderRadius:"50%",background:"rgba(24, 128, 255, 0.1)",position:"relative",boxShadow:"0 0 0 9999px rgba(0, 0, 0, 0.5)",animation:"pulse 2s ease-in-out infinite"},children:[t.jsx("div",{style:{position:"absolute",top:"-10px",left:"50%",transform:"translateX(-50%)",width:"30px",height:"6px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite"}}),t.jsx("div",{style:{position:"absolute",bottom:"-10px",left:"50%",transform:"translateX(-50%)",width:"30px",height:"6px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite 0.3s"}}),t.jsx("div",{style:{position:"absolute",left:"-10px",top:"50%",transform:"translateY(-50%)",width:"6px",height:"30px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite 0.6s"}}),t.jsx("div",{style:{position:"absolute",right:"-10px",top:"50%",transform:"translateY(-50%)",width:"6px",height:"30px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite 0.9s"}})]})}),ra&&t.jsx("div",{style:{position:"absolute",bottom:"20px",left:"50%",transform:"translateX(-50%)",background:"rgba(0, 0, 0, 0.7)",color:"white",padding:"8px 16px",borderRadius:"20px",fontSize:"14px",fontWeight:"500",textAlign:"center",pointerEvents:"none"}})]}),t.jsx("canvas",{ref:R,style:{display:"none"}})]}),W&&t.jsx(c,{color:"danger",children:t.jsx("p",{children:W})}),t.jsxs("div",{style:{display:"flex",justifyContent:"center",gap:12,margin:"18px 0"},children:[L?t.jsxs(u,{color:"medium",onClick:()=>{T(null),xa()},size:"large",shape:"round",children:[t.jsx(m,{icon:k,slot:"start"})," Ulangi"]}):t.jsxs(u,{color:"primary",onClick:()=>{if(M.current&&R.current){const a=M.current,e=R.current;e.width=a.videoWidth,e.height=a.videoHeight;const t=e.getContext("2d");t&&(t.drawImage(a,0,0,e.width,e.height),T(e.toDataURL("image/jpeg"))),_a()}},size:"large",shape:"round",disabled:!E||!K,children:[t.jsx(m,{icon:f,slot:"start"})," Ambil Foto"]}),L&&t.jsxs(u,{color:"success",onClick:async()=>{C("");const a=ya(fa);if(!a.valid)return void C(a.error);if(!K)return void C("Lokasi tidak valid. Pastikan Anda berada dalam radius kantor.");if(!L)return void C("Foto wajah diperlukan untuk absensi.");let e;V(!0);try{const a=await F(),t=new Date,n=N(t),i=t.toTimeString().split(" ")[0],s="".concat(a.lat,",").concat(a.lng),r=((a,e)=>{const t=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),n=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),i=D(),s=n.find(a=>a.hari===i&&a.jam_kerja_id);if(!s||!s.jam_kerja_id)return"Tepat Waktu";const r=t.find(a=>a.id==s.jam_kerja_id);if(!r)return"Tepat Waktu";if("masuk"===a)return e<=r.jam_masuk?"Tepat Waktu":"Terlambat";return e<r.jam_pulang?"Pulang Awal":"Tepat Waktu"})(fa,i.substring(0,5));if(e={api_key:"absensiku_api_key_2023",user_id:y.id||y.nik,tanggal:n,status:r,keterangan:"Absensi ".concat(fa," melalui aplikasi mobile - Status: ").concat(r),jenisAbsensi:fa},"masuk"===fa?(e.jam_masuk=i,e.foto_masuk_base64=L,e.lokasi_masuk=s):(e.jam_pulang=i,e.foto_pulang_base64=L,e.lokasi_pulang=s),!navigator.onLine){if(ma(e))return ha(fa),H({message:"Absensi ".concat(fa," disimpan offline. Data akan dikirim otomatis saat online."),color:"warning",duration:4e3,position:"top"}),T(null),_a(),void setTimeout(()=>{window.location.href="/home"},2e3);throw new Error("Gagal menyimpan data offline")}let o;if("masuk"===fa)o=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});else{const a=N(),t=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(y.id||y.nik,"&tanggal=").concat(a)),n=await t.json();if(!("success"===n.status&&n.data.length>0))throw new Error("Data absensi masuk tidak ditemukan untuk hari ini");{const a=n.data[0].id;e.id=a,o=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}}const l=await o.json();if("success"===l.status){ha(fa);const a={...e,synced:!0,timestamp:(new Date).toISOString()},t=JSON.parse(localStorage.getItem("absensi_backup")||"[]");t.push(a),t.length>30&&t.splice(0,t.length-30),localStorage.setItem("absensi_backup",JSON.stringify(t)),H({message:"Absensi ".concat(fa," berhasil disimpan!"),color:"success",duration:3e3,position:"top"}),T(null),_a(),setTimeout(()=>{window.location.href="/home"},2e3)}else{ma(e)?(ha(fa),H({message:"Server error: ".concat(l.message,". Data disimpan offline dan akan dikirim ulang otomatis."),color:"warning",duration:4e3,position:"top"}),T(null),_a(),setTimeout(()=>{window.location.href="/home"},2e3)):(C(l.message||"Gagal menyimpan data absensi"),H({message:l.message||"Gagal menyimpan data absensi",color:"danger",duration:3e3,position:"top"}))}}catch(t){ma(e)?(ha(fa),H({message:"Koneksi bermasalah. Absensi ".concat(fa," disimpan offline dan akan dikirim otomatis saat online."),color:"warning",duration:4e3,position:"top"}),T(null),_a(),setTimeout(()=>{window.location.href="/home"},2e3)):(C("Terjadi kesalahan saat menyimpan data. Silakan coba lagi."),H({message:"Terjadi kesalahan saat menyimpan data",color:"danger",duration:3e3,position:"top"}))}finally{V(!1)}},size:"large",shape:"round",disabled:Q,children:[t.jsx(m,{icon:h,slot:"start"}),Q?"Mengirim...":"Absen ".concat("masuk"===fa?"Masuk":"Pulang")]})]}),t.jsxs(u,{fill:"outline",size:"small",color:"medium",onClick:()=>sa(!ia),style:{margin:"0 0 16px 0","--border-radius":"20px","--padding-start":"16px","--padding-end":"16px"},children:[t.jsx(m,{icon:ia?b:j,slot:"start"}),ia?"Sembunyikan Detail":"Tampilkan Detail"]}),ia&&t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsxs("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#f5f7ff",border:"1px solid #e4e9ff"},children:[t.jsx(c,{color:"primary",children:t.jsxs("p",{style:{margin:0,fontWeight:700},children:[t.jsx(m,{icon:x,style:{marginRight:8}}),(new Date).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})]})}),t.jsx(c,{color:"medium",children:t.jsx("small",{children:"Tanggal di atas adalah acuan pencatatan absensi hari ini."})})]}),t.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#f0f8ff",border:"1px solid #e1f5fe"},children:t.jsx(c,{color:"primary",children:t.jsxs("p",{style:{fontSize:"1.1rem",fontWeight:"bold",margin:"0"},children:["👤 ",y.nama||"Nama Karyawan"]})})}),t.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#f8f9fa",border:"1px solid #e9ecef"},children:t.jsx(c,{color:"dark",children:t.jsxs("p",{style:{fontSize:"1.1rem",fontWeight:"bold",margin:"0"},children:["🕐 Waktu Sekarang: ",ta]})})}),z&&t.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#1880ff",border:"1px solid #1880ff"},children:t.jsx(c,{color:"light",children:t.jsxs("p",{style:{fontSize:"0.95rem",margin:"0"},children:["📋 ",z]})})}),E&&t.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#e3f2fd",border:"1px solid #bbdefb"},children:t.jsx(c,{color:"primary",children:t.jsx("p",{style:{fontSize:"0.9rem",margin:"0"},children:"masuk"===fa?"⏰ Waktu absen masuk: ".concat(Sa("masuk")):"⏰ Waktu absen pulang: ".concat(Sa("pulang"))})})}),t.jsx("div",{style:{margin:"12px 0",padding:"8px",borderRadius:"8px",backgroundColor:Z?"#e8f5e8":"#fff3cd"},children:t.jsx(c,{color:Z?"success":"warning",children:t.jsxs("small",{children:[Z?"🟢 Online":"🔴 Offline",aa>0&&" • ".concat(aa," data menunggu sinkronisasi")]})})}),!Z&&t.jsx("div",{style:{margin:"12px 0",padding:"8px",borderRadius:"8px",backgroundColor:"#f0f8ff",border:"1px solid #e1f5fe"},children:t.jsx(c,{color:"primary",children:t.jsxs("small",{children:["📱 Mode Offline: Status absen berdasarkan data lokal",t.jsx("br",{}),(()=>{const a=N(),e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),t=JSON.parse(localStorage.getItem("absensi_backup")||"[]");return[...e.filter(e=>e.tanggal===a),...t.filter(e=>e.tanggal===a)].some(a=>"masuk"===a.jenisAbsensi||a.jam_masuk)?"✅ Sudah absen masuk hari ini":"❌ Belum absen masuk hari ini"})()]})})})]})]}),t.jsx(g,{isOpen:Q,message:"Mengirim data absensi...",duration:0})]})]})};export{J as default};

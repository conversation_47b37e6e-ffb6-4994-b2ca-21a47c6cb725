import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import {
  IonContent,
  IonPage,
  IonButton,
  IonIcon,
  IonText,
  IonSpinner,
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  useIonToast,
  IonRefresher,
  IonRefresherContent,
  IonAvatar
} from '@ionic/react';
import { cameraOutline, documentTextOutline, calendarOutline, closeCircleOutline, closeOutline, logOutOutline, peopleOutline, clipboardOutline, timeOutline, cloudDownloadOutline, personCircleOutline } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './Home.css';
import { fetchAndStoreHariLibur, isTodayLibur } from '../utils/hariLibur';
import { downloadAllDataWithStatus } from '../utils/downloadAllData';
import { IonAlert } from '@ionic/react';
import { Camera } from '@capacitor/camera';


interface AbsensiData {
  id: string;
  user_id: string;
  tanggal: string;
  jam_masuk: string | null;
  foto_masuk: string | null;
  lokasi_masuk: string | null;
  jam_pulang: string | null;
  foto_pulang: string | null;
  lokasi_pulang: string | null;
  status: string;
  keterangan: string;
}

const Home: React.FC = () => {
  // Memoize user data untuk menghindari parsing berulang
  const user = useMemo(() => {
    try {
      const raw = localStorage.getItem('user');
      const parsed = raw ? JSON.parse(raw) : {};
      return Array.isArray(parsed) ? (parsed[0] || {}) : parsed;
    } catch {
      return {} as any;
    }
  }, []); // Empty dependency karena user data tidak berubah selama session

  // Combine related state untuk mengurangi re-render
  const [uiState, setUiState] = useState({
    loading: false,
    downloading: false,
    isModalOpen: false,
    showDownloadAlert: false,
    showStatusAlert: false
  });

  const [time, setTime] = useState<{h: string, m: string}>({ h: '--', m: '--' });
  const [absensiHariIni, setAbsensiHariIni] = useState<AbsensiData | null>(null);
  const [selectedImage, setSelectedImage] = useState<{url: string, title: string} | null>(null);
  const [liburInfo, setLiburInfo] = useState<{libur: boolean, nama?: string}>({libur: false});
  const [downloadStatus, setDownloadStatus] = useState<any>(null);
  const [present] = useIonToast();

  // Ref untuk debouncing sync
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const history = useHistory();

  // Memoize today's date untuk menghindari kalkulasi berulang
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);
  const userId = useMemo(() => user.id || user.nik, [user.id, user.nik]);





  // Memoize fungsi untuk mengambil data absen offline dari localStorage
  const getOfflineAbsensiHariIni = useCallback(() => {
    try {
      // Cek data offline queue
      const offlineQueue = JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]');
      const offlineToday = offlineQueue.filter((item: any) => {
        const itemDate = new Date(item.timestamp).toISOString().split('T')[0];
        return itemDate === today && (item.user_id === userId);
      });

      // Cek data backup yang sudah sync
      const backupData = JSON.parse(localStorage.getItem('absensi_backup') || '[]');
      const backupToday = backupData.filter((item: any) => {
        const itemDate = new Date(item.timestamp).toISOString().split('T')[0];
        return itemDate === today && (item.user_id === userId);
      });

      // Gabungkan data offline dan backup
      const allOfflineData = [...offlineToday, ...backupToday];

      if (allOfflineData.length > 0) {
        // Buat struktur data yang sesuai dengan format server
        const absensiData: any = {
          id: 'offline_' + today,
          user_id: userId,
          tanggal: today,
          jam_masuk: null,
          foto_masuk: null,
          lokasi_masuk: null,
          jam_pulang: null,
          foto_pulang: null,
          lokasi_pulang: null,
          status: 'Hadir',
          keterangan: 'Data offline'
        };

        // Proses data offline untuk mengisi jam masuk/pulang
        allOfflineData.forEach((item: any) => {
          if (item.jenisAbsensi === 'masuk' || item.jam_masuk) {
            absensiData.jam_masuk = item.jam_masuk || item.timestamp.split('T')[1].substring(0, 8);
            absensiData.foto_masuk = item.foto_masuk_base64 || null;
            absensiData.lokasi_masuk = item.lokasi_masuk || null;
            absensiData.status = item.status || 'Hadir';
          }
          if (item.jenisAbsensi === 'pulang' || item.jam_pulang) {
            absensiData.jam_pulang = item.jam_pulang || item.timestamp.split('T')[1].substring(0, 8);
            absensiData.foto_pulang = item.foto_pulang_base64 || null;
            absensiData.lokasi_pulang = item.lokasi_pulang || null;
          }
        });

        return absensiData;
      }

      return null;
    } catch (error) {
      console.error('Error getting offline absensi:', error);
      return null;
    }
  }, [today, userId]); // Dependencies yang minimal dan stabil



  // Memoize dan optimasi fungsi untuk mengambil data absen hari ini
  const fetchAbsensiHariIni = useCallback(async () => {
    if (!userId) return;

    // Batch state update untuk mengurangi re-render
    setUiState(prev => ({ ...prev, loading: true }));

    try {
      let onlineData = null;

      // Coba ambil data online dengan timeout untuk perangkat lambat
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 detik timeout

        const response = await fetch(
          `https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${userId}&tanggal=${today}`,
          { signal: controller.signal }
        );
        clearTimeout(timeoutId);

        const result = await response.json();

        if (result.status === 'success' && result.data.length > 0) {
          onlineData = result.data[0];
        }
      } catch (error) {
        console.log('Tidak dapat mengambil data online, menggunakan data offline');
      }

      // Ambil data offline
      const offlineData = getOfflineAbsensiHariIni();

      // Prioritaskan data online, tapi gunakan data offline jika online tidak ada
      if (onlineData) {
        setAbsensiHariIni(onlineData);
      } else if (offlineData) {
        setAbsensiHariIni(offlineData);
      } else {
        setAbsensiHariIni(null);
      }

    } catch (error) {
      console.error('Error fetching absensi hari ini:', error);
      // Jika error, coba gunakan data offline
      const offlineData = getOfflineAbsensiHariIni();
      setAbsensiHariIni(offlineData);
    } finally {
      // Batch state update
      setUiState(prev => ({ ...prev, loading: false }));
    }
  }, [userId, today, getOfflineAbsensiHariIni]);

  // Optimasi clock update dengan memoization
  useEffect(() => {
    const update = () => {
      const now = new Date();
      const newTime = {
        h: now.getHours().toString().padStart(2, '0'),
        m: now.getMinutes().toString().padStart(2, '0'),
      };

      // Hanya update jika waktu benar-benar berubah
      setTime(prevTime => {
        if (prevTime.h !== newTime.h || prevTime.m !== newTime.m) {
          return newTime;
        }
        return prevTime;
      });
    };

    update();
    // Gunakan interval yang lebih efisien - update setiap 30 detik untuk menghemat battery
    const interval = setInterval(update, 30000);
    return () => clearInterval(interval);
  }, []);

  // Optimasi fungsi sinkronisasi data offline dengan memoization dan throttling
  const syncOfflineData = useCallback(async () => {
    // Cek apakah sedang ada proses sync yang berjalan
    const syncInProgress = localStorage.getItem('sync_in_progress');
    if (syncInProgress) {
      const syncTime = parseInt(syncInProgress);
      // Jika sync sudah berjalan lebih dari 5 menit, reset flag
      if (Date.now() - syncTime > 5 * 60 * 1000) {
        localStorage.removeItem('sync_in_progress');
      } else {
        console.log('[Home] Sync already in progress, skipping...');
        return;
      }
    }

    try {
      // Set flag bahwa sync sedang berjalan dengan timestamp
      localStorage.setItem('sync_in_progress', Date.now().toString());

      let offlineQueue = JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]');
      const initialCount = offlineQueue.length;

      if (initialCount === 0) {
        localStorage.removeItem('sync_in_progress');
        return;
      }

      console.log(`[Home] Syncing ${initialCount} offline records...`);
      let successCount = 0;

      // Proses setiap data offline
      for (let i = offlineQueue.length - 1; i >= 0; i--) {
        const data = offlineQueue[i];

        // Cek apakah data sudah pernah di-sync (ada flag synced)
        if (data.synced) {
          console.log('Data already synced, removing from queue:', data.id);
          offlineQueue.splice(i, 1);
          continue;
        }

        try {
          let response;
          const payload = { ...data };
          delete payload.id; // Hapus ID sementara
          delete payload.timestamp;
          delete payload.synced;
          delete payload.jenisAbsensi; // Hapus field tambahan

          if (data.jenisAbsensi === 'masuk') {
            // POST untuk absensi masuk
            response = await fetch('https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(payload)
            });
          } else {
            // PUT untuk absensi pulang - cari ID record dulu
            const today = data.tanggal;
            const checkResponse = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${data.user_id}&tanggal=${today}`);
            const checkResult = await checkResponse.json();

            if (checkResult.status === 'success' && checkResult.data.length > 0) {
              payload.id = checkResult.data[0].id;
              response = await fetch('https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
              });
            } else {
              console.error('Record not found for offline pulang data');
              continue;
            }
          }

          const result = await response.json();
          if (result.status === 'success') {
            // Langsung hapus data yang berhasil dikirim dari array
            offlineQueue.splice(i, 1);
            successCount++;
            console.log('Successfully synced and removed offline record:', data.id);

            // Simpan backup data yang berhasil dikirim
            const backupData = { ...data, synced: true, syncedAt: new Date().toISOString() };
            const existingBackup = JSON.parse(localStorage.getItem('absensi_backup') || '[]');
            existingBackup.push(backupData);
            // Simpan maksimal 30 hari terakhir
            if (existingBackup.length > 30) {
              existingBackup.splice(0, existingBackup.length - 30);
            }
            localStorage.setItem('absensi_backup', JSON.stringify(existingBackup));
          } else {
            console.error('Failed to sync record:', data.id, result.message);
          }
        } catch (error) {
          console.error('Error syncing individual record:', data.id, error);
        }
      }

      // Update localStorage dengan queue yang sudah dibersihkan
      localStorage.setItem('offline_absensi_queue', JSON.stringify(offlineQueue));

      if (successCount > 0) {
        present({
          message: `${successCount} data offline berhasil disinkronkan`,
          color: 'success',
          duration: 3000,
          position: 'top'
        });

        // Refresh data absensi setelah sync berhasil
        fetchAbsensiHariIni();
      }

    } catch (error) {
      console.error('[Home] Error during sync:', error);
    } finally {
      // Hapus flag sync in progress
      localStorage.removeItem('sync_in_progress');
    }
  }, []); // Empty dependency array karena fungsi ini self-contained

  // Memoize fungsi wrapper dengan debouncing untuk mencegah multiple sync
  const debouncedSyncOfflineData = useCallback(() => {
    // Clear timeout sebelumnya jika ada
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }

    // Set timeout baru dengan delay yang lebih panjang untuk perangkat lambat
    syncTimeoutRef.current = setTimeout(() => {
      syncOfflineData();
    }, 3000); // Delay 3 detik untuk perangkat spesifikasi rendah
  }, [syncOfflineData]);

  // Memoize fungsi untuk membersihkan flag sync yang mungkin tertinggal
  const cleanupSyncFlag = useCallback(() => {
    const syncInProgress = localStorage.getItem('sync_in_progress');
    if (syncInProgress) {
      const now = Date.now();
      const flagTime = parseInt(syncInProgress);

      // Jika flag sudah lebih dari 30 detik, hapus (kemungkinan stuck)
      if (now - flagTime > 30000) {
        console.log('[Home] Cleaning up stuck sync flag');
        localStorage.removeItem('sync_in_progress');
      }
    }
  }, []);

  // Memoize fungsi untuk handle pull-to-refresh
  const handleRefresh = useCallback(async (event: CustomEvent) => {
    console.log('[Home] Pull-to-refresh triggered');

    try {
      // Refresh data absensi
      await fetchAbsensiHariIni();

      // Sync data offline jika ada koneksi
      if (navigator.onLine) {
        await syncOfflineData();
      }

      // Refresh hari libur dengan dynamic import untuk mengurangi bundle size
      const { fetchAndStoreHariLibur, isTodayLibur } = await import('../utils/hariLibur');
      await fetchAndStoreHariLibur();
      setLiburInfo(isTodayLibur());

      present({
        message: 'Data berhasil diperbarui',
        color: 'success',
        duration: 2000,
        position: 'top'
      });

    } catch (error) {
      console.error('Error during refresh:', error);
      present({
        message: 'Gagal memperbarui data',
        color: 'danger',
        duration: 2000,
        position: 'top'
      });
    } finally {
      // Complete the refresh
      event.detail.complete();
    }
  }, [fetchAbsensiHariIni, syncOfflineData, present]);

  // Optimasi useEffect untuk fetch data awal dengan dependencies yang tepat
  useEffect(() => {
    fetchAbsensiHariIni();

    // Bersihkan flag sync yang mungkin tertinggal
    cleanupSyncFlag();

    // Sync data offline jika ada koneksi internet dengan delay
    if (navigator.onLine) {
      // Delay sync untuk memberikan waktu UI render terlebih dahulu
      setTimeout(() => {
        debouncedSyncOfflineData();
      }, 1000);
    }
  }, [fetchAbsensiHariIni, cleanupSyncFlag, debouncedSyncOfflineData]);

  // Optimasi visibility change handler dengan memoization
  const handleVisibilityChange = useCallback(() => {
    if (!document.hidden) {
      fetchAbsensiHariIni();
      // Sync data offline jika ada koneksi
      if (navigator.onLine) {
        debouncedSyncOfflineData();
      }
    }
  }, [fetchAbsensiHariIni, debouncedSyncOfflineData]);

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [handleVisibilityChange]);

  // Monitor status online/offline untuk auto sync
  // Monitor status online/offline untuk auto sync
  useEffect(() => {
    const handleOnline = () => {
      console.log('[Home] Device is online, syncing offline data...');
      debouncedSyncOfflineData();
    };

    const handleOffline = () => {
      console.log('[Home] Device is offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);

      // Cleanup timeout jika ada
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, []);

  // Fetch hari libur dan jam kerja saat halaman dimuat
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load hari libur
        await fetchAndStoreHariLibur();
        setLiburInfo(isTodayLibur());
        
        // Load jam kerja jika belum ada
        const jamKerjaList = localStorage.getItem('jam_kerja_list');
        const jamKerjaBidangList = localStorage.getItem('jam_kerja_bidang_list');
        
        if (!jamKerjaList || !jamKerjaBidangList) {
          // Import fungsi yang diperlukan
          const { fetchAndStoreJamKerja } = await import('../utils/jamKerja');
          const { fetchAndStoreJamKerjaBidang } = await import('../utils/jamKerjaBidang');
          await fetchAndStoreJamKerja();
          await fetchAndStoreJamKerjaBidang();
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };
    
    loadInitialData();
  }, []);

  // Update info libur jika user refresh halaman
  useEffect(() => {
    setLiburInfo(isTodayLibur());
  }, [uiState.loading]);

  useEffect(() => {
    // Minta izin kamera dan lokasi saat pertama kali aplikasi dijalankan
    const requestPermissions = async () => {
      try {
        await Camera.requestPermissions();
      } catch (e) {
        // Optional: bisa tampilkan alert jika gagal
      }

    };
    requestPermissions();
  }, []);



  const handleDownloadData = useCallback(async () => {
    setUiState(prev => ({ ...prev, downloading: true }));
    const status = await downloadAllDataWithStatus();
    setUiState(prev => ({ ...prev, downloading: false, showStatusAlert: true }));
    setDownloadStatus(status);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('user');
    window.location.replace('/login');
  };

  // Helper function untuk mendapatkan URL foto
  const getFotoUrl = (fotoName: string | null) => {
    if (!fotoName) return null;
    return `https://absensiku.trunois.my.id/uploads/${fotoName}`;
  };

  // Helper function untuk format jam
  const formatJam = (jam: string | null | undefined) => {
    if (!jam) return '- : -';
    return jam.substring(0, 5); // Ambil HH:MM saja
  };

  // Helper function untuk mendapatkan status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Tepat Waktu':
        return '#4caf50'; // Hijau
      case 'Terlambat':
        return '#f44336'; // Merah
      case 'Pulang Awal':
        return '#ff9800'; // Orange
      default:
        return '#9e9e9e'; // Abu-abu
    }
  };

  // Fungsi untuk menghitung status absen berdasarkan jam masuk dan jam kerja
  const hitungStatusAbsen = (jamMasuk: string | null, jamPulang: string | null) => {
    if (!jamMasuk) return 'Belum Absen';
    
    try {
      // Ambil data jam kerja dari localStorage
      const jamKerjaList = JSON.parse(localStorage.getItem('jam_kerja_list') || '[]');
      const jamKerjaBidangList = JSON.parse(localStorage.getItem('jam_kerja_bidang_list') || '[]');
      
      const today = new Date().toLocaleDateString('id-ID', { weekday: 'long' });
      const jamKerjaBidang = jamKerjaBidangList.find((j: any) => j.hari === today && j.jam_kerja_id);
      
      if (!jamKerjaBidang || !jamKerjaBidang.jam_kerja_id) {
        return 'Tepat Waktu'; // Default jika tidak ada data jam kerja
      }
      
      const jamKerja = jamKerjaList.find((j: any) => j.id == jamKerjaBidang.jam_kerja_id);
      if (!jamKerja) {
        return 'Tepat Waktu'; // Default jika tidak ada data jam kerja
      }
      
      // Parse jam masuk absen dan jam masuk seharusnya
      const jamMasukAbsen = new Date(`2000-01-01T${jamMasuk}`);
      const jamMasukSeharusnya = new Date(`2000-01-01T${jamKerja.jam_masuk}`);
      
      // Jika absen setelah jam masuk seharusnya, maka terlambat
      if (jamMasukAbsen > jamMasukSeharusnya) {
        return 'Terlambat';
      }
      
      return 'Tepat Waktu';
    } catch (error) {
      console.error('Error menghitung status absen:', error);
      return 'Tepat Waktu'; // Default jika error
    }
  };

  // Memoize fungsi untuk membuka modal gambar
  const openImageModal = useCallback((imageUrl: string, title: string) => {
    setSelectedImage({ url: imageUrl, title });
    setUiState(prev => ({ ...prev, isModalOpen: true }));
  }, []);

  // Memoize fungsi untuk menutup modal
  const closeImageModal = useCallback(() => {
    setUiState(prev => ({ ...prev, isModalOpen: false }));
    setSelectedImage(null);
  }, []);

  return (
    <IonPage>
      <IonContent fullscreen className="home2-bg">
        {/* Pull-to-refresh */}
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon="chevron-down-circle-outline"
            pullingText="Tarik untuk memperbarui..."
            refreshingSpinner="circles"
            refreshingText="Memperbarui data..."
          />
        </IonRefresher>

        <div className="home2-header">
          <div className="home2-header-content" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-start' }} />
            <div style={{ flex: 2, textAlign: 'center' }}>
              <div className="home-header-avatar">
                <IonAvatar>
                  {user.foto_profil ? (
                    <img src={`https://absensiku.trunois.my.id/uploads/${user.foto_profil}`} alt="Avatar" />
                  ) : (
                    <div className="home-avatar-initial">
                      {(user.nama || '?').charAt(0).toUpperCase()}
                    </div>
                  )}
                </IonAvatar>
              </div>
              <h1 className="home2-nama" style={{ margin: 0 }}>{user.nama || 'Nama user'}</h1>
              <p className="home2-jabatan" style={{ margin: 0 }}>{user.jabatan || 'Jabatan'}</p>
            </div>
            <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-end' }}>
              <IonButton onClick={handleLogout} color="medium" size="small" fill="clear" style={{ minWidth: 0, padding: 0, marginRight: 12 }}>
                <IonIcon icon={logOutOutline} slot="icon-only" style={{ fontSize: 30, color: '#fff' }} />
              </IonButton>
            </div>
          </div>
        </div>
        <div className="home2-main">
          <div className="home2-clock-row">
            <div className="home2-clock-box">{time.h}</div>
            <div className="home2-clock-sep">:</div>
            <div className="home2-clock-box">{time.m}</div>
          </div>
          {/* Info Hari Libur */}
          {liburInfo.libur && (
            <div className="holiday-banner">
              <IonIcon icon={calendarOutline} style={{ marginRight: 8 }} />
              Hari ini libur: {liburInfo.nama}
            </div>
          )}



          <div className="home2-menu-row">
            <div className="home2-menu-btn menu-histori" onClick={() => history.push('/histori')}>
              <div className="menu-icon-wrap"><IonIcon icon={documentTextOutline} /></div>
              <span className="menu-label">Histori</span>
            </div>
            <div className="home2-menu-btn menu-izin" onClick={() => history.push('/histori-izin-dinas')}>
              <div className="menu-icon-wrap"><IonIcon icon={calendarOutline} /></div>
              <span className="menu-label">Izin</span>
            </div>
            <div className="home2-menu-btn menu-rapat" onClick={() => history.push('/rapat')}>
              <div className="menu-icon-wrap"><IonIcon icon={peopleOutline} /></div>
              <span className="menu-label">Rapat/Apel</span>
            </div>
          </div>

          {/* Menu Row Kedua */}
          <div className="home2-menu-row" style={{ marginTop: '12px' }}>
            <div className="home2-menu-btn menu-laporan" onClick={() => history.push('/laporan-harian')}>
              <div className="menu-icon-wrap"><IonIcon icon={clipboardOutline} /></div>
              <span className="menu-label">Lap. Harian</span>
            </div>
            <div className="home2-menu-btn menu-profil" onClick={() => history.push('/profile')}>
              <div className="menu-icon-wrap"><IonIcon icon={personCircleOutline} /></div>
              <span className="menu-label">Profil</span>
            </div>
            <div className="home2-menu-btn menu-lembur" onClick={() => history.push('/lembur')}>
              <div className="menu-icon-wrap"><IonIcon icon={timeOutline} /></div>
              <span className="menu-label">Lembur</span>
            </div>
          </div>
          {/* Tombol Download Data (panjang horizontal, ikon kiri) */}
          <div className="download-horizontal-wrap">
            <button
              className={`download-horizontal-btn${uiState.downloading ? ' disabled' : ''}`}
              onClick={!uiState.downloading ? handleDownloadData : undefined}
            >
              <span className="download-icon">
                <IonIcon icon={cloudDownloadOutline} />
              </span>
              <span className="download-text">{uiState.downloading ? 'Memproses...' : 'Download Data'}</span>
              {uiState.downloading && <IonSpinner name="crescent" className="download-spinner" />}
            </button>
          </div>
          <div className="home2-status-wrap">
            <div className="home2-status-title">Status Absen Hari Ini</div>

            {uiState.loading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <IonSpinner name="crescent" />
                <p style={{ margin: '10px 0 0 0', color: '#666' }}>Memuat data...</p>
              </div>
            ) : (
              <>
                <div className="home2-status-row">
                  {/* Card Absen Masuk */}
                  <div className="home2-status-box" style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: '12px',
                    minHeight: '180px'
                  }}>
                    <span className="home2-status-label">Masuk</span>
                    <span className="home2-status-time">{formatJam(absensiHariIni?.jam_masuk)}</span>
                    {absensiHariIni?.jam_masuk && (
                      <div style={{
                        fontSize: '0.7rem',
                        color: getStatusColor(hitungStatusAbsen(absensiHariIni.jam_masuk, absensiHariIni.jam_pulang)),
                        fontWeight: 'bold',
                        marginTop: '2px',
                        marginBottom: '8px'
                      }}>
                        {hitungStatusAbsen(absensiHariIni.jam_masuk, absensiHariIni.jam_pulang)}
                        {absensiHariIni.id?.startsWith('offline_') && (
                          <span style={{
                            fontSize: '0.6rem',
                            color: '#ff9800',
                            marginLeft: '4px',
                            fontWeight: 'normal'
                          }}>
                            (Offline)
                          </span>
                        )}
                      </div>
                    )}

                    {/* Foto Absen Masuk di dalam card */}
                    {absensiHariIni?.foto_masuk ? (
                      <div style={{ marginTop: '8px' }}>
                        {getFotoUrl(absensiHariIni.foto_masuk) && (
                          <img
                            src={getFotoUrl(absensiHariIni.foto_masuk)!}
                            alt="Foto Absen Masuk"
                            style={{
                              width: '80px',
                              height: '80px',
                              objectFit: 'cover',
                              borderRadius: '8px',
                              border: '2px solid #ddd',
                              cursor: 'pointer',
                              transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                            }}
                            onClick={() => openImageModal(
                              getFotoUrl(absensiHariIni.foto_masuk)!,
                              `Foto Absen Masuk - ${formatJam(absensiHariIni.jam_masuk)} (${hitungStatusAbsen(absensiHariIni.jam_masuk, absensiHariIni.jam_pulang)})`
                            )}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'scale(1.05)';
                              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'scale(1)';
                              e.currentTarget.style.boxShadow = 'none';
                            }}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                            }}
                          />
                        )}
                      </div>
                    ) : (
                      absensiHariIni?.jam_masuk && (
                        <div style={{
                          marginTop: '8px',
                          fontSize: '0.7rem',
                          color: '#999',
                          textAlign: 'center'
                        }}>
                          📸 Foto tersedia
                        </div>
                      )
                    )}
                  </div>

                  {/* Card Absen Pulang */}
                  <div className="home2-status-box" style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: '12px',
                    minHeight: '180px'
                  }}>
                    <span className="home2-status-label">Pulang</span>
                    <span className="home2-status-time">{formatJam(absensiHariIni?.jam_pulang)}</span>
                    {absensiHariIni?.jam_pulang && (
                      <div style={{
                        fontSize: '0.7rem',
                        color: '#4caf50',
                        fontWeight: 'bold',
                        marginTop: '2px',
                        marginBottom: '8px'
                      }}>
                        Selesai
                      </div>
                    )}

                    {/* Foto Absen Pulang di dalam card */}
                    {absensiHariIni?.foto_pulang ? (
                      <div style={{ marginTop: '8px' }}>
                        {getFotoUrl(absensiHariIni.foto_pulang) && (
                          <img
                            src={getFotoUrl(absensiHariIni.foto_pulang)!}
                            alt="Foto Absen Pulang"
                            style={{
                              width: '80px',
                              height: '80px',
                              objectFit: 'cover',
                              borderRadius: '8px',
                              border: '2px solid #ddd',
                              cursor: 'pointer',
                              transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                            }}
                            onClick={() => openImageModal(
                              getFotoUrl(absensiHariIni.foto_pulang)!,
                              `Foto Absen Pulang - ${formatJam(absensiHariIni.jam_pulang)}`
                            )}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'scale(1.05)';
                              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'scale(1)';
                              e.currentTarget.style.boxShadow = 'none';
                            }}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                            }}
                          />
                        )}
                      </div>
                    ) : (
                      absensiHariIni?.jam_pulang && (
                        <div style={{
                          marginTop: '8px',
                          fontSize: '0.7rem',
                          color: '#999',
                          textAlign: 'center'
                        }}>
                          📸 Foto tersedia
                        </div>
                      )
                    )}
                  </div>
                </div>



                {/* Pesan jika belum absen */}
                {!absensiHariIni && (
                  <div style={{
                    textAlign: 'center',
                    padding: '20px',
                    color: '#666'
                  }}>
                    <IonIcon
                      icon={closeCircleOutline}
                      style={{ fontSize: '2rem', color: '#ff9800', marginBottom: '8px' }}
                    />
                    <p style={{ margin: '0', fontSize: '0.9rem' }}>
                      Belum ada absensi hari ini
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </IonContent>

      {/* Modal untuk menampilkan gambar besar */}
      <IonModal isOpen={uiState.isModalOpen} onDidDismiss={closeImageModal}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>{selectedImage?.title || 'Foto Absensi'}</IonTitle>
            <IonButtons slot="end">
              <IonButton onClick={closeImageModal}>
                <IonIcon icon={closeOutline} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            flexDirection: 'column'
          }}>
            {selectedImage && (
              <>
                <img
                  src={selectedImage.url}
                  alt={selectedImage.title}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '70vh',
                    objectFit: 'contain',
                    borderRadius: '12px',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                <div style={{
                  marginTop: '16px',
                  textAlign: 'center',
                  padding: '12px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '8px',
                  maxWidth: '300px'
                }}>
                  <IonText color="medium">
                    <p style={{ margin: '0', fontSize: '0.9rem' }}>
                      {selectedImage.title}
                    </p>
                  </IonText>
                </div>
              </>
            )}
          </div>
        </IonContent>
      </IonModal>

      <IonAlert
        isOpen={uiState.showDownloadAlert}
        onDidDismiss={() => setUiState(prev => ({ ...prev, showDownloadAlert: false }))}
        header="Download Data"
        message="Data berhasil di-download dan disimpan di perangkat."
        buttons={['OK']}
      />

      <IonAlert
        isOpen={uiState.showStatusAlert}
        onDidDismiss={() => setUiState(prev => ({ ...prev, showStatusAlert: false }))}
        header="Status Data Anda"
        message={`
          Hari Libur: ${downloadStatus?.hariLibur ? '✅' : '❌'}
          Jam Kerja: ${downloadStatus?.jamKerja ? '✅' : '❌'}
          Jam Kerja Bidang: ${downloadStatus?.jamKerjaBidang ? '✅' : '❌'}
          Bidang: ${downloadStatus?.bidang ? '✅' : '❌'}
          Lokasi: ${downloadStatus?.lokasi ? '✅' : '❌'}
        `}
        buttons={['OK']}
      />



      {/* Floating Camera Button */}
      <div className="floating-camera-container">
        <button
          className="floating-camera-btn"
          onClick={() => history.push('/absensi')}
          disabled={liburInfo.libur}
          style={liburInfo.libur ? { opacity: 0.5, cursor: 'not-allowed' } : {}}
        >
          <IonIcon icon={cameraOutline} className="floating-camera-icon" />
        </button>
      </div>
    </IonPage>
  );
};

export default Home;

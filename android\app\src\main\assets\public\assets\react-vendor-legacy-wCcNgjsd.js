System.register(["./ionic-legacy-DbGqp7zN.js"],function(t,e){"use strict";var n,r,o,i,c,a;return{setters:[t=>{n=t.g,r=t.R,o=t.i,i=t._,c=t.c,a=t.l,t.a,t.b}],execute:function(){function e(t,n){return e=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},e(t,n)}function s(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,e(t,n)}t({b:function(t){var e=t.computedMatch,n=t.to,s=t.push,u=void 0!==s&&s;return r.createElement(Bt.Consumer,null,function(t){t||o();var s=t.history,p=t.staticContext,f=u?s.push:s.replace,l=c(e?"string"==typeof n?Ht(n,e.params):i({},n,{pathname:Ht(n.pathname,e.params)}):n);return p?(f(l),null):r.createElement(It,{onMount:function(){f(l)},onUpdate:function(t,e){var n=c(e.to);a(n,i({},l,{key:n.key}))||f(l)},to:n})})},m:Kt,u:function(){return Qt(Wt)},w:function(t){var e="withRouter("+(t.displayName||t.name)+")",n=function(e){var n=e.wrappedComponentRef,c=X(e,["wrappedComponentRef"]);return r.createElement(Bt.Consumer,null,function(e){return e||o(),r.createElement(t,i({},c,e,{ref:n}))})};return n.displayName=e,n.WrappedComponent=t,kt(n,t)}});var u={exports:{}};function p(){}function f(){}f.resetWarningCache=p,u.exports=function(){function t(t,e,n,r,o,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:f,resetWarningCache:p};return n.PropTypes=n,n}();var l=u.exports;const y=n(l);var m={exports:{}},d=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)};m.exports=P,m.exports.parse=v,m.exports.compile=function(t,e){return S(v(t,e),e)},m.exports.tokensToFunction=S,m.exports.tokensToRegExp=E;var h=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function v(t,e){for(var n,r=[],o=0,i=0,c="",a=e&&e.delimiter||"/";null!=(n=h.exec(t));){var s=n[0],u=n[1],p=n.index;if(c+=t.slice(i,p),i=p+s.length,u)c+=u[1];else{var f=t[i],l=n[2],y=n[3],m=n[4],d=n[5],v=n[6],g=n[7];c&&(r.push(c),c="");var x=null!=l&&null!=f&&f!==l,S="+"===v||"*"===v,$="?"===v||"*"===v,w=l||a,_=m||d,E=l||("string"==typeof r[r.length-1]?r[r.length-1]:"");r.push({name:y||o++,prefix:l||"",delimiter:w,optional:$,repeat:S,partial:x,asterisk:!!g,pattern:_?C(_):g?".*":b(w,E)})}}return i<t.length&&(c+=t.substr(i)),c&&r.push(c),r}function b(t,e){return!e||e.indexOf(t)>-1?"[^"+$(t)+"]+?":$(e)+"|(?:(?!"+$(e)+")[^"+$(t)+"])+?"}function g(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function x(t){return encodeURI(t).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function S(t,e){for(var n=new Array(t.length),r=0;r<t.length;r++)"object"==typeof t[r]&&(n[r]=new RegExp("^(?:"+t[r].pattern+")$",_(e)));return function(e,r){for(var o="",i=e||{},c=(r||{}).pretty?g:encodeURIComponent,a=0;a<t.length;a++){var s=t[a];if("string"!=typeof s){var u,p=i[s.name];if(null==p){if(s.optional){s.partial&&(o+=s.prefix);continue}throw new TypeError('Expected "'+s.name+'" to be defined')}if(d(p)){if(!s.repeat)throw new TypeError('Expected "'+s.name+'" to not repeat, but received `'+JSON.stringify(p)+"`");if(0===p.length){if(s.optional)continue;throw new TypeError('Expected "'+s.name+'" to not be empty')}for(var f=0;f<p.length;f++){if(u=c(p[f]),!n[a].test(u))throw new TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but received `'+JSON.stringify(u)+"`");o+=(0===f?s.prefix:s.delimiter)+u}}else{if(u=s.asterisk?x(p):c(p),!n[a].test(u))throw new TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but received "'+u+'"');o+=s.prefix+u}}else o+=s}return o}}function $(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function C(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function w(t,e){return t.keys=e,t}function _(t){return t&&t.sensitive?"":"i"}function E(t,e,n){d(e)||(n=e||n,e=[]);for(var r=(n=n||{}).strict,o=!1!==n.end,i="",c=0;c<t.length;c++){var a=t[c];if("string"==typeof a)i+=$(a);else{var s=$(a.prefix),u="(?:"+a.pattern+")";e.push(a),a.repeat&&(u+="(?:"+s+u+")*"),i+=u=a.optional?a.partial?s+"("+u+")?":"(?:"+s+"("+u+"))?":s+"("+u+")"}}var p=$(n.delimiter||"/"),f=i.slice(-p.length)===p;return r||(i=(f?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=o?"$":r&&f?"":"(?="+p+"|$)",w(new RegExp("^"+i,_(n)),e)}function P(t,e,n){return d(e)||(n=e||n,e=[]),n=n||{},t instanceof RegExp?function(t,e){var n=t.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)e.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return w(t,e)}(t,e):d(t)?function(t,e,n){for(var r=[],o=0;o<t.length;o++)r.push(P(t[o],e,n).source);return w(new RegExp("(?:"+r.join("|")+")",_(n)),e)}(t,e,n):function(t,e,n){return E(v(t,n),e,n)}(t,e,n)}var M=m.exports;const O=n(M);var R={},T="function"==typeof Symbol&&Symbol.for,j=T?Symbol.for("react.element"):60103,A=T?Symbol.for("react.portal"):60106,U=T?Symbol.for("react.fragment"):60107,k=T?Symbol.for("react.strict_mode"):60108,F=T?Symbol.for("react.profiler"):60114,L=T?Symbol.for("react.provider"):60109,D=T?Symbol.for("react.context"):60110,N=T?Symbol.for("react.async_mode"):60111,W=T?Symbol.for("react.concurrent_mode"):60111,B=T?Symbol.for("react.forward_ref"):60112,I=T?Symbol.for("react.suspense"):60113,z=T?Symbol.for("react.suspense_list"):60120,V=T?Symbol.for("react.memo"):60115,q=T?Symbol.for("react.lazy"):60116,H=T?Symbol.for("react.block"):60121,J=T?Symbol.for("react.fundamental"):60117,Y=T?Symbol.for("react.responder"):60118,G=T?Symbol.for("react.scope"):60119;
/** @license React v16.13.1
       * react-is.production.min.js
       *
       * Copyright (c) Facebook, Inc. and its affiliates.
       *
       * This source code is licensed under the MIT license found in the
       * LICENSE file in the root directory of this source tree.
       */function K(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case j:switch(t=t.type){case N:case W:case U:case F:case k:case I:return t;default:switch(t=t&&t.$$typeof){case D:case B:case q:case V:case L:return t;default:return e}}case A:return e}}}function Q(t){return K(t)===W}function X(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;n[r]=t[r]}return n}R.AsyncMode=N,R.ConcurrentMode=W,R.ContextConsumer=D,R.ContextProvider=L,R.Element=j,R.ForwardRef=B,R.Fragment=U,R.Lazy=q,R.Memo=V,R.Portal=A,R.Profiler=F,R.StrictMode=k,R.Suspense=I,R.isAsyncMode=function(t){return Q(t)||K(t)===N},R.isConcurrentMode=Q,R.isContextConsumer=function(t){return K(t)===D},R.isContextProvider=function(t){return K(t)===L},R.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===j},R.isForwardRef=function(t){return K(t)===B},R.isFragment=function(t){return K(t)===U},R.isLazy=function(t){return K(t)===q},R.isMemo=function(t){return K(t)===V},R.isPortal=function(t){return K(t)===A},R.isProfiler=function(t){return K(t)===F},R.isStrictMode=function(t){return K(t)===k},R.isSuspense=function(t){return K(t)===I},R.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===U||t===W||t===F||t===k||t===I||t===z||"object"==typeof t&&null!==t&&(t.$$typeof===q||t.$$typeof===V||t.$$typeof===L||t.$$typeof===D||t.$$typeof===B||t.$$typeof===J||t.$$typeof===Y||t.$$typeof===G||t.$$typeof===H)},R.typeOf=K;var Z={exports:{}},tt={},et="function"==typeof Symbol&&Symbol.for,nt=et?Symbol.for("react.element"):60103,rt=et?Symbol.for("react.portal"):60106,ot=et?Symbol.for("react.fragment"):60107,it=et?Symbol.for("react.strict_mode"):60108,ct=et?Symbol.for("react.profiler"):60114,at=et?Symbol.for("react.provider"):60109,st=et?Symbol.for("react.context"):60110,ut=et?Symbol.for("react.async_mode"):60111,pt=et?Symbol.for("react.concurrent_mode"):60111,ft=et?Symbol.for("react.forward_ref"):60112,lt=et?Symbol.for("react.suspense"):60113,yt=et?Symbol.for("react.suspense_list"):60120,mt=et?Symbol.for("react.memo"):60115,dt=et?Symbol.for("react.lazy"):60116,ht=et?Symbol.for("react.block"):60121,vt=et?Symbol.for("react.fundamental"):60117,bt=et?Symbol.for("react.responder"):60118,gt=et?Symbol.for("react.scope"):60119;function xt(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case nt:switch(t=t.type){case ut:case pt:case ot:case ct:case it:case lt:return t;default:switch(t=t&&t.$$typeof){case st:case ft:case dt:case mt:case at:return t;default:return e}}case rt:return e}}}function St(t){return xt(t)===pt}tt.AsyncMode=ut,tt.ConcurrentMode=pt,tt.ContextConsumer=st,tt.ContextProvider=at,tt.Element=nt,tt.ForwardRef=ft,tt.Fragment=ot,tt.Lazy=dt,tt.Memo=mt,tt.Portal=rt,tt.Profiler=ct,tt.StrictMode=it,tt.Suspense=lt,tt.isAsyncMode=function(t){return St(t)||xt(t)===ut},tt.isConcurrentMode=St,tt.isContextConsumer=function(t){return xt(t)===st},tt.isContextProvider=function(t){return xt(t)===at},tt.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===nt},tt.isForwardRef=function(t){return xt(t)===ft},tt.isFragment=function(t){return xt(t)===ot},tt.isLazy=function(t){return xt(t)===dt},tt.isMemo=function(t){return xt(t)===mt},tt.isPortal=function(t){return xt(t)===rt},tt.isProfiler=function(t){return xt(t)===ct},tt.isStrictMode=function(t){return xt(t)===it},tt.isSuspense=function(t){return xt(t)===lt},tt.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===ot||t===pt||t===ct||t===it||t===lt||t===yt||"object"==typeof t&&null!==t&&(t.$$typeof===dt||t.$$typeof===mt||t.$$typeof===at||t.$$typeof===st||t.$$typeof===ft||t.$$typeof===vt||t.$$typeof===bt||t.$$typeof===gt||t.$$typeof===ht)},tt.typeOf=xt,Z.exports=tt;var $t=Z.exports,Ct={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},wt={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},_t={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Et={};function Pt(t){return $t.isMemo(t)?_t:Et[t.$$typeof]||Ct}Et[$t.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Et[$t.Memo]=_t;var Mt=Object.defineProperty,Ot=Object.getOwnPropertyNames,Rt=Object.getOwnPropertySymbols,Tt=Object.getOwnPropertyDescriptor,jt=Object.getPrototypeOf,At=Object.prototype,Ut=function t(e,n,r){if("string"!=typeof n){if(At){var o=jt(n);o&&o!==At&&t(e,o,r)}var i=Ot(n);Rt&&(i=i.concat(Rt(n)));for(var c=Pt(e),a=Pt(n),s=0;s<i.length;++s){var u=i[s];if(!(wt[u]||r&&r[u]||a&&a[u]||c&&c[u])){var p=Tt(n,u);try{Mt(e,u,p)}catch(ot){}}}}return e};const kt=n(Ut);var Ft=1073741823,Lt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},Dt=r.createContext||function(t,e){var n,o,i,c="__create-react-context-"+(Lt[i="__global_unique_id__"]=(Lt[i]||0)+1)+"__",a=function(t){function n(){for(var e,n,r,o=arguments.length,i=new Array(o),c=0;c<o;c++)i[c]=arguments[c];return(e=t.call.apply(t,[this].concat(i))||this).emitter=(n=e.props.value,r=[],{on:function(t){r.push(t)},off:function(t){r=r.filter(function(e){return e!==t})},get:function(){return n},set:function(t,e){n=t,r.forEach(function(t){return t(n,e)})}}),e}s(n,t);var r=n.prototype;return r.getChildContext=function(){var t;return(t={})[c]=this.emitter,t},r.componentWillReceiveProps=function(t){if(this.props.value!==t.value){var n,r=this.props.value,o=t.value;!function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}(r,o)?(n="function"==typeof e?e(r,o):Ft,0!=(n|=0)&&this.emitter.set(t.value,n)):n=0}},r.render=function(){return this.props.children},n}(r.Component);a.childContextTypes=((n={})[c]=y.object.isRequired,n);var u=function(e){function n(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).observedBits=void 0,t.state={value:t.getValue()},t.onUpdate=function(e,n){(0|t.observedBits)&n&&t.setState({value:t.getValue()})},t}s(n,e);var r=n.prototype;return r.componentWillReceiveProps=function(t){var e=t.observedBits;this.observedBits=null==e?Ft:e},r.componentDidMount=function(){this.context[c]&&this.context[c].on(this.onUpdate);var t=this.props.observedBits;this.observedBits=null==t?Ft:t},r.componentWillUnmount=function(){this.context[c]&&this.context[c].off(this.onUpdate)},r.getValue=function(){return this.context[c]?this.context[c].get():t},r.render=function(){return(t=this.props.children,Array.isArray(t)?t[0]:t)(this.state.value);var t},n}(r.Component);return u.contextTypes=((o={})[c]=y.object,o),{Provider:a,Consumer:u}},Nt=function(t){var e=Dt();return e.displayName=t,e},Wt=Nt("Router-History"),Bt=Nt("Router");t("a",function(t){function e(e){var n;return(n=t.call(this,e)||this).state={location:e.history.location},n._isMounted=!1,n._pendingLocation=null,e.staticContext||(n.unlisten=e.history.listen(function(t){n._pendingLocation=t})),n}s(e,t),e.computeRootMatch=function(t){return{path:"/",url:"/",params:{},isExact:"/"===t}};var n=e.prototype;return n.componentDidMount=function(){var t=this;this._isMounted=!0,this.unlisten&&this.unlisten(),this.props.staticContext||(this.unlisten=this.props.history.listen(function(e){t._isMounted&&t.setState({location:e})})),this._pendingLocation&&this.setState({location:this._pendingLocation})},n.componentWillUnmount=function(){this.unlisten&&(this.unlisten(),this._isMounted=!1,this._pendingLocation=null)},n.render=function(){return r.createElement(Bt.Provider,{value:{history:this.props.history,location:this.state.location,match:e.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}},r.createElement(Wt.Provider,{children:this.props.children||null,value:this.props.history}))},e}(r.Component)),r.Component;var It=function(t){function e(){return t.apply(this,arguments)||this}s(e,t);var n=e.prototype;return n.componentDidMount=function(){this.props.onMount&&this.props.onMount.call(this,this)},n.componentDidUpdate=function(t){this.props.onUpdate&&this.props.onUpdate.call(this,this,t)},n.componentWillUnmount=function(){this.props.onUnmount&&this.props.onUnmount.call(this,this)},n.render=function(){return null},e}(r.Component),zt={},Vt=1e4,qt=0;function Ht(t,e){return void 0===t&&(t="/"),void 0===e&&(e={}),"/"===t?t:function(t){if(zt[t])return zt[t];var e=O.compile(t);return qt<Vt&&(zt[t]=e,qt++),e}(t)(e,{pretty:!0})}var Jt={},Yt=1e4,Gt=0;function Kt(t,e){void 0===e&&(e={}),("string"==typeof e||Array.isArray(e))&&(e={path:e});var n=e,r=n.path,o=n.exact,i=void 0!==o&&o,c=n.strict,a=void 0!==c&&c,s=n.sensitive,u=void 0!==s&&s;return[].concat(r).reduce(function(e,n){if(!n&&""!==n)return null;if(e)return e;var r=function(t,e){var n=""+e.end+e.strict+e.sensitive,r=Jt[n]||(Jt[n]={});if(r[t])return r[t];var o=[],i={regexp:O(t,o,e),keys:o};return Gt<Yt&&(r[t]=i,Gt++),i}(n,{end:i,strict:a,sensitive:u}),o=r.regexp,c=r.keys,s=o.exec(t);if(!s)return null;var p=s[0],f=s.slice(1),l=t===p;return i&&!l?null:{path:n,url:"/"===n&&""===p?"/":p,isExact:l,params:c.reduce(function(t,e,n){return t[e.name]=f[n],t},{})}},null)}t("R",function(t){function e(){return t.apply(this,arguments)||this}return s(e,t),e.prototype.render=function(){var t=this;return r.createElement(Bt.Consumer,null,function(e){e||o();var n=t.props.location||e.location,c=t.props.computedMatch?t.props.computedMatch:t.props.path?Kt(n.pathname,t.props):e.match,a=i({},e,{location:n,match:c}),s=t.props,u=s.children,p=s.component,f=s.render;return Array.isArray(u)&&function(t){return 0===r.Children.count(t)}(u)&&(u=null),r.createElement(Bt.Provider,{value:a},a.match?u?"function"==typeof u?u(a):u:p?r.createElement(p,a):f?f(a):null:"function"==typeof u?u(a):null)})},e}(r.Component)),r.Component,r.Component;var Qt=r.useContext}}});

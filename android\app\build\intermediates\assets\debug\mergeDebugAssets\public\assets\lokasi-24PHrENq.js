import{f as t}from"./networkOptimizer-CUziElFO.js";async function s(){try{const s=JSON.parse(localStorage.getItem("user")||"{}").lokasi_id;if(!s)return;const a=await t("https://absensiku.trunois.my.id/api/lokasi.php?api_key=absensiku_api_key_2023",{},18e5);if("success"===a.status&&Array.isArray(a.data)){const t=a.data.filter(t=>t.id==s);localStorage.setItem("lokasi_list",JSON.stringify(t))}}catch(s){}}export{s as fetchAndStoreLokasi};

.histori-header{padding:16px}.histori-user-info{display:flex;align-items:center;gap:16px}.histori-user-icon{font-size:2rem;color:var(--ion-color-primary)}.histori-user-info h3{margin:0 0 4px;font-size:1.2rem;font-weight:600}.histori-user-info p{margin:0;color:var(--ion-color-medium);font-size:.9rem}.histori-filter-summary{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;background:var(--ion-color-light);margin:0 16px 16px;border-radius:8px}.histori-loading{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;text-align:center}.histori-loading p{margin-top:16px;color:var(--ion-color-medium)}.histori-error,.histori-empty{padding:16px}.histori-content{padding:0 16px 16px}.histori-card{margin-bottom:16px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,.1)}.histori-card-title{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:8px}.histori-date{display:flex;align-items:center;gap:8px;font-size:1rem;font-weight:600}.histori-date ion-icon{color:var(--ion-color-primary)}.histori-time-info{display:flex;flex-direction:column;gap:12px}.histori-time-item{display:flex;align-items:flex-start;gap:12px}.histori-time-item ion-icon{margin-top:2px;flex-shrink:0}.histori-time-item>div{flex:1}.histori-time-item strong{color:var(--ion-color-dark)}.histori-keterangan{margin-top:12px;padding:8px 12px;background:var(--ion-color-light);border-radius:6px;font-size:.9rem}.histori-filter-content{padding:16px}.histori-filter-actions{margin-top:24px;display:flex;flex-direction:column;gap:12px}.histori-image-modal{display:flex;align-items:center;justify-content:center;min-height:300px;padding:20px}.histori-image-modal ion-img{max-width:100%;max-height:80vh;object-fit:contain;border-radius:8px;box-shadow:0 4px 16px rgba(0,0,0,.2)}@media (max-width: 576px){.histori-card-title{flex-direction:column;align-items:flex-start}.histori-time-item{flex-direction:column;gap:8px}.histori-time-item ion-icon{margin-top:0}}.histori-card{transition:transform .2s ease,box-shadow .2s ease}.histori-card:hover{transform:translateY(-2px);box-shadow:0 4px 16px rgba(0,0,0,.15)}.histori-card ion-badge{display:flex;align-items:center;gap:4px;font-size:.8rem;padding:4px 8px}.histori-time-item ion-button{--padding-start: 8px;--padding-end: 8px;height:24px;font-size:.7rem;margin-top:4px}@keyframes pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.histori-loading ion-spinner{animation:pulse 1.5s ease-in-out infinite}

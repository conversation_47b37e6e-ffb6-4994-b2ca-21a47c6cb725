System.register(["./ionic-legacy-DbGqp7zN.js","./utils-legacy-DvNNcox0.js","./react-vendor-legacy-wCcNgjsd.js","./capacitor-legacy-cVgeOc-7.js"],function(e,s){"use strict";var n,a,i,r,t,c,o,l,d,u,m,h,k,g,x,y;return{setters:[e=>{n=e.r,a=e.j,i=e.I,r=e.J,t=e.K,c=e.L,o=e.h,l=e.k,d=e.m,u=e.n,m=e.p,h=e.v,k=e.N,g=e.O,x=e.w},e=>{y=e.a},null,null],execute:function(){e("default",()=>{const[e,s]=n.useState(!1),[j,p]=n.useState(!1),[S,f]=n.useState(""),[b,w]=n.useState(!1),[P,v]=n.useState(""),[B,O]=n.useState("success"),[T,$]=n.useState("");return a.jsxs(i,{children:[a.jsx(r,{children:a.jsx(t,{children:a.jsx(c,{children:"Barcode Scanner Test"})})}),a.jsxs(o,{className:"ion-padding",children:[a.jsxs(l,{children:[a.jsx(d,{children:a.jsx(u,{children:"Test Barcode Scanner"})}),a.jsxs(m,{children:[a.jsx(h,{expand:"block",onClick:async()=>{try{if(s(!0),!y)throw new Error("BarcodeScanner plugin tidak tersedia");const e=await y.checkPermission({force:!0});if(!e.granted)return e.denied?f("Permission kamera ditolak. Silakan aktifkan di pengaturan aplikasi."):f("Permission kamera diperlukan untuk scan barcode"),void p(!0);document.body.classList.add("scanner-active");const n=await y.startScan();n.hasContent?($(n.content),v(`Berhasil scan: ${n.content}`),O("success"),w(!0)):(v("Scan dibatalkan atau tidak ada hasil"),O("warning"),w(!0))}catch(e){v(`Error: ${e.message||"Gagal melakukan scan barcode"}`),O("danger"),w(!0)}finally{s(!1),document.body.classList.remove("scanner-active");try{await y.stopScan()}catch(n){}}},disabled:e,color:"primary",style:{marginBottom:"10px"},children:e?"Scanning...":"Test Full Scan"}),a.jsx(h,{expand:"block",onClick:async()=>{try{const e=await y.checkPermission({force:!1});f(`Permission status: ${JSON.stringify(e,null,2)}`),p(!0)}catch(e){f(`Error checking permission: ${e.message}`),p(!0)}},fill:"outline",color:"secondary",style:{marginBottom:"10px"},children:"Check Permission Only"}),a.jsx(h,{expand:"block",onClick:async()=>{try{const e=await y.checkPermission({force:!0});f(`Permission request result: ${JSON.stringify(e,null,2)}`),p(!0)}catch(e){f(`Error requesting permission: ${e.message}`),p(!0)}},fill:"outline",color:"tertiary",style:{marginBottom:"10px"},children:"Request Permission"}),T&&a.jsx("div",{style:{marginTop:"20px"},children:a.jsxs(k,{color:"success",children:[a.jsx("h3",{children:"Hasil Scan Terakhir:"}),a.jsx("p",{children:T})]})})]})]}),a.jsx(g,{isOpen:j,onDidDismiss:()=>p(!1),header:"Test Result",message:S,buttons:["OK"]}),a.jsx(x,{isOpen:b,onDidDismiss:()=>w(!1),message:P,duration:3e3,color:B})]})]})})}}});

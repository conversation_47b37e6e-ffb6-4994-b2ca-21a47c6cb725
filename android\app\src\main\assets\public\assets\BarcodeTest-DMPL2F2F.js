import{r as s,j as a,I as e,J as n,K as i,L as c,h as r,k as t,m as o,n as l,p as d,v as m,N as u,O as h,w as k}from"./ionic-CJlrxXsE.js";import{a as p}from"./utils-W2Gk7u7g.js";import"./react-vendor-DCX9i6UF.js";import"./capacitor-DGgumwVn.js";const x=()=>{const[x,j]=s.useState(!1),[g,y]=s.useState(!1),[S,f]=s.useState(""),[b,w]=s.useState(!1),[P,v]=s.useState(""),[B,O]=s.useState("success"),[T,C]=s.useState("");return a.jsxs(e,{children:[a.jsx(n,{children:a.jsx(i,{children:a.jsx(c,{children:"Barcode Scanner Test"})})}),a.jsxs(r,{className:"ion-padding",children:[a.jsxs(t,{children:[a.jsx(o,{children:a.jsx(l,{children:"Test Barcode Scanner"})}),a.jsxs(d,{children:[a.jsx(m,{expand:"block",onClick:async()=>{try{if(j(!0),!p)throw new Error("BarcodeScanner plugin tidak tersedia");const s=await p.checkPermission({force:!0});if(!s.granted)return s.denied?f("Permission kamera ditolak. Silakan aktifkan di pengaturan aplikasi."):f("Permission kamera diperlukan untuk scan barcode"),void y(!0);document.body.classList.add("scanner-active");const a=await p.startScan();a.hasContent?(C(a.content),v("Berhasil scan: ".concat(a.content)),O("success"),w(!0)):(v("Scan dibatalkan atau tidak ada hasil"),O("warning"),w(!0))}catch(s){v("Error: ".concat(s.message||"Gagal melakukan scan barcode")),O("danger"),w(!0)}finally{j(!1),document.body.classList.remove("scanner-active");try{await p.stopScan()}catch(a){}}},disabled:x,color:"primary",style:{marginBottom:"10px"},children:x?"Scanning...":"Test Full Scan"}),a.jsx(m,{expand:"block",onClick:async()=>{try{const s=await p.checkPermission({force:!1});f("Permission status: ".concat(JSON.stringify(s,null,2))),y(!0)}catch(s){f("Error checking permission: ".concat(s.message)),y(!0)}},fill:"outline",color:"secondary",style:{marginBottom:"10px"},children:"Check Permission Only"}),a.jsx(m,{expand:"block",onClick:async()=>{try{const s=await p.checkPermission({force:!0});f("Permission request result: ".concat(JSON.stringify(s,null,2))),y(!0)}catch(s){f("Error requesting permission: ".concat(s.message)),y(!0)}},fill:"outline",color:"tertiary",style:{marginBottom:"10px"},children:"Request Permission"}),T&&a.jsx("div",{style:{marginTop:"20px"},children:a.jsxs(u,{color:"success",children:[a.jsx("h3",{children:"Hasil Scan Terakhir:"}),a.jsx("p",{children:T})]})})]})]}),a.jsx(h,{isOpen:g,onDidDismiss:()=>y(!1),header:"Test Result",message:S,buttons:["OK"]}),a.jsx(k,{isOpen:b,onDidDismiss:()=>w(!1),message:P,duration:3e3,color:B})]})]})};export{x as default};

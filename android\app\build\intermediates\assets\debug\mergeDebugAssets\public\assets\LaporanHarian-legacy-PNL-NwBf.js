System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(e,a){"use strict";var n,i,t,r,o,s,l,d,c,p,g,u,m,h,x,j,y,f,k,b,v,w,S,C,T,D,I,F,L,_,z,E,A,H,O,R,B,M,P,K;return{setters:[e=>{n=e.r,i=e.D,t=e.j,r=e.E,o=e.F,s=e.Z,l=e.s,d=e.t,c=e.T,p=e.U,g=e.C,u=e.N,m=e.v,h=e.o,x=e.k,j=e.m,y=e.n,f=e.p,k=e.H,b=e.J,v=e.K,w=e.L,S=e.M,C=e.h,T=e.u,D=e.$,I=e.W,F=e.x,L=e.I,_=e.P,z=e.a0,E=e.a1},e=>{A=e.r,H=e.q,O=e.x,R=e.g,B=e.h,M=e.y,P=e.z,K=e.j},null,null,null],execute:function(){const a=()=>{const[e,a]=n.useState([]),[T,D]=n.useState([]),[I,F]=n.useState(!0),[L,_]=n.useState(""),[z,E]=n.useState(""),[B,M]=n.useState(null),[P,K]=n.useState(!1),[N]=i(),U=JSON.parse(localStorage.getItem("user")||"{}"),$=async()=>{try{F(!0);const e=await fetch(`https://absensiku.trunois.my.id/api/laporan_harian.php?api_key=absensiku_api_key_2023&nama_karyawan=${encodeURIComponent(U.nama||"")}`),n=await e.json();"success"===n.status?(a(n.data),D(n.data)):N({message:"Gagal memuat data laporan",color:"danger",duration:3e3,position:"top"})}catch(e){N({message:"Terjadi kesalahan saat memuat data",color:"danger",duration:3e3,position:"top"})}finally{F(!1)}},G=e=>new Date(e).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"});return n.useEffect(()=>{$()},[]),n.useEffect(()=>{(()=>{let a=e;L&&(a=a.filter(e=>e.keterangan.toLowerCase().includes(L.toLowerCase())||e.periode.toLowerCase().includes(L.toLowerCase()))),z&&(a=a.filter(e=>e.periode===z)),D(a)})()},[L,z,e]),t.jsxs(t.Fragment,{children:[t.jsx(r,{slot:"fixed",onIonRefresh:async e=>{await $(),e.detail.complete()},children:t.jsx(o,{})}),t.jsxs("div",{style:{marginBottom:"16px"},children:[t.jsx(s,{value:L,onIonInput:e=>_(e.detail.value),placeholder:"Cari laporan berdasarkan keterangan atau bulan...",style:{"--background":"#f8f9fa","--border-radius":"12px"}}),t.jsxs(l,{style:{"--background":"#f8f9fa","--border-radius":"12px",marginTop:"8px"},children:[t.jsx(d,{children:"Filter Bulan:"}),t.jsxs(c,{value:z,onIonChange:e=>E(e.detail.value),placeholder:"Semua Bulan",children:[t.jsx(p,{value:"",children:"Semua Bulan"}),(()=>{const a=new Set(e.map(e=>e.periode));return Array.from(a).sort()})().map(e=>t.jsx(p,{value:e,children:e},e))]})]})]}),I&&t.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[t.jsx(g,{name:"crescent"}),t.jsx("p",{style:{marginTop:"16px",color:"#666"},children:"Memuat laporan..."})]}),!I&&0===T.length&&t.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[t.jsxs(u,{color:"medium",children:[t.jsx("h3",{children:"📋 Belum Ada Laporan"}),t.jsx("p",{children:"Belum ada laporan harian yang dibuat."})]}),t.jsxs(m,{fill:"outline",onClick:$,children:[t.jsx(h,{icon:A,slot:"start"}),"Refresh"]})]}),!I&&T.map(e=>t.jsxs(x,{style:{marginBottom:"16px"},children:[t.jsxs(j,{children:[t.jsx(y,{style:{fontSize:"1.1rem",color:"#1880ff"},children:e.periode}),t.jsx(u,{color:"medium",children:t.jsx("small",{children:G(e.tanggal)})})]}),t.jsxs(f,{children:[t.jsx("p",{style:{margin:"0 0 12px 0",lineHeight:1.5},children:e.keterangan.length>100?e.keterangan.substring(0,100)+"...":e.keterangan}),t.jsxs("div",{style:{display:"flex",gap:"8px",justifyContent:"flex-end"},children:[t.jsxs(m,{size:"small",fill:"outline",color:"primary",onClick:()=>(e=>{M(e),K(!0)})(e),children:[t.jsx(h,{icon:H,slot:"start"}),"Detail"]}),t.jsxs(m,{size:"small",fill:"outline",color:"danger",onClick:()=>(async e=>{try{const a=await fetch("https://absensiku.trunois.my.id/api/laporan_harian.php",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({api_key:"absensiku_api_key_2023",id:e})}),n=await a.json();"success"===n.status?(N({message:"Laporan berhasil dihapus",color:"success",duration:3e3,position:"top"}),$()):N({message:n.message||"Gagal menghapus laporan",color:"danger",duration:3e3,position:"top"})}catch(a){N({message:"Terjadi kesalahan saat menghapus laporan",color:"danger",duration:3e3,position:"top"})}})(e.id),children:[t.jsx(h,{icon:O,slot:"start"}),"Hapus"]})]})]})]},e.id)),t.jsxs(k,{isOpen:P,onDidDismiss:()=>K(!1),children:[t.jsx(b,{children:t.jsxs(v,{children:[t.jsx(w,{children:"Detail Laporan"}),t.jsx(S,{slot:"end",children:t.jsx(m,{onClick:()=>K(!1),children:t.jsx(h,{icon:R})})})]})}),t.jsx(C,{className:"ion-padding",children:B&&t.jsxs("div",{children:[t.jsx("h2",{style:{color:"#1880ff",marginBottom:"8px"},children:B.periode}),t.jsx("p",{style:{color:"#666",marginBottom:"16px"},children:G(B.tanggal)}),t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx("h3",{children:"Keterangan:"}),t.jsx("p",{style:{lineHeight:1.6,whiteSpace:"pre-wrap"},children:B.keterangan})]}),B.foto&&t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx("h3",{children:"Foto:"}),t.jsx("img",{src:(J=B.foto,J?`https://absensiku.trunois.my.id/uploads/${J}`:null),alt:"Foto Laporan",style:{width:"100%",maxHeight:"300px",objectFit:"cover",borderRadius:"8px",border:"1px solid #ddd"},onError:e=>{e.target.style.display="none"}})]}),t.jsxs("div",{style:{fontSize:"0.9rem",color:"#999"},children:[t.jsxs("p",{children:["Dibuat: ",new Date(B.created_at).toLocaleString("id-ID")]}),B.updated_at!==B.created_at&&t.jsxs("p",{children:["Diupdate: ",new Date(B.updated_at).toLocaleString("id-ID")]})]})]})})]})]});var J},N=({onLaporanAdded:e})=>{const a=()=>{const e=new Date;return`${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][e.getMonth()]} ${e.getFullYear()}`},[r,o]=n.useState({periode:a(),tanggal:(new Date).toISOString().split("T")[0],keterangan:""}),[s,c]=n.useState(null),[p,g]=n.useState(!1),[k]=i(),b=n.useRef(null),v=n.useRef(null),w=n.useRef(null),[S,C]=n.useState(!1),[L,_]=n.useState(null),z=JSON.parse(localStorage.getItem("user")||"{}");n.useEffect(()=>()=>{O()},[]);const{minDate:E,maxDate:A}=(()=>{const e=new Date,a=e.getFullYear(),n=e.getMonth();return{minDate:new Date(a,n,1).toISOString().split("T")[0],maxDate:new Date(a,n+1,0).toISOString().split("T")[0]}})(),H=async()=>{try{if(_(null),"https:"!==location.protocol&&"localhost"!==location.hostname)throw new Error("Camera requires HTTPS or localhost");if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw new Error("Camera API not supported on this device/browser");try{if("denied"===(await navigator.permissions.query({name:"camera"})).state)throw new Error("Camera permission denied. Please allow camera access in browser settings.")}catch(e){}C(!0);let n=null;const i=[{video:{facingMode:{exact:"environment"},width:{ideal:1280},height:{ideal:720}}},{video:{facingMode:"environment",width:{ideal:640},height:{ideal:480}}},{video:{facingMode:"user",width:{ideal:640},height:{ideal:480}}},{video:{width:{ideal:640},height:{ideal:480}}},{video:!0}];for(let e=0;e<i.length;e++)try{n=await navigator.mediaDevices.getUserMedia(i[e]);break}catch(a){if(e===i.length-1)throw a}if(!n||!b.current)throw new Error("Failed to get video stream or video element not available");b.current.srcObject=n,b.current.onloadedmetadata=()=>{b.current&&b.current.play().then(()=>{}).catch(e=>{})},b.current.oncanplay=()=>{},b.current.onerror=e=>{_("Video stream error"),O()}}catch(n){C(!1);let e="Tidak dapat mengakses kamera.";"NotAllowedError"===n.name||n.message.includes("permission")?e="Izin kamera ditolak. Silakan izinkan akses kamera di pengaturan browser.":"NotFoundError"===n.name?e="Kamera tidak ditemukan pada perangkat ini.":"NotSupportedError"===n.name?e="Kamera tidak didukung pada browser ini.":n.message.includes("HTTPS")&&(e="Kamera memerlukan koneksi HTTPS yang aman."),_(e),k({message:`${e} Silakan gunakan upload file sebagai alternatif.`,color:"warning",duration:5e3,position:"top"})}},O=()=>{try{if(b.current&&b.current.srcObject){const e=b.current.srcObject;e.getTracks().forEach(e=>{e.stop()}),b.current.srcObject=null,b.current.onloadedmetadata=null,b.current.oncanplay=null,b.current.onerror=null}C(!1),_(null)}catch(e){C(!1)}},R=()=>{w.current&&w.current.click()};return t.jsxs(t.Fragment,{children:[t.jsxs(x,{children:[t.jsx(j,{children:t.jsx(y,{style:{color:"#1880ff"},children:"➕ Tambah Laporan Harian"})}),t.jsxs(f,{children:[t.jsxs(l,{children:[t.jsx(d,{position:"stacked",children:"Periode Bulan *"}),t.jsx(T,{value:r.periode,readonly:!0,placeholder:"Periode bulan laporan",style:{"--color":"#666","--placeholder-color":"#999",cursor:"not-allowed"}}),t.jsx(D,{slot:"helper",color:"medium",children:"📅 Periode otomatis sesuai bulan saat ini"})]}),t.jsxs(l,{children:[t.jsx(d,{position:"stacked",children:"Tanggal *"}),t.jsx(T,{type:"date",value:r.tanggal,min:E,max:A,onIonInput:e=>o({...r,tanggal:e.detail.value})}),t.jsxs(D,{slot:"helper",color:"medium",children:["📅 Hanya bisa memilih tanggal di bulan ini (",E," s/d ",A,")"]})]}),t.jsxs(l,{children:[t.jsx(d,{position:"stacked",children:"Keterangan Kegiatan *"}),t.jsx(I,{value:r.keterangan,onIonInput:e=>o({...r,keterangan:e.detail.value}),placeholder:"Deskripsikan kegiatan yang dilakukan...",rows:4,maxlength:1e3})]}),t.jsxs("div",{style:{margin:"20px 0"},children:[t.jsx(u,{children:t.jsx("h3",{style:{margin:"0 0 12px 0"},children:"📷 Foto Kegiatan (Opsional)"})}),!s&&!S&&t.jsxs("div",{children:[t.jsxs("div",{style:{display:"flex",gap:"8px",margin:"12px 0"},children:[t.jsxs(m,{expand:"block",fill:"outline",onClick:H,style:{flex:1},children:[t.jsx(h,{icon:B,slot:"start"}),"Ambil Foto"]}),t.jsxs(m,{expand:"block",fill:"outline",color:"secondary",onClick:R,style:{flex:1},children:[t.jsx(h,{icon:M,slot:"start"}),"Upload File"]})]}),t.jsx("div",{style:{fontSize:"0.8rem",color:"#666",textAlign:"center",margin:"8px 0"},children:"💡 Tips: Pastikan aplikasi dizinkan akses kamera"})]}),t.jsx("input",{ref:w,type:"file",accept:"image/*",onChange:e=>{const a=e.target.files?.[0];if(a){if(!a.type.startsWith("image/"))return void k({message:"Hanya file gambar yang diperbolehkan",color:"warning",duration:3e3,position:"top"});if(a.size>5242880)return void k({message:"Ukuran file maksimal 5MB",color:"warning",duration:3e3,position:"top"});const e=new FileReader;e.onload=e=>{e.target?.result&&(c(e.target.result),O())},e.onerror=()=>{k({message:"Gagal membaca file",color:"danger",duration:3e3,position:"top"})},e.readAsDataURL(a)}e.target&&(e.target.value="")},style:{display:"none"}}),L&&!S&&t.jsxs("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px",borderRadius:"8px",margin:"12px 0",fontSize:"0.9rem"},children:[t.jsxs("div",{style:{marginBottom:"8px"},children:["⚠️ ",L]}),t.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"8px"},children:[L.includes("izin")||L.includes("permission")?t.jsx(m,{size:"small",fill:"outline",color:"warning",onClick:async()=>{try{(await navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(e=>e.stop()),k({message:"Izin kamera berhasil diberikan! Silakan coba ambil foto lagi.",color:"success",duration:3e3,position:"top"}),setTimeout(()=>{H()},500)}catch(e){k({message:"Gagal mendapatkan izin kamera. Silakan izinkan akses kamera di pengaturan browser.",color:"danger",duration:5e3,position:"top"})}},children:"🔓 Minta Izin Kamera"}):t.jsx(m,{size:"small",fill:"outline",color:"warning",onClick:H,children:"🔄 Coba Lagi"}),t.jsx(m,{size:"small",fill:"outline",color:"secondary",onClick:R,children:"📁 Upload File"})]})]}),S&&t.jsxs("div",{style:{marginBottom:"16px"},children:[t.jsxs("div",{style:{position:"relative"},children:[t.jsx("video",{ref:b,autoPlay:!0,playsInline:!0,muted:!0,style:{width:"100%",height:"250px",borderRadius:"8px",background:"#000",objectFit:"cover",display:"block"},onLoadedMetadata:()=>{},onCanPlay:()=>{},onError:e=>{_("Video stream error"),O()}}),S&&!b.current?.srcObject&&t.jsx("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:"white",background:"rgba(0,0,0,0.8)",padding:"12px 20px",borderRadius:"8px",fontSize:"0.9rem",textAlign:"center"}})]}),t.jsx("canvas",{ref:v,style:{display:"none"}}),t.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"12px"},children:[t.jsxs(m,{expand:"block",onClick:()=>{try{if(b.current&&v.current){const e=b.current,a=v.current;if(e.readyState!==e.HAVE_ENOUGH_DATA)return void k({message:"Kamera belum siap. Tunggu sebentar dan coba lagi.",color:"warning",duration:3e3,position:"top"});a.width=e.videoWidth||640,a.height=e.videoHeight||480;const n=a.getContext("2d");if(n){n.drawImage(e,0,0,a.width,a.height);const i=a.toDataURL("image/jpeg",.8);c(i),k({message:"Foto berhasil diambil!",color:"success",duration:2e3,position:"top"})}O()}}catch(e){k({message:"Gagal mengambil foto. Coba lagi atau gunakan upload file.",color:"danger",duration:3e3,position:"top"})}},style:{flex:1},children:[t.jsx(h,{icon:B,slot:"start"}),"Ambil Foto"]}),t.jsx(m,{expand:"block",fill:"outline",color:"medium",onClick:O,style:{flex:1},children:"Batal"}),t.jsxs(m,{expand:"block",fill:"outline",color:"secondary",onClick:R,style:{flex:1},children:[t.jsx(h,{icon:P,slot:"start"}),"File"]})]})]}),s&&t.jsxs("div",{style:{marginBottom:"16px"},children:[t.jsx("img",{src:s,alt:"Foto Kegiatan",style:{width:"100%",maxHeight:"250px",borderRadius:"8px",objectFit:"cover",border:"1px solid #ddd"},onError:e=>{k({message:"Gagal memuat gambar",color:"danger",duration:3e3,position:"top"})}}),t.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"12px"},children:[t.jsxs(m,{expand:"block",fill:"outline",onClick:()=>{c(null),H()},style:{flex:1},children:[t.jsx(h,{icon:B,slot:"start"}),"Kamera"]}),t.jsxs(m,{expand:"block",fill:"outline",color:"secondary",onClick:R,style:{flex:1},children:[t.jsx(h,{icon:M,slot:"start"}),"Ganti File"]}),t.jsx(m,{expand:"block",fill:"outline",color:"danger",onClick:()=>{c(null),O()},style:{flex:1},children:"Hapus"})]})]})]}),t.jsxs(m,{expand:"block",onClick:async()=>{if(!r.periode)return void k({message:"Periode bulan wajib dipilih",color:"warning",duration:3e3,position:"top"});if(!r.tanggal)return void k({message:"Tanggal wajib diisi",color:"warning",duration:3e3,position:"top"});const n=r.tanggal;if(n<E||n>A)k({message:`Tanggal harus dalam bulan ini (${E} s/d ${A})`,color:"warning",duration:4e3,position:"top"});else if(r.keterangan.trim())if(z.nama)if(r.keterangan.trim().length<10)k({message:"Keterangan minimal 10 karakter",color:"warning",duration:3e3,position:"top"});else{g(!0);try{let n="";if(s)try{s.startsWith("data:image/")?n=s:k({message:"Format foto tidak valid, laporan akan dikirim tanpa foto",color:"warning",duration:3e3,position:"top"})}catch(i){k({message:"Ada masalah dengan foto, laporan akan dikirim tanpa foto",color:"warning",duration:3e3,position:"top"})}const l={api_key:"absensiku_api_key_2023",nama_karyawan:z.nama,periode:r.periode,tanggal:r.tanggal,keterangan:r.keterangan.trim(),foto:n},d=await fetch("https://absensiku.trunois.my.id/api/laporan_harian.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!d.ok)return await d.text(),void k({message:`Server error (${d.status}): ${d.statusText}`,color:"danger",duration:5e3,position:"top"});let p;const g=await d.text();try{p=JSON.parse(g)}catch(t){return void k({message:"Server mengembalikan response yang tidak valid. Coba lagi nanti.",color:"danger",duration:5e3,position:"top"})}"success"===p.status?(o({periode:a(),tanggal:(new Date).toISOString().split("T")[0],keterangan:""}),c(null),O(),e()):k({message:p.message||"Gagal menambahkan laporan",color:"danger",duration:3e3,position:"top"})}catch(l){l instanceof TypeError&&l.message.includes("fetch")?k({message:"Tidak dapat terhubung ke server. Periksa koneksi internet Anda.",color:"danger",duration:5e3,position:"top"}):k({message:"Terjadi kesalahan saat mengirim laporan. Coba lagi nanti.",color:"danger",duration:3e3,position:"top"})}finally{g(!1)}}else k({message:"Data user tidak valid. Silakan login ulang.",color:"warning",duration:3e3,position:"top"});else k({message:"Keterangan wajib diisi",color:"warning",duration:3e3,position:"top"})},disabled:p,style:{margin:"10px 0 0 0","--background":"#1880ff","--background-activated":"#005be7"},children:[t.jsx(h,{icon:K,slot:"start"}),p?"Mengirim...":"Simpan Laporan"]})]})]}),t.jsx(F,{isOpen:p,message:"Menyimpan laporan...",duration:0})]})},U={background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},$={color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center",marginRight:"50px"};e("default",()=>{const[e,r]=n.useState("riwayat"),[o]=i();return t.jsxs(L,{children:[t.jsx(b,{style:U,children:t.jsxs(v,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[t.jsx(S,{slot:"start",children:t.jsx(_,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),t.jsx(w,{style:$,children:"Laporan Harian"})]})}),t.jsxs(C,{fullscreen:!0,children:[t.jsx("div",{style:{padding:"16px 16px 0 16px"},children:t.jsxs(z,{value:e,onIonChange:e=>r(e.detail.value),style:{background:"#f8f9fa",borderRadius:"12px",padding:"4px"},children:[t.jsx(E,{value:"riwayat",children:t.jsx(d,{style:{fontWeight:600},children:"📋 Riwayat"})}),t.jsx(E,{value:"tambah",children:t.jsx(d,{style:{fontWeight:600},children:"➕ Tambah"})})]})}),t.jsxs("div",{style:{padding:"16px"},children:["riwayat"===e&&t.jsx(a,{}),"tambah"===e&&t.jsx(N,{onLaporanAdded:()=>{r("riwayat"),o({message:"Laporan bulanan berhasil ditambahkan!",color:"success",duration:3e3,position:"top"})}})]})]})]})})}}});

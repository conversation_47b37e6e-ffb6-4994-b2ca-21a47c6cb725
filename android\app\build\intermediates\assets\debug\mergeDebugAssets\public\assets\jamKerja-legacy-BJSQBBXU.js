System.register(["./networkOptimizer-legacy-Beqb1gSF.js"],function(t,e){"use strict";var a;return{setters:[t=>{a=t.f}],execute:function(){t("fetchAndStoreJamKerja",async function(){try{const t=JSON.parse(localStorage.getItem("user")||"{}").bidang_id;if(!t)return;const e=await a("https://absensiku.trunois.my.id/api/jam_kerja.php?api_key=absensiku_api_key_2023",{},18e5);if("success"===e.status&&Array.isArray(e.data)){const a=e.data.filter(e=>e.bidang_id==t);localStorage.setItem("jam_kerja_list",JSON.stringify(a))}}catch(t){}})}}});

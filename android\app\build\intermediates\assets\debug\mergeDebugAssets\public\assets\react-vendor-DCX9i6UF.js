import{g as t,R as e,i as r,_ as n,c as o,l as i,a,b as c}from"./ionic-CJlrxXsE.js";function s(t,e){return(s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,s(t,e)}var p={exports:{}};function f(){}function l(){}l.resetWarningCache=f;p.exports=function(){function t(t,e,r,n,o,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:l,resetWarningCache:f};return r.PropTypes=r,r}();const y=t(p.exports);var m={exports:{}},d=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)};m.exports=P,m.exports.parse=v,m.exports.compile=function(t,e){return S(v(t,e),e)},m.exports.tokensToFunction=S,m.exports.tokensToRegExp=E;var h=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function v(t,e){for(var r,n=[],o=0,i=0,a="",c=e&&e.delimiter||"/";null!=(r=h.exec(t));){var s=r[0],u=r[1],p=r.index;if(a+=t.slice(i,p),i=p+s.length,u)a+=u[1];else{var f=t[i],l=r[2],y=r[3],m=r[4],d=r[5],v=r[6],g=r[7];a&&(n.push(a),a="");var x=null!=l&&null!=f&&f!==l,S="+"===v||"*"===v,$="?"===v||"*"===v,w=l||c,_=m||d,E=l||("string"==typeof n[n.length-1]?n[n.length-1]:"");n.push({name:y||o++,prefix:l||"",delimiter:w,optional:$,repeat:S,partial:x,asterisk:!!g,pattern:_?C(_):g?".*":b(w,E)})}}return i<t.length&&(a+=t.substr(i)),a&&n.push(a),n}function b(t,e){return!e||e.indexOf(t)>-1?"[^"+$(t)+"]+?":$(e)+"|(?:(?!"+$(e)+")[^"+$(t)+"])+?"}function g(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function x(t){return encodeURI(t).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function S(t,e){for(var r=new Array(t.length),n=0;n<t.length;n++)"object"==typeof t[n]&&(r[n]=new RegExp("^(?:"+t[n].pattern+")$",_(e)));return function(e,n){for(var o="",i=e||{},a=(n||{}).pretty?g:encodeURIComponent,c=0;c<t.length;c++){var s=t[c];if("string"!=typeof s){var u,p=i[s.name];if(null==p){if(s.optional){s.partial&&(o+=s.prefix);continue}throw new TypeError('Expected "'+s.name+'" to be defined')}if(d(p)){if(!s.repeat)throw new TypeError('Expected "'+s.name+'" to not repeat, but received `'+JSON.stringify(p)+"`");if(0===p.length){if(s.optional)continue;throw new TypeError('Expected "'+s.name+'" to not be empty')}for(var f=0;f<p.length;f++){if(u=a(p[f]),!r[c].test(u))throw new TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but received `'+JSON.stringify(u)+"`");o+=(0===f?s.prefix:s.delimiter)+u}}else{if(u=s.asterisk?x(p):a(p),!r[c].test(u))throw new TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but received "'+u+'"');o+=s.prefix+u}}else o+=s}return o}}function $(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function C(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function w(t,e){return t.keys=e,t}function _(t){return t&&t.sensitive?"":"i"}function E(t,e,r){d(e)||(r=e||r,e=[]);for(var n=(r=r||{}).strict,o=!1!==r.end,i="",a=0;a<t.length;a++){var c=t[a];if("string"==typeof c)i+=$(c);else{var s=$(c.prefix),u="(?:"+c.pattern+")";e.push(c),c.repeat&&(u+="(?:"+s+u+")*"),i+=u=c.optional?c.partial?s+"("+u+")?":"(?:"+s+"("+u+"))?":s+"("+u+")"}}var p=$(r.delimiter||"/"),f=i.slice(-p.length)===p;return n||(i=(f?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=o?"$":n&&f?"":"(?="+p+"|$)",w(new RegExp("^"+i,_(r)),e)}function P(t,e,r){return d(e)||(r=e||r,e=[]),r=r||{},t instanceof RegExp?function(t,e){var r=t.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)e.push({name:n,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return w(t,e)}(t,e):d(t)?function(t,e,r){for(var n=[],o=0;o<t.length;o++)n.push(P(t[o],e,r).source);return w(new RegExp("(?:"+n.join("|")+")",_(r)),e)}(t,e,r):function(t,e,r){return E(v(t,r),e,r)}(t,e,r)}const M=t(m.exports);var O={},R="function"==typeof Symbol&&Symbol.for,T=R?Symbol.for("react.element"):60103,j=R?Symbol.for("react.portal"):60106,A=R?Symbol.for("react.fragment"):60107,U=R?Symbol.for("react.strict_mode"):60108,k=R?Symbol.for("react.profiler"):60114,F=R?Symbol.for("react.provider"):60109,L=R?Symbol.for("react.context"):60110,D=R?Symbol.for("react.async_mode"):60111,N=R?Symbol.for("react.concurrent_mode"):60111,W=R?Symbol.for("react.forward_ref"):60112,B=R?Symbol.for("react.suspense"):60113,I=R?Symbol.for("react.suspense_list"):60120,z=R?Symbol.for("react.memo"):60115,V=R?Symbol.for("react.lazy"):60116,q=R?Symbol.for("react.block"):60121,H=R?Symbol.for("react.fundamental"):60117,J=R?Symbol.for("react.responder"):60118,Y=R?Symbol.for("react.scope"):60119;
/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function G(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case T:switch(t=t.type){case D:case N:case A:case k:case U:case B:return t;default:switch(t=t&&t.$$typeof){case L:case W:case V:case z:case F:return t;default:return e}}case j:return e}}}function K(t){return G(t)===N}function Q(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}O.AsyncMode=D,O.ConcurrentMode=N,O.ContextConsumer=L,O.ContextProvider=F,O.Element=T,O.ForwardRef=W,O.Fragment=A,O.Lazy=V,O.Memo=z,O.Portal=j,O.Profiler=k,O.StrictMode=U,O.Suspense=B,O.isAsyncMode=function(t){return K(t)||G(t)===D},O.isConcurrentMode=K,O.isContextConsumer=function(t){return G(t)===L},O.isContextProvider=function(t){return G(t)===F},O.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===T},O.isForwardRef=function(t){return G(t)===W},O.isFragment=function(t){return G(t)===A},O.isLazy=function(t){return G(t)===V},O.isMemo=function(t){return G(t)===z},O.isPortal=function(t){return G(t)===j},O.isProfiler=function(t){return G(t)===k},O.isStrictMode=function(t){return G(t)===U},O.isSuspense=function(t){return G(t)===B},O.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===A||t===N||t===k||t===U||t===B||t===I||"object"==typeof t&&null!==t&&(t.$$typeof===V||t.$$typeof===z||t.$$typeof===F||t.$$typeof===L||t.$$typeof===W||t.$$typeof===H||t.$$typeof===J||t.$$typeof===Y||t.$$typeof===q)},O.typeOf=G;var X={exports:{}},Z={},tt="function"==typeof Symbol&&Symbol.for,et=tt?Symbol.for("react.element"):60103,rt=tt?Symbol.for("react.portal"):60106,nt=tt?Symbol.for("react.fragment"):60107,ot=tt?Symbol.for("react.strict_mode"):60108,it=tt?Symbol.for("react.profiler"):60114,at=tt?Symbol.for("react.provider"):60109,ct=tt?Symbol.for("react.context"):60110,st=tt?Symbol.for("react.async_mode"):60111,ut=tt?Symbol.for("react.concurrent_mode"):60111,pt=tt?Symbol.for("react.forward_ref"):60112,ft=tt?Symbol.for("react.suspense"):60113,lt=tt?Symbol.for("react.suspense_list"):60120,yt=tt?Symbol.for("react.memo"):60115,mt=tt?Symbol.for("react.lazy"):60116,dt=tt?Symbol.for("react.block"):60121,ht=tt?Symbol.for("react.fundamental"):60117,vt=tt?Symbol.for("react.responder"):60118,bt=tt?Symbol.for("react.scope"):60119;function gt(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case et:switch(t=t.type){case st:case ut:case nt:case it:case ot:case ft:return t;default:switch(t=t&&t.$$typeof){case ct:case pt:case mt:case yt:case at:return t;default:return e}}case rt:return e}}}function xt(t){return gt(t)===ut}Z.AsyncMode=st,Z.ConcurrentMode=ut,Z.ContextConsumer=ct,Z.ContextProvider=at,Z.Element=et,Z.ForwardRef=pt,Z.Fragment=nt,Z.Lazy=mt,Z.Memo=yt,Z.Portal=rt,Z.Profiler=it,Z.StrictMode=ot,Z.Suspense=ft,Z.isAsyncMode=function(t){return xt(t)||gt(t)===st},Z.isConcurrentMode=xt,Z.isContextConsumer=function(t){return gt(t)===ct},Z.isContextProvider=function(t){return gt(t)===at},Z.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===et},Z.isForwardRef=function(t){return gt(t)===pt},Z.isFragment=function(t){return gt(t)===nt},Z.isLazy=function(t){return gt(t)===mt},Z.isMemo=function(t){return gt(t)===yt},Z.isPortal=function(t){return gt(t)===rt},Z.isProfiler=function(t){return gt(t)===it},Z.isStrictMode=function(t){return gt(t)===ot},Z.isSuspense=function(t){return gt(t)===ft},Z.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===nt||t===ut||t===it||t===ot||t===ft||t===lt||"object"==typeof t&&null!==t&&(t.$$typeof===mt||t.$$typeof===yt||t.$$typeof===at||t.$$typeof===ct||t.$$typeof===pt||t.$$typeof===ht||t.$$typeof===vt||t.$$typeof===bt||t.$$typeof===dt)},Z.typeOf=gt,X.exports=Z;var St=X.exports,$t={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ct={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},wt={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},_t={};function Et(t){return St.isMemo(t)?wt:_t[t.$$typeof]||$t}_t[St.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},_t[St.Memo]=wt;var Pt=Object.defineProperty,Mt=Object.getOwnPropertyNames,Ot=Object.getOwnPropertySymbols,Rt=Object.getOwnPropertyDescriptor,Tt=Object.getPrototypeOf,jt=Object.prototype;var At=function t(e,r,n){if("string"!=typeof r){if(jt){var o=Tt(r);o&&o!==jt&&t(e,o,n)}var i=Mt(r);Ot&&(i=i.concat(Ot(r)));for(var a=Et(e),c=Et(r),s=0;s<i.length;++s){var u=i[s];if(!(Ct[u]||n&&n[u]||c&&c[u]||a&&a[u])){var p=Rt(r,u);try{Pt(e,u,p)}catch(f){}}}}return e};const Ut=t(At);var kt=1073741823,Ft="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};var Lt=e.createContext||function(t,r){var n,o,i,a="__create-react-context-"+((Ft[i="__global_unique_id__"]=(Ft[i]||0)+1)+"__"),c=function(t){function e(){for(var e,r,n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return(e=t.call.apply(t,[this].concat(i))||this).emitter=(r=e.props.value,n=[],{on:function(t){n.push(t)},off:function(t){n=n.filter(function(e){return e!==t})},get:function(){return r},set:function(t,e){r=t,n.forEach(function(t){return t(r,e)})}}),e}u(e,t);var n=e.prototype;return n.getChildContext=function(){var t;return(t={})[a]=this.emitter,t},n.componentWillReceiveProps=function(t){if(this.props.value!==t.value){var e,n=this.props.value,o=t.value;((i=n)===(a=o)?0!==i||1/i==1/a:i!=i&&a!=a)?e=0:(e="function"==typeof r?r(n,o):kt,0!==(e|=0)&&this.emitter.set(t.value,e))}var i,a},n.render=function(){return this.props.children},e}(e.Component);c.childContextTypes=((n={})[a]=y.object.isRequired,n);var s=function(e){function r(){for(var t,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return(t=e.call.apply(e,[this].concat(n))||this).observedBits=void 0,t.state={value:t.getValue()},t.onUpdate=function(e,r){0!==((0|t.observedBits)&r)&&t.setState({value:t.getValue()})},t}u(r,e);var n=r.prototype;return n.componentWillReceiveProps=function(t){var e=t.observedBits;this.observedBits=null==e?kt:e},n.componentDidMount=function(){this.context[a]&&this.context[a].on(this.onUpdate);var t=this.props.observedBits;this.observedBits=null==t?kt:t},n.componentWillUnmount=function(){this.context[a]&&this.context[a].off(this.onUpdate)},n.getValue=function(){return this.context[a]?this.context[a].get():t},n.render=function(){return(t=this.props.children,Array.isArray(t)?t[0]:t)(this.state.value);var t},r}(e.Component);return s.contextTypes=((o={})[a]=y.object,o),{Provider:c,Consumer:s}},Dt=function(t){var e=Lt();return e.displayName=t,e},Nt=Dt("Router-History"),Wt=Dt("Router"),Bt=function(t){function r(e){var r;return(r=t.call(this,e)||this).state={location:e.history.location},r._isMounted=!1,r._pendingLocation=null,e.staticContext||(r.unlisten=e.history.listen(function(t){r._pendingLocation=t})),r}u(r,t),r.computeRootMatch=function(t){return{path:"/",url:"/",params:{},isExact:"/"===t}};var n=r.prototype;return n.componentDidMount=function(){var t=this;this._isMounted=!0,this.unlisten&&this.unlisten(),this.props.staticContext||(this.unlisten=this.props.history.listen(function(e){t._isMounted&&t.setState({location:e})})),this._pendingLocation&&this.setState({location:this._pendingLocation})},n.componentWillUnmount=function(){this.unlisten&&(this.unlisten(),this._isMounted=!1,this._pendingLocation=null)},n.render=function(){return e.createElement(Wt.Provider,{value:{history:this.props.history,location:this.state.location,match:r.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}},e.createElement(Nt.Provider,{children:this.props.children||null,value:this.props.history}))},r}(e.Component);e.Component;var It=function(t){function e(){return t.apply(this,arguments)||this}u(e,t);var r=e.prototype;return r.componentDidMount=function(){this.props.onMount&&this.props.onMount.call(this,this)},r.componentDidUpdate=function(t){this.props.onUpdate&&this.props.onUpdate.call(this,this,t)},r.componentWillUnmount=function(){this.props.onUnmount&&this.props.onUnmount.call(this,this)},r.render=function(){return null},e}(e.Component),zt={},Vt=0;function qt(t,e){return void 0===t&&(t="/"),void 0===e&&(e={}),"/"===t?t:function(t){if(zt[t])return zt[t];var e=M.compile(t);return Vt<1e4&&(zt[t]=e,Vt++),e}(t)(e,{pretty:!0})}function Ht(t){var a=t.computedMatch,c=t.to,s=t.push,u=void 0!==s&&s;return e.createElement(Wt.Consumer,null,function(t){t||r();var s=t.history,p=t.staticContext,f=u?s.push:s.replace,l=o(a?"string"==typeof c?qt(c,a.params):n({},c,{pathname:qt(c.pathname,a.params)}):c);return p?(f(l),null):e.createElement(It,{onMount:function(){f(l)},onUpdate:function(t,e){var r=o(e.to);i(r,n({},l,{key:r.key}))||f(l)},to:c})})}var Jt={},Yt=0;function Gt(t,e){void 0===e&&(e={}),("string"==typeof e||Array.isArray(e))&&(e={path:e});var r=e,n=r.path,o=r.exact,i=void 0!==o&&o,a=r.strict,c=void 0!==a&&a,s=r.sensitive,u=void 0!==s&&s;return[].concat(n).reduce(function(e,r){if(!r&&""!==r)return null;if(e)return e;var n=function(t,e){var r=""+e.end+e.strict+e.sensitive,n=Jt[r]||(Jt[r]={});if(n[t])return n[t];var o=[],i={regexp:M(t,o,e),keys:o};return Yt<1e4&&(n[t]=i,Yt++),i}(r,{end:i,strict:c,sensitive:u}),o=n.regexp,a=n.keys,s=o.exec(t);if(!s)return null;var p=s[0],f=s.slice(1),l=t===p;return i&&!l?null:{path:r,url:"/"===r&&""===p?"/":p,isExact:l,params:a.reduce(function(t,e,r){return t[e.name]=f[r],t},{})}},null)}var Kt=function(t){function o(){return t.apply(this,arguments)||this}return u(o,t),o.prototype.render=function(){var t=this;return e.createElement(Wt.Consumer,null,function(o){o||r();var i=t.props.location||o.location,a=t.props.computedMatch?t.props.computedMatch:t.props.path?Gt(i.pathname,t.props):o.match,c=n({},o,{location:i,match:a}),s=t.props,u=s.children,p=s.component,f=s.render;return Array.isArray(u)&&function(t){return 0===e.Children.count(t)}(u)&&(u=null),e.createElement(Wt.Provider,{value:c},c.match?u?"function"==typeof u?u(c):u:p?e.createElement(p,c):f?f(c):null:"function"==typeof u?u(c):null)})},o}(e.Component);function Qt(t){var o="withRouter("+(t.displayName||t.name)+")",i=function(o){var i=o.wrappedComponentRef,a=Q(o,["wrappedComponentRef"]);return e.createElement(Wt.Consumer,null,function(o){return o||r(),e.createElement(t,n({},a,o,{ref:i}))})};return i.displayName=o,i.WrappedComponent=t,Ut(i,t)}e.Component,e.Component;var Xt=e.useContext;function Zt(){return Xt(Nt)}export{Kt as R,Bt as a,Ht as b,Gt as m,Zt as u,Qt as w};

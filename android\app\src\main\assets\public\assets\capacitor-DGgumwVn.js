const __vite__fileDeps=["assets/web-CDahzHX1.js","assets/ionic-CJlrxXsE.js","assets/react-vendor-DCX9i6UF.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{d as e}from"./ionic-CJlrxXsE.js";
/*! Capacitor: https://capacitorjs.com/ - MIT License */var t,n;(n=t||(t={})).Unimplemented="UNIMPLEMENTED",n.Unavailable="UNAVAILABLE";class a extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const r=e=>{const n=e.CapacitorCustomPlatform||null,r=e.Capacitor||{},o=r.Plugins=r.Plugins||{},i=()=>null!==n?n.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),s=e=>{var t;return null===(t=r.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},c=new Map;return r.convertFileSrc||(r.convertFileSrc=e=>e),r.getPlatform=i,r.handleError=t=>e.console.error(t),r.isNativePlatform=()=>"web"!==i(),r.isPluginAvailable=e=>{const t=c.get(e);return!!(null==t?void 0:t.platforms.has(i()))||!!s(e)},r.registerPlugin=(e,l={})=>{const d=c.get(e);if(d)return d.proxy;const p=i(),u=s(e);let m;const h=o=>{let i;const s=(...s)=>{const c=(async()=>(!m&&p in l?m=m="function"==typeof l[p]?await l[p]():l[p]:null!==n&&!m&&"web"in l&&(m=m="function"==typeof l.web?await l.web():l.web),m))().then(n=>{const c=((n,o)=>{var i,s;if(!u){if(n)return null===(s=n[o])||void 0===s?void 0:s.bind(n);throw new a('"'.concat(e,'" plugin is not implemented on ').concat(p),t.Unimplemented)}{const t=null==u?void 0:u.methods.find(e=>o===e.name);if(t)return"promise"===t.rtype?t=>r.nativePromise(e,o.toString(),t):(t,n)=>r.nativeCallback(e,o.toString(),t,n);if(n)return null===(i=n[o])||void 0===i?void 0:i.bind(n)}})(n,o);if(c){const e=c(...s);return i=null==e?void 0:e.remove,e}throw new a('"'.concat(e,".").concat(o,'()" is not implemented on ').concat(p),t.Unimplemented)});return"addListener"===o&&(c.remove=async()=>i()),c};return s.toString=()=>"".concat(o.toString(),"() { [capacitor code] }"),Object.defineProperty(s,"name",{value:o,writable:!1,configurable:!1}),s},w=h("addListener"),f=h("removeListener"),g=(e,t)=>{const n=w({eventName:e},t),a=async()=>{const a=await n;f({eventName:e,callbackId:a},t)},r=new Promise(e=>n.then(()=>e({remove:a})));return r.remove=async()=>{await a()},r},y=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return u?g:w;case"removeListener":return f;default:return h(t)}}});return o[e]=y,c.set(e,{name:e,proxy:y,platforms:new Set([...Object.keys(l),...u?[p]:[]])}),y},r.Exception=a,r.DEBUG=!!r.DEBUG,r.isLoggingEnabled=!!r.isLoggingEnabled,r},o=(e=>e.Capacitor=r(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),i=o.registerPlugin;class s{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const a=this.windowListeners[e];a&&!a.registered&&this.addWindowListener(a),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const a=this.listeners[e];if(a)a.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new o.Exception(e,t.Unimplemented)}unavailable(e="not available"){return new o.Exception(e,t.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const a=n.indexOf(t);this.listeners[e].splice(a,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const c=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),l=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class d extends s{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,a]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=l(n).trim(),a=l(a).trim(),t[n]=a}),t}async setCookie(e){try{const t=c(e.key),n=c(e.value),a="; expires=".concat((e.expires||"").replace("expires=","")),r=(e.path||"/").replace("path=",""),o=null!=e.url&&e.url.length>0?"domain=".concat(e.url):"";document.cookie="".concat(t,"=").concat(n||"").concat(a,"; path=").concat(r,"; ").concat(o,";")}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie="".concat(e.key,"=; Max-Age=0")}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,"=;expires=".concat((new Date).toUTCString(),";path=/"))}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}i("CapacitorCookies",{web:()=>new d});const p=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),a=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,a,r)=>(n[a]=e[t[r]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(a.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,a]of Object.entries(e.data||{}))t.set(n,a);n.body=t.toString()}else if(a.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const a=new Headers(n.headers);a.delete("content-type"),n.headers=a}else(a.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class u extends s{async request(e){const t=p(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[a,r]=n;let o,i;return Array.isArray(r)?(i="",r.forEach(e=>{o=t?encodeURIComponent(e):e,i+="".concat(a,"=").concat(o,"&")}),i.slice(0,-1)):(o=t?encodeURIComponent(r):r,i="".concat(a,"=").concat(o)),"".concat(e,"&").concat(i)},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),a=n?"".concat(e.url,"?").concat(n):e.url,r=await fetch(a,t),o=r.headers.get("content-type")||"";let i,s,{responseType:c="text"}=r.ok?e:{};switch(o.includes("application/json")&&(c="json"),c){case"arraybuffer":case"blob":s=await r.blob(),i=await(async e=>new Promise((t,n)=>{const a=new FileReader;a.onload=()=>{const e=a.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},a.onerror=e=>n(e),a.readAsDataURL(e)}))(s);break;case"json":i=await r.json();break;default:i=await r.text()}const l={};return r.headers.forEach((e,t)=>{l[t]=e}),{data:i,headers:l,status:r.status,url:r.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}var m,h,w,f,g,y;i("CapacitorHttp",{web:()=>new u}),(h=m||(m={})).Prompt="PROMPT",h.Camera="CAMERA",h.Photos="PHOTOS",(f=w||(w={})).Rear="REAR",f.Front="FRONT",(y=g||(g={})).Uri="uri",y.Base64="base64",y.DataUrl="dataUrl";class v extends s{async getPhoto(e){return new Promise(async(t,n)=>{if(e.webUseInput||e.source===m.Photos)this.fileInputExperience(e,t,n);else if(e.source===m.Prompt){let a=document.querySelector("pwa-action-sheet");a||(a=document.createElement("pwa-action-sheet"),document.body.appendChild(a)),a.header=e.promptLabelHeader||"Photo",a.cancelable=!1,a.options=[{title:e.promptLabelPhoto||"From Photos"},{title:e.promptLabelPicture||"Take Picture"}],a.addEventListener("onSelection",async a=>{0===a.detail?this.fileInputExperience(e,t,n):this.cameraExperience(e,t,n)})}else this.cameraExperience(e,t,n)})}async pickImages(e){return new Promise(async(e,t)=>{this.multipleFileInputExperience(e,t)})}async cameraExperience(e,t,n){if(customElements.get("pwa-camera-modal")){const o=document.createElement("pwa-camera-modal");o.facingMode=e.direction===w.Front?"user":"environment",document.body.appendChild(o);try{await o.componentOnReady(),o.addEventListener("onPhoto",async r=>{const i=r.detail;null===i?n(new a("User cancelled photos app")):i instanceof Error?n(i):t(await this._getCameraPhoto(i,e)),o.dismiss(),document.body.removeChild(o)}),o.present()}catch(r){this.fileInputExperience(e,t,n)}}else this.fileInputExperience(e,t,n)}fileInputExperience(e,t,n){let r=document.querySelector("#_capacitor-camera-input");const o=()=>{var e;null===(e=r.parentNode)||void 0===e||e.removeChild(r)};r||(r=document.createElement("input"),r.id="_capacitor-camera-input",r.type="file",r.hidden=!0,document.body.appendChild(r),r.addEventListener("change",n=>{const a=r.files[0];let i="jpeg";if("image/png"===a.type?i="png":"image/gif"===a.type&&(i="gif"),"dataUrl"===e.resultType||"base64"===e.resultType){const n=new FileReader;n.addEventListener("load",()=>{if("dataUrl"===e.resultType)t({dataUrl:n.result,format:i});else if("base64"===e.resultType){const e=n.result.split(",")[1];t({base64String:e,format:i})}o()}),n.readAsDataURL(a)}else t({webPath:URL.createObjectURL(a),format:i}),o()}),r.addEventListener("cancel",e=>{n(new a("User cancelled photos app")),o()})),r.accept="image/*",r.capture=!0,e.source===m.Photos||e.source===m.Prompt?r.removeAttribute("capture"):e.direction===w.Front?r.capture="user":e.direction===w.Rear&&(r.capture="environment"),r.click()}multipleFileInputExperience(e,t){let n=document.querySelector("#_capacitor-camera-input-multiple");const r=()=>{var e;null===(e=n.parentNode)||void 0===e||e.removeChild(n)};n||(n=document.createElement("input"),n.id="_capacitor-camera-input-multiple",n.type="file",n.hidden=!0,n.multiple=!0,document.body.appendChild(n),n.addEventListener("change",t=>{const a=[];for(let e=0;e<n.files.length;e++){const t=n.files[e];let r="jpeg";"image/png"===t.type?r="png":"image/gif"===t.type&&(r="gif"),a.push({webPath:URL.createObjectURL(t),format:r})}e({photos:a}),r()}),n.addEventListener("cancel",e=>{t(new a("User cancelled photos app")),r()})),n.accept="image/*",n.click()}_getCameraPhoto(e,t){return new Promise((n,a)=>{const r=new FileReader,o=e.type.split("/")[1];"uri"===t.resultType?n({webPath:URL.createObjectURL(e),format:o,saved:!1}):(r.readAsDataURL(e),r.onloadend=()=>{const e=r.result;"dataUrl"===t.resultType?n({dataUrl:e,format:o,saved:!1}):n({base64String:e.split(",")[1],format:o,saved:!1})},r.onerror=e=>{a(e)})})}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(await window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch(e){throw this.unavailable("Camera permissions are not available in this browser")}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}async pickLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}async getLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}}const b=i("Camera",{web:()=>new v});const E=i("Geolocation",{web:()=>e(()=>import("./web-CDahzHX1.js"),__vite__mapDeps([0,1,2])).then(e=>new e.GeolocationWeb)});!function(e=!1){typeof window>"u"||(window.CapacitorUtils=window.CapacitorUtils||{},void 0===window.Capacitor||e?void 0!==window.cordova&&function(e){e.CapacitorUtils.Synapse=new Proxy({},{get:(t,n)=>e.cordova.plugins[n]})}(window):function(e){e.CapacitorUtils.Synapse=new Proxy({},{get:(t,n)=>new Proxy({},{get:(t,a)=>(t,r,o)=>{const i=e.Capacitor.Plugins[n];void 0!==i?"function"==typeof i[a]?(async()=>{try{const e=await i[a](t);r(e)}catch(e){o(e)}})():o(new Error("Method ".concat(a," not found in Capacitor plugin ").concat(n))):o(new Error("Capacitor plugin ".concat(n," not found")))}})})}(window))}();export{b as C,E as G,s as W,i as r};

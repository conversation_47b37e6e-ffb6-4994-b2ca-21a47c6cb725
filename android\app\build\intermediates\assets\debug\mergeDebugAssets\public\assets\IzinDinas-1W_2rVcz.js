import{r as e,j as s,I as a,J as i,K as n,M as t,P as r,L as l,h as o,k as d,p as c,C as u,o as g,s as h,t as x,S as j,u as m,W as p,v as f,w as k,O as y,H as b,N as v}from"./ionic-CJlrxXsE.js";import{j as S,s as w,h as _,d as z,g as I,r as D}from"./index-BZ7jmVXp.js";import{u as T}from"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";import"./capacitor-DGgumwVn.js";const C=()=>{const C=JSON.parse(localStorage.getItem("user")||"{}"),O=T(),F=e.useRef(null),A=e.useRef(null),[M,R]=e.useState(""),[B,W]=e.useState(""),[P,H]=e.useState(""),[U,J]=e.useState(""),[N,K]=e.useState(""),[L,E]=e.useState(""),[G,q]=e.useState(!1),[Q,V]=e.useState(null),[X,Y]=e.useState(""),[Z,$]=e.useState(""),[ee,se]=e.useState(!1),[ae,ie]=e.useState(!1),[ne,te]=e.useState(""),[re,le]=e.useState("success"),[oe,de]=e.useState(!1),[ce,ue]=e.useState(""),[ge,he]=e.useState(null),[xe,je]=e.useState(!0),me=e=>{V(e),Y(""),$(""),q(!0)},pe=async()=>{$(""),Y("");try{const s="wajah"===Q?"user":"environment";try{const e=await navigator.mediaDevices.getUserMedia({video:{facingMode:{exact:s},width:{ideal:1280},height:{ideal:720}}});F.current&&(F.current.srcObject=e)}catch(e){const a=await navigator.mediaDevices.getUserMedia({video:{facingMode:s,width:{ideal:1280},height:{ideal:720}}});F.current&&(F.current.srcObject=a);$("Kamera ".concat("user"===s?"depan":"belakang"," tidak tersedia. Menggunakan kamera yang tersedia."))}}catch(s){$("Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.")}},fe=()=>{if(F.current&&F.current.srcObject){F.current.srcObject.getTracks().forEach(e=>e.stop()),F.current.srcObject=null}},ke=()=>{fe(),q(!1),V(null),Y(""),$("")};e.useEffect(()=>(G&&pe(),()=>{G&&fe()}),[G]);const ye=(e,s)=>{te(e),le(s),ie(!0)},be=()=>{R(""),W(""),H(""),J(""),K(""),E("")},ve=e=>e?e.substring(0,5):"-";return e.useEffect(()=>{(async()=>{if(C.id||C.nik){je(!0);try{const e=(new Date).toISOString().split("T")[0],s=C.id||C.nik,a=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(s,"&tanggal=").concat(e)),i=await a.json();"success"===i.status&&i.data.length>0?he({jam_masuk:i.data[0].jam_masuk,jam_pulang:i.data[0].jam_pulang}):he(null)}catch(e){he(null)}finally{je(!1)}}else je(!1)})()},[]),s.jsxs(a,{children:[s.jsx(i,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},children:s.jsxs(n,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[s.jsx(t,{slot:"start",children:s.jsx(r,{defaultHref:"/histori-izin-dinas",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),s.jsx(l,{style:{color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center"},children:"Izin Dinas"})]})}),s.jsxs(o,{className:"ion-padding",children:[xe?s.jsx(d,{children:s.jsx(c,{children:s.jsxs("div",{style:{textAlign:"center",padding:"20px"},children:[s.jsx(u,{name:"crescent"}),s.jsx("p",{style:{margin:"10px 0 0 0",color:"#666"},children:"Memuat status absensi..."})]})})}):(null==ge?void 0:ge.jam_masuk)?s.jsx(d,{style:{marginBottom:"20px",border:ge.jam_pulang?"2px solid #4caf50":"2px solid #ff9800"},children:s.jsxs(c,{children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"},children:[s.jsx(g,{icon:ge.jam_pulang?S:w,style:{fontSize:"1.5rem",color:ge.jam_pulang?"#4caf50":"#ff9800",marginRight:"12px"}}),s.jsxs("div",{children:[s.jsx("div",{style:{fontWeight:"bold",fontSize:"1.1rem",color:"#333"},children:"Status Absensi Hari Ini"}),s.jsxs("div",{style:{fontSize:"0.9rem",color:"#666"},children:["Masuk: ",ve(ge.jam_masuk)," | Pulang: ",ve(ge.jam_pulang)]})]})]}),!ge.jam_pulang&&s.jsxs("div",{style:{padding:"12px",backgroundColor:"#fff3cd",borderRadius:"8px",border:"1px solid #ffeaa7"},children:[s.jsx("div",{style:{fontSize:"0.9rem",color:"#856404",fontWeight:"bold",marginBottom:"4px"},children:"ℹ️ Informasi Penting"}),s.jsx("div",{style:{fontSize:"0.85rem",color:"#856404"},children:"Anda sudah absen masuk hari ini. Izin dinas ini akan digunakan sebagai pengganti absen pulang."})]})]})}):null,s.jsx(d,{children:s.jsxs(c,{children:[s.jsxs("div",{style:{marginBottom:"20px"},children:[s.jsx("h2",{style:{margin:"0 0 8px 0",color:"#333"},children:"Form Pengajuan Izin Dinas"}),s.jsx("p",{style:{margin:"0",fontSize:"0.9rem",color:"#666"},children:"Lengkapi form di bawah untuk mengajukan izin dinas"})]}),s.jsxs(h,{children:[s.jsxs(x,{position:"stacked",children:["Tanggal Mulai ",s.jsx("span",{style:{color:"red"},children:"*"})]}),s.jsx(j,{value:M,onIonChange:e=>R(e.detail.value),presentation:"date",locale:"id-ID",min:(new Date).toISOString().split("T")[0]})]}),s.jsxs(h,{children:[s.jsxs(x,{position:"stacked",children:["Tanggal Selesai ",s.jsx("span",{style:{color:"red"},children:"*"})]}),s.jsx(j,{value:B,onIonChange:e=>W(e.detail.value),presentation:"date",locale:"id-ID",min:M||(new Date).toISOString().split("T")[0]})]}),s.jsxs(h,{children:[s.jsxs(x,{position:"stacked",children:["Tujuan Dinas ",s.jsx("span",{style:{color:"red"},children:"*"})]}),s.jsx(m,{value:P,onIonInput:e=>H(e.detail.value),placeholder:"Contoh: Rapat koordinasi di kantor pusat"})]}),s.jsxs(h,{children:[s.jsxs(x,{position:"stacked",children:["Keterangan ",s.jsx("span",{style:{color:"red"},children:"*"})]}),s.jsx(p,{value:U,onIonInput:e=>J(e.detail.value),placeholder:"Jelaskan detail kegiatan dinas yang akan dilakukan",rows:3})]})]})}),s.jsx(d,{children:s.jsxs(c,{children:[s.jsxs("div",{style:{marginBottom:"20px"},children:[s.jsx("h3",{style:{margin:"0 0 8px 0",color:"#333"},children:"Upload Dokumen"}),s.jsx("p",{style:{margin:"0",fontSize:"0.9rem",color:"#666"},children:"Ambil foto surat tugas dan foto wajah untuk verifikasi"})]}),s.jsxs("div",{style:{marginBottom:"20px"},children:[s.jsx(x,{children:s.jsxs("strong",{children:["Foto Surat Tugas ",s.jsx("span",{style:{color:"red"},children:"*"})]})}),s.jsx("div",{style:{marginTop:"8px"},children:N?s.jsxs("div",{style:{textAlign:"center"},children:[s.jsx("img",{src:N,alt:"Foto Surat Tugas",style:{width:"200px",height:"150px",objectFit:"cover",borderRadius:"8px",border:"2px solid #ddd",marginBottom:"12px"}}),s.jsx("div",{children:s.jsxs(f,{size:"small",fill:"outline",onClick:()=>me("surat"),children:[s.jsx(g,{icon:_,slot:"start"}),"Ambil Ulang"]})})]}):s.jsxs(f,{expand:"block",fill:"outline",onClick:()=>me("surat"),children:[s.jsx(g,{icon:_,slot:"start"}),"Ambil Foto Surat Tugas"]})})]}),s.jsxs("div",{style:{marginBottom:"20px"},children:[s.jsx(x,{children:s.jsxs("strong",{children:["Foto Wajah ",s.jsx("span",{style:{color:"red"},children:"*"})]})}),s.jsx("div",{style:{marginTop:"8px"},children:L?s.jsxs("div",{style:{textAlign:"center"},children:[s.jsx("img",{src:L,alt:"Foto Wajah",style:{width:"150px",height:"150px",objectFit:"cover",borderRadius:"50%",border:"2px solid #ddd",marginBottom:"12px"}}),s.jsx("div",{children:s.jsxs(f,{size:"small",fill:"outline",onClick:()=>me("wajah"),children:[s.jsx(g,{icon:_,slot:"start"}),"Ambil Ulang"]})})]}):s.jsxs(f,{expand:"block",fill:"outline",onClick:()=>me("wajah"),children:[s.jsx(g,{icon:_,slot:"start"}),"Ambil Foto Wajah"]})})]})]})}),s.jsx(d,{children:s.jsxs(c,{children:[s.jsx(f,{expand:"block",onClick:async()=>{if(C.id||C.nik)if(M&&B&&P&&U)if(N&&L){se(!0);try{const s=e=>new Date(e).toISOString().split("T")[0],a={api_key:"absensiku_api_key_2023",user_id:C.id||C.nik,tanggal_mulai:s(M),tanggal_selesai:s(B),tujuan:P,keterangan:U,foto_surat_tugas_base64:N,foto_wajah_base64:L,status:"pending",is_notified:"0"},i=await fetch("https://absensiku.trunois.my.id/api/izin_dinas.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok){const e=await i.text();return void ye("Server error (".concat(i.status,"): ").concat(e.substring(0,100)),"danger")}const n=await i.text();let t;try{t=JSON.parse(n)}catch(e){return void ye("Server mengembalikan response yang tidak valid","danger")}"success"===t.status?(ye("Izin dinas berhasil diajukan","success"),be(),setTimeout(()=>{O.push("/histori-izin-dinas")},2e3)):ye(t.message||"Gagal mengajukan izin dinas","danger")}catch(s){ye("Terjadi kesalahan koneksi","danger")}finally{se(!1)}}else ye("Mohon ambil foto surat tugas dan foto wajah","danger");else ye("Mohon lengkapi semua field yang wajib diisi","danger");else ye("Data user tidak ditemukan. Silakan login ulang.","danger")},disabled:ee,style:{height:"50px",fontSize:"1.1rem",fontWeight:"bold"},children:ee?s.jsxs(s.Fragment,{children:[s.jsx(u,{name:"crescent",style:{marginRight:"8px"}}),"Mengirim..."]}):s.jsxs(s.Fragment,{children:[s.jsx(g,{icon:z,slot:"start"}),"Ajukan Izin Dinas"]})}),s.jsx("div",{style:{marginTop:"12px",textAlign:"center",fontSize:"0.8rem",color:"#666"},children:"Pastikan semua data sudah benar sebelum mengirim"})]})}),s.jsx(k,{isOpen:ae,onDidDismiss:()=>ie(!1),message:ne,duration:3e3,color:re}),s.jsx(y,{isOpen:oe,onDidDismiss:()=>de(!1),header:"Informasi",message:ce,buttons:["OK"]}),s.jsxs(b,{isOpen:G,onDidDismiss:ke,children:[s.jsxs(i,{children:[s.jsxs(n,{children:[s.jsxs(l,{children:["Ambil Foto ","surat"===Q?"Surat Tugas":"Wajah"]}),s.jsx(t,{slot:"end",children:s.jsx(f,{onClick:ke,children:s.jsx(g,{icon:I})})})]}),s.jsxs("div",{style:{padding:"8px 16px",backgroundColor:"rgba(26, 101, 235, 0.1)",fontSize:"0.85rem",color:"#1a65eb",textAlign:"center"},children:["📷 Menggunakan kamera ","surat"===Q?"belakang":"depan","surat"===Q?" untuk foto dokumen":" untuk foto wajah"]})]}),s.jsx(o,{children:s.jsxs("div",{style:{padding:"16px",display:"flex",flexDirection:"column",height:"100%"},children:[s.jsxs("div",{style:{flex:1,display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"16px"},children:[X?s.jsx("img",{src:X,alt:"Preview Foto",style:{width:"100%",maxHeight:"70vh",borderRadius:"12px",objectFit:"cover"}}):s.jsx("video",{ref:F,autoPlay:!0,playsInline:!0,style:{width:"100%",maxHeight:"70vh",borderRadius:"12px",background:"#000",objectFit:"cover"}}),s.jsx("canvas",{ref:A,style:{display:"none"}})]}),Z&&s.jsx(v,{color:"danger",children:s.jsx("p",{style:{textAlign:"center",margin:"8px 0"},children:Z})}),s.jsx("div",{style:{display:"flex",justifyContent:"center",gap:"12px",marginTop:"auto",paddingBottom:"16px"},children:X?s.jsxs(s.Fragment,{children:[s.jsxs(f,{color:"medium",onClick:()=>{Y(""),pe()},size:"large",shape:"round",children:[s.jsx(g,{icon:D,slot:"start"}),"Ulangi"]}),s.jsxs(f,{color:"success",onClick:()=>{if(X&&Q){"surat"===Q?K(X):E(X);(new Date).toISOString();ye("Foto ".concat("surat"===Q?"surat tugas":"wajah"," berhasil diambil"),"success"),ke()}},size:"large",shape:"round",children:[s.jsx(g,{icon:S,slot:"start"}),"Gunakan"]})]}):s.jsxs(f,{color:"primary",onClick:()=>{if(F.current&&A.current){const e=F.current,s=A.current;s.width=e.videoWidth,s.height=e.videoHeight;const a=s.getContext("2d");a&&(a.drawImage(e,0,0,s.width,s.height),Y(s.toDataURL("image/jpeg",.85))),fe()}},size:"large",shape:"round",disabled:!!Z,children:[s.jsx(g,{icon:_,slot:"start"}),"Ambil Foto"]})})]})})]})]})]})};export{C as default};

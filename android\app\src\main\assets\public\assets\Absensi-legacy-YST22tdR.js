System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./capacitor-legacy-cVgeOc-7.js","./utils-legacy-DvNNcox0.js","./jamKerja-legacy-BJSQBBXU.js","./jamKerjaBidang-legacy-D-Z9PwNM.js","./react-vendor-legacy-wCcNgjsd.js","./networkOptimizer-legacy-Beqb1gSF.js"],function(a,e){"use strict";var t,i,n,s,r,o,l,d,u,c,g,m,p,f,k,h,b,j,x,_,y,S,w,v,O;return{setters:[a=>{t=a.r,i=a.D,n=a.j,s=a.I,r=a.J,o=a.K,l=a.M,d=a.P,u=a.L,c=a.h,g=a.N,m=a.v,p=a.o,f=a.x},a=>{k=a.i,h=a.h,b=a.r,j=a.j,x=a.k,_=a.m,y=a.c},a=>{S=a.G},a=>{w=a.S},a=>{v=a.fetchAndStoreJamKerja},a=>{O=a.fetchAndStoreJamKerjaBidang},null,null],execute:function(){const e=new w;e.create();const I={background:"linear-gradient(135deg, #1880ff 60%, #005be7 100%)",boxShadow:"0 4px 18px rgba(24, 128, 255, 0)",borderBottomLeftRadius:32,borderBottomRightRadius:32,minHeight:80,padding:"0 0 8px 0"},$={fontSize:"1.5rem",fontWeight:700,letterSpacing:.5,color:"#fff",textShadow:"0 2px 8px rgba(24,128,255,0.13)"};function D(){return["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][(new Date).getDay()]}function N(a=new Date){return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`}function A(a,e,t){const i=a=>a*Math.PI/180,n=i(a),s=i(Number(t.latitude)),r=i(Number(t.latitude)-a),o=i(Number(t.longitude)-e),l=Math.sin(r/2)*Math.sin(r/2)+Math.cos(n)*Math.cos(s)*Math.sin(o/2)*Math.sin(o/2);return 2*Math.atan2(Math.sqrt(l),Math.sqrt(1-l))*6371e3<=Number(t.radius)}a("default",()=>{const a=JSON.parse(localStorage.getItem("user")||"{}"),w=1===a?.allow_flexible_schedule||"1"===a?.allow_flexible_schedule||!0===a?.allow_flexible_schedule,J=t.useRef(null),M=t.useRef(null),[R,L]=t.useState(null),[T,W]=t.useState(""),[C,E]=t.useState(!1),[q,z]=t.useState(""),[P,B]=t.useState(""),[G]=i(),{getLocation:H}=function(){const[a,i]=t.useState(null),[n,s]=t.useState(!1),[r,o]=t.useState(null);return{coords:a,loading:n,error:r,getLocation:async()=>{s(!0),o(null);try{if("granted"!==(await S.checkPermissions()).location&&"granted"!==(await S.requestPermissions()).location)throw new Error("Izin lokasi ditolak");const a=await S.getCurrentPosition({enableHighAccuracy:!0}),t=a.coords.latitude,n=a.coords.longitude;i({lat:t,lng:n});const s={lat:t,lng:n,timestamp:Date.now()};if(await e.set("last_gps",s),!navigator.onLine){let a=await e.get("gps_queue")||[];a.push(s),await e.set("gps_queue",a)}return s}catch(a){throw o(a.message||"Gagal mendapatkan lokasi"),a}finally{s(!1)}},syncData:async()=>{if(!navigator.onLine)return;const a=await e.get("gps_queue")||[];for(const e of a)try{await fetch("https://your-server.com/api/sync_gps",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(t){continue}await e.set("gps_queue",[])}}}(),[K,F]=t.useState(!1),[U,X]=t.useState(!1),[Y,Q]=t.useState(!1),[V,Z]=t.useState(navigator.onLine),[aa,ea]=t.useState(0),[ta,ia]=t.useState(""),[na,sa]=t.useState(!1),[ra,oa]=t.useState(!0),[la,da]=t.useState(0),ua=JSON.parse(localStorage.getItem("lokasi_list")||"[]")[0],ca=()=>{try{const a=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]");ea(a.length)}catch(a){}},ga=a=>{try{const e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),t={...a,id:Date.now().toString()+"_"+Math.random().toString(36).substring(2,11),timestamp:(new Date).toISOString()};return e.push(t),localStorage.setItem("offline_absensi_queue",JSON.stringify(e)),ca(),t}catch(e){return null}},[ma,pa]=t.useState(!1),[fa,ka]=t.useState("masuk"),ha=a=>{const e={jenis:a,tanggal:N(),timestamp:(new Date).toISOString()};localStorage.setItem("status_absen",JSON.stringify(e))},ba=()=>{try{const a=N(),e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]").filter(e=>e.tanggal===a),t=JSON.parse(localStorage.getItem("absensi_backup")||"[]").filter(e=>e.tanggal===a),i=[...e,...t],n=i.some(a=>"masuk"===a.jenisAbsensi||a.jam_masuk),s=i.some(a=>"pulang"===a.jenisAbsensi||a.jam_pulang);return n&&!s?"pulang":"masuk"}catch(a){return"masuk"}},ja=()=>{N(),JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),JSON.parse(localStorage.getItem("absensi_backup")||"[]"),localStorage.getItem("status_absen")};t.useEffect(()=>(window.debugStatusAbsen=ja,()=>{delete window.debugStatusAbsen}),[fa,V]),t.useEffect(()=>{const e=()=>{Z(!0),(a.id||a.nik)&&(async()=>{try{const e=N(),t=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${a.id||a.nik}&tanggal=${e}`),i=await t.json();if("success"===i.status&&i.data.length>0){const a=i.data[0];a.jam_masuk&&!a.jam_pulang?(ka("pulang"),ha("masuk")):a.jam_masuk&&a.jam_pulang&&(ka("masuk"),ha("pulang"))}}catch(e){}})()},t=()=>{Z(!1);const a=ba();ka(a)};if(window.addEventListener("online",e),window.addEventListener("offline",t),ca(),(()=>{try{const a=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),e=new Date;e.setDate(e.getDate()-7);const t=a.filter(a=>new Date(a.timestamp)>e);t.length<a.length&&(localStorage.setItem("offline_absensi_queue",JSON.stringify(t)),ca())}catch(a){}})(),!navigator.onLine&&(a.id||a.nik)){const a=ba();ka(a)}return()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[a]),t.useEffect(()=>{(a.id||a.nik)&&(async()=>{try{if(navigator.onLine){const e=N(),t=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${a.id||a.nik}&tanggal=${e}`),i=await t.json();if("success"===i.status&&i.data.length>0){const a=i.data[0];a.jam_masuk&&!a.jam_pulang?(ka("pulang"),ha("masuk")):a.jam_masuk&&a.jam_pulang?(ka("masuk"),ha("pulang")):ka("masuk")}else ka("masuk")}else{const a=ba();ka(a)}}catch(e){const a=ba();ka(a)}})()},[a]),t.useEffect(()=>{ua&&!ma&&(async()=>{if(!ma&&ua){B("Mengecek lokasi...");try{const a=await H();A(a.lat,a.lng,ua)?(B("Lokasi valid, dalam radius."),F(!0),X(!1),G({message:"Lokasi valid, dalam radius.",color:"success",duration:2e3,position:"top"})):(B("Anda di luar radius lokasi!"),F(!1),X(!0),G({message:"Anda di luar radius lokasi!",color:"danger",duration:2e3,position:"top"})),pa(!0)}catch(a){B("Gagal mendapatkan lokasi"),F(!1),X(!0),G({message:"Gagal mendapatkan lokasi",color:"danger",duration:2e3,position:"top"}),pa(!0)}}})()},[ua,ma]);const xa=async()=>{W(""),L(null);try{const a=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});J.current&&(J.current.srcObject=a)}catch(a){W("Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.")}},_a=()=>{J.current&&J.current.srcObject&&(J.current.srcObject.getTracks().forEach(a=>a.stop()),J.current.srcObject=null)},ya=a=>{if(w)return{valid:!0,error:"",info:"Mode fleksibel aktif: Anda dapat absen kapan saja."};const e=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),t=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),i=D(),n=t.find(a=>a.hari===i&&a.jam_kerja_id);if(!n||!n.jam_kerja_id)return{valid:!1,error:"Tidak ada jadwal jam kerja untuk hari ini.",info:""};const s=e.find(a=>a.id==n.jam_kerja_id);if(!s)return{valid:!1,error:"Data jam kerja tidak ditemukan.",info:""};const r=new Date,o=a=>a.toString().padStart(2,"0"),l=o(r.getHours())+":"+o(r.getMinutes()),d=`Jam kerja: ${s.awal_jam_masuk} - ${s.akhir_jam_pulang}`;if("masuk"===a){if(!s.awal_jam_masuk||!s.akhir_jam_masuk)return{valid:!1,error:"Data jam masuk tidak lengkap.",info:d};if(l<s.awal_jam_masuk)return{valid:!1,error:`Absen masuk belum bisa dilakukan. Waktu absen masuk: ${s.awal_jam_masuk} - ${s.akhir_jam_masuk}`,info:d};if(l>s.akhir_jam_masuk)return{valid:!1,error:`Waktu absen masuk sudah berakhir. Waktu absen masuk: ${s.awal_jam_masuk} - ${s.akhir_jam_masuk}`,info:d}}else{if(!s.jam_pulang||!s.akhir_jam_pulang)return{valid:!1,error:"Data jam pulang tidak lengkap.",info:d};if(l<s.jam_pulang)return{valid:!1,error:`Absen pulang belum bisa dilakukan. Waktu absen pulang: ${s.jam_pulang} - ${s.akhir_jam_pulang}`,info:d};if(l>s.akhir_jam_pulang)return{valid:!1,error:`Waktu absen pulang sudah berakhir. Waktu absen pulang: ${s.jam_pulang} - ${s.akhir_jam_pulang}`,info:d}}return{valid:!0,error:"",info:d}},Sa=a=>{if(w)return"Fleksibel (kapan saja)";const e=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),t=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),i=D(),n=t.find(a=>a.hari===i&&a.jam_kerja_id);if(!n||!n.jam_kerja_id)return"Data tidak tersedia";const s=e.find(a=>a.id==n.jam_kerja_id);return s?"masuk"===a?`${s.awal_jam_masuk||"--:--"} - ${s.akhir_jam_masuk||"--:--"}`:`${s.jam_pulang||"--:--"} - ${s.akhir_jam_pulang||"--:--"}`:"Data tidak tersedia"};return t.useEffect(()=>{W(""),E(!1),z("");const a=ya(fa);if(z(a.info),!a.valid)return W(a.error),void E(!1);E(!0),xa()},[fa,la,w]),t.useEffect(()=>{(async()=>{if(w)return;const a=localStorage.getItem("jam_kerja_list"),e=localStorage.getItem("jam_kerja_bidang_list");try{a||await v(),e||await O()}catch(t){}finally{da(a=>a+1)}})()},[w]),t.useEffect(()=>{const a=()=>{const a=new Date,e=a=>a.toString().padStart(2,"0"),t=e(a.getHours())+":"+e(a.getMinutes())+":"+e(a.getSeconds());ia(t)};a();const e=setInterval(a,1e3);return()=>clearInterval(e)},[]),t.useEffect(()=>()=>_a(),[]),n.jsxs(s,{children:[n.jsx("style",{children:"\n  @keyframes pulse {\n    0% {\n      border-color: #1880ff;\n      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(24, 128, 255, 0.7);\n      transform: scale(1);\n    }\n    50% {\n      border-color: #00d4ff;\n      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 10px rgba(24, 128, 255, 0.3);\n      transform: scale(1.02);\n    }\n    100% {\n      border-color: #1880ff;\n      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(24, 128, 255, 0.7);\n      transform: scale(1);\n    }\n  }\n\n  @keyframes fadeInOut {\n    0%, 100% { opacity: 0.8; }\n    50% { opacity: 1; }\n  }\n\n  @keyframes cornerBlink {\n    0%, 100% { opacity: 1; background: #1880ff; }\n    50% { opacity: 0.6; background: #00d4ff; }\n  }\n"}),n.jsx(r,{style:I,children:n.jsxs(o,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[n.jsx(l,{slot:"start",children:n.jsx(d,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),n.jsxs(u,{style:$,children:["Absensi ","masuk"===fa?"Masuk":"Pulang"]})]})}),n.jsxs(c,{className:"ion-padding",fullscreen:!0,children:[n.jsxs("div",{style:{maxWidth:400,margin:"0 auto",textAlign:"center"},children:[n.jsx("div",{style:{margin:"16px 0 24px 0",padding:"16px",borderRadius:"16px",backgroundColor:"masuk"===fa?"#e8f5e8":"#fff3cd",border:"2px solid "+("masuk"===fa?"#4caf50":"#ff9800")},children:n.jsx(g,{color:"masuk"===fa?"success":"warning",children:n.jsx("h2",{style:{margin:"0",fontSize:"1.5rem",fontWeight:"bold"},children:"masuk"===fa?"📥 Absensi Masuk":"📤 Absensi Pulang"})})}),P&&n.jsx(g,{color:P.includes("valid")?"success":"danger",children:n.jsx("p",{children:P})}),U&&n.jsxs(m,{color:"tertiary",onClick:async()=>{X(!1),B("Mengecek ulang lokasi..."),pa(!1);try{const a=await H();ua&&!A(a.lat,a.lng,ua)?(B("Anda di luar radius lokasi!"),F(!1),X(!0),G({message:"Anda di luar radius lokasi!",color:"danger",duration:2e3,position:"top"})):(B("Lokasi valid, dalam radius."),F(!0),X(!1),G({message:"Lokasi valid, dalam radius.",color:"success",duration:2e3,position:"top"})),pa(!0)}catch(a){B("Gagal mendapatkan lokasi"),F(!1),X(!0),G({message:"Gagal mendapatkan lokasi",color:"danger",duration:2e3,position:"top"}),pa(!0)}},style:{margin:"12px 0"},children:[n.jsx(p,{icon:k,slot:"start"})," Refresh Lokasi"]}),!R&&n.jsx("div",{style:{textAlign:"center",margin:"12px 0"}}),n.jsxs("div",{style:{margin:"18px 0",position:"relative"},children:[R?n.jsx("img",{src:R,alt:"Foto Wajah",style:{width:"100%",borderRadius:18,objectFit:"cover"}}):n.jsxs("div",{style:{position:"relative",borderRadius:18,overflow:"hidden"},children:[n.jsx("video",{ref:J,autoPlay:!0,playsInline:!0,style:{width:"100%",height:"300px",borderRadius:18,background:"#000",objectFit:"cover"}}),ra&&n.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,pointerEvents:"none",display:"flex",alignItems:"center",justifyContent:"center"},children:n.jsxs("div",{style:{width:"min(200px, 50vw)",height:"min(260px, 65vw)",maxWidth:"250px",maxHeight:"320px",border:"3px solid #1880ff",borderRadius:"50%",background:"rgba(24, 128, 255, 0.1)",position:"relative",boxShadow:"0 0 0 9999px rgba(0, 0, 0, 0.5)",animation:"pulse 2s ease-in-out infinite"},children:[n.jsx("div",{style:{position:"absolute",top:"-10px",left:"50%",transform:"translateX(-50%)",width:"30px",height:"6px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite"}}),n.jsx("div",{style:{position:"absolute",bottom:"-10px",left:"50%",transform:"translateX(-50%)",width:"30px",height:"6px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite 0.3s"}}),n.jsx("div",{style:{position:"absolute",left:"-10px",top:"50%",transform:"translateY(-50%)",width:"6px",height:"30px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite 0.6s"}}),n.jsx("div",{style:{position:"absolute",right:"-10px",top:"50%",transform:"translateY(-50%)",width:"6px",height:"30px",background:"#1880ff",borderRadius:"3px",animation:"cornerBlink 1.5s ease-in-out infinite 0.9s"}})]})}),ra&&n.jsx("div",{style:{position:"absolute",bottom:"20px",left:"50%",transform:"translateX(-50%)",background:"rgba(0, 0, 0, 0.7)",color:"white",padding:"8px 16px",borderRadius:"20px",fontSize:"14px",fontWeight:"500",textAlign:"center",pointerEvents:"none"}})]}),n.jsx("canvas",{ref:M,style:{display:"none"}})]}),T&&n.jsx(g,{color:"danger",children:n.jsx("p",{children:T})}),n.jsxs("div",{style:{display:"flex",justifyContent:"center",gap:12,margin:"18px 0"},children:[R?n.jsxs(m,{color:"medium",onClick:()=>{L(null),xa()},size:"large",shape:"round",children:[n.jsx(p,{icon:b,slot:"start"})," Ulangi"]}):n.jsxs(m,{color:"primary",onClick:()=>{if(J.current&&M.current){const a=J.current,e=M.current;e.width=a.videoWidth,e.height=a.videoHeight;const t=e.getContext("2d");t&&(t.drawImage(a,0,0,e.width,e.height),L(e.toDataURL("image/jpeg"))),_a()}},size:"large",shape:"round",disabled:!C||!K,children:[n.jsx(p,{icon:h,slot:"start"})," Ambil Foto"]}),R&&n.jsxs(m,{color:"success",onClick:async()=>{W("");const e=ya(fa);if(!e.valid)return void W(e.error);if(!K)return void W("Lokasi tidak valid. Pastikan Anda berada dalam radius kantor.");if(!R)return void W("Foto wajah diperlukan untuk absensi.");let t;Q(!0);try{const e=await H(),i=new Date,n=N(i),s=i.toTimeString().split(" ")[0],r=`${e.lat},${e.lng}`,o=((a,e)=>{const t=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),i=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),n=D(),s=i.find(a=>a.hari===n&&a.jam_kerja_id);if(!s||!s.jam_kerja_id)return"Tepat Waktu";const r=t.find(a=>a.id==s.jam_kerja_id);return r?"masuk"===a?e<=r.jam_masuk?"Tepat Waktu":"Terlambat":e<r.jam_pulang?"Pulang Awal":"Tepat Waktu":"Tepat Waktu"})(fa,s.substring(0,5));if(t={api_key:"absensiku_api_key_2023",user_id:a.id||a.nik,tanggal:n,status:o,keterangan:`Absensi ${fa} melalui aplikasi mobile - Status: ${o}`,jenisAbsensi:fa},"masuk"===fa?(t.jam_masuk=s,t.foto_masuk_base64=R,t.lokasi_masuk=r):(t.jam_pulang=s,t.foto_pulang_base64=R,t.lokasi_pulang=r),!navigator.onLine){if(ga(t))return ha(fa),G({message:`Absensi ${fa} disimpan offline. Data akan dikirim otomatis saat online.`,color:"warning",duration:4e3,position:"top"}),L(null),_a(),void setTimeout(()=>{window.location.href="/home"},2e3);throw new Error("Gagal menyimpan data offline")}let l;if("masuk"===fa)l=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});else{const e=N(),i=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${a.id||a.nik}&tanggal=${e}`),n=await i.json();if(!("success"===n.status&&n.data.length>0))throw new Error("Data absensi masuk tidak ditemukan untuk hari ini");{const a=n.data[0].id;t.id=a,l=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})}}const d=await l.json();if("success"===d.status){ha(fa);const a={...t,synced:!0,timestamp:(new Date).toISOString()},e=JSON.parse(localStorage.getItem("absensi_backup")||"[]");e.push(a),e.length>30&&e.splice(0,e.length-30),localStorage.setItem("absensi_backup",JSON.stringify(e)),G({message:`Absensi ${fa} berhasil disimpan!`,color:"success",duration:3e3,position:"top"}),L(null),_a(),setTimeout(()=>{window.location.href="/home"},2e3)}else ga(t)?(ha(fa),G({message:`Server error: ${d.message}. Data disimpan offline dan akan dikirim ulang otomatis.`,color:"warning",duration:4e3,position:"top"}),L(null),_a(),setTimeout(()=>{window.location.href="/home"},2e3)):(W(d.message||"Gagal menyimpan data absensi"),G({message:d.message||"Gagal menyimpan data absensi",color:"danger",duration:3e3,position:"top"}))}catch(i){ga(t)?(ha(fa),G({message:`Koneksi bermasalah. Absensi ${fa} disimpan offline dan akan dikirim otomatis saat online.`,color:"warning",duration:4e3,position:"top"}),L(null),_a(),setTimeout(()=>{window.location.href="/home"},2e3)):(W("Terjadi kesalahan saat menyimpan data. Silakan coba lagi."),G({message:"Terjadi kesalahan saat menyimpan data",color:"danger",duration:3e3,position:"top"}))}finally{Q(!1)}},size:"large",shape:"round",disabled:Y,children:[n.jsx(p,{icon:j,slot:"start"}),Y?"Mengirim...":"Absen "+("masuk"===fa?"Masuk":"Pulang")]})]}),n.jsxs(m,{fill:"outline",size:"small",color:"medium",onClick:()=>sa(!na),style:{margin:"0 0 16px 0","--border-radius":"20px","--padding-start":"16px","--padding-end":"16px"},children:[n.jsx(p,{icon:na?x:_,slot:"start"}),na?"Sembunyikan Detail":"Tampilkan Detail"]}),na&&n.jsxs("div",{style:{marginBottom:"20px"},children:[n.jsxs("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#f5f7ff",border:"1px solid #e4e9ff"},children:[n.jsx(g,{color:"primary",children:n.jsxs("p",{style:{margin:0,fontWeight:700},children:[n.jsx(p,{icon:y,style:{marginRight:8}}),(new Date).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})]})}),n.jsx(g,{color:"medium",children:n.jsx("small",{children:"Tanggal di atas adalah acuan pencatatan absensi hari ini."})})]}),n.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#f0f8ff",border:"1px solid #e1f5fe"},children:n.jsx(g,{color:"primary",children:n.jsxs("p",{style:{fontSize:"1.1rem",fontWeight:"bold",margin:"0"},children:["👤 ",a.nama||"Nama Karyawan"]})})}),n.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#f8f9fa",border:"1px solid #e9ecef"},children:n.jsx(g,{color:"dark",children:n.jsxs("p",{style:{fontSize:"1.1rem",fontWeight:"bold",margin:"0"},children:["🕐 Waktu Sekarang: ",ta]})})}),q&&n.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#1880ff",border:"1px solid #1880ff"},children:n.jsx(g,{color:"light",children:n.jsxs("p",{style:{fontSize:"0.95rem",margin:"0"},children:["📋 ",q]})})}),C&&n.jsx("div",{style:{margin:"12px 0",padding:"12px",borderRadius:"12px",backgroundColor:"#e3f2fd",border:"1px solid #bbdefb"},children:n.jsx(g,{color:"primary",children:n.jsx("p",{style:{fontSize:"0.9rem",margin:"0"},children:"masuk"===fa?`⏰ Waktu absen masuk: ${Sa("masuk")}`:`⏰ Waktu absen pulang: ${Sa("pulang")}`})})}),n.jsx("div",{style:{margin:"12px 0",padding:"8px",borderRadius:"8px",backgroundColor:V?"#e8f5e8":"#fff3cd"},children:n.jsx(g,{color:V?"success":"warning",children:n.jsxs("small",{children:[V?"🟢 Online":"🔴 Offline",aa>0&&` • ${aa} data menunggu sinkronisasi`]})})}),!V&&n.jsx("div",{style:{margin:"12px 0",padding:"8px",borderRadius:"8px",backgroundColor:"#f0f8ff",border:"1px solid #e1f5fe"},children:n.jsx(g,{color:"primary",children:n.jsxs("small",{children:["📱 Mode Offline: Status absen berdasarkan data lokal",n.jsx("br",{}),(()=>{const a=N(),e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]"),t=JSON.parse(localStorage.getItem("absensi_backup")||"[]");return[...e.filter(e=>e.tanggal===a),...t.filter(e=>e.tanggal===a)].some(a=>"masuk"===a.jenisAbsensi||a.jam_masuk)?"✅ Sudah absen masuk hari ini":"❌ Belum absen masuk hari ini"})()]})})})]})]}),n.jsx(f,{isOpen:Y,message:"Mengirim data absensi...",duration:0})]})]})})}}});

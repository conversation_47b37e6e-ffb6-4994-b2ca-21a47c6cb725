System.register([],function(e,t){"use strict";return{execute:function(){function t(e){return t.result?t.result:e&&"function"==typeof e.getSerializer?(t.result=e.getSerializer(),t.result):Promise.reject(new Error("localforage.getSerializer() was not available! localforage v1.4+ is required!"))}function n(e,t){return n.result=n.result||{},n.result[t]?n.result[t]:e&&"function"==typeof e.getDriver?(n.result[t]=e.getDriver(t),n.result[t]):Promise.reject(new Error("localforage.getDriver() was not available! localforage v1.4+ is required!"))}function r(e){return n(e,e.WEBSQL)}var i=new Promise(function(e,t){"undefined"!=typeof sqlitePlugin?e():"undefined"==typeof cordova?t(new Error("cordova is not defined.")):document.addEventListener("deviceready",function(){return e()},!1)}).catch(function(){return Promise.resolve()});function o(){return i.then(function(){if("undefined"!=typeof sqlitePlugin&&"function"==typeof sqlitePlugin.openDatabase)return sqlitePlugin.openDatabase;throw new Error("SQLite plugin is not present.")})}!function(e){var t=["clear","getItem","iterate","key","keys","length","removeItem","setItem","dropInstance"];function n(e,t){e[t]=function(){var e=this,n=arguments;return r(e).then(function(r){return r[t].apply(e,n)})}}for(var i=0,o=t.length;i<o;i++)n(e,t[i])}(e("default",{_driver:"cordovaSQLiteDriver",_initStorage:function(e){var n=this,i={db:null};if(e)for(var u in e)i[u]="string"!=typeof e[u]?e[u].toString():e[u];var a=o().then(function(e){return new Promise(function(t,r){try{i.location=i.location||"default",i.db=e({name:i.name,version:String(i.version),description:i.description,size:i.size,key:i.dbKey,location:i.location})}catch(o){r(o)}i.db.transaction(function(e){e.executeSql("CREATE TABLE IF NOT EXISTS "+i.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],function(){n._dbInfo=i,t()},function(e,t){r(t)})})})}),c=t(n),l=r(n);return Promise.all([c,l,a]).then(function(e){return i.serializer=e[0],a})},_support:function(){return o().then(function(e){return!!e}).catch(function(){return!1})}}))}}});

System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(e,i){"use strict";var t,a,n,s,r,l,o,d,c,m,h,x,g,u,p,j,f,k,b,y,v,S,w,_,D,T,N,I,A,C,z,F,W,M,$,R,J,B,K;return{setters:[e=>{t=e.r,a=e.j,n=e.I,s=e.J,r=e.K,l=e.M,o=e.P,d=e.L,c=e.v,m=e.o,h=e.h,x=e.E,g=e.F,u=e.k,p=e.p,j=e.Q,f=e.C,k=e.N,b=e.q,y=e.m,v=e.n,S=e.H,w=e.s,_=e.t,D=e.S,T=e.T,N=e.U,I=e.V,A=e.w,C=e.d},e=>{z=e.n,F=e.o,W=e.w,M=e.c,$=e.t,R=e.q,J=e.g,B=e.f,K=e.j},null,null,null],execute:function(){var P=document.createElement("style");P.textContent=".histori-header{padding:16px}.histori-user-info{display:flex;align-items:center;gap:16px}.histori-user-icon{font-size:2rem;color:var(--ion-color-primary)}.histori-user-info h3{margin:0 0 4px;font-size:1.2rem;font-weight:600}.histori-user-info p{margin:0;color:var(--ion-color-medium);font-size:.9rem}.histori-filter-summary{display:flex;align-items:center;justify-content:space-between;padding:12px 16px;background:var(--ion-color-light);margin:0 16px 16px;border-radius:8px}.histori-loading{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;text-align:center}.histori-loading p{margin-top:16px;color:var(--ion-color-medium)}.histori-error,.histori-empty{padding:16px}.histori-content{padding:0 16px 16px}.histori-card{margin-bottom:16px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,.1)}.histori-card-title{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:8px}.histori-date{display:flex;align-items:center;gap:8px;font-size:1rem;font-weight:600}.histori-date ion-icon{color:var(--ion-color-primary)}.histori-time-info{display:flex;flex-direction:column;gap:12px}.histori-time-item{display:flex;align-items:flex-start;gap:12px}.histori-time-item ion-icon{margin-top:2px;flex-shrink:0}.histori-time-item>div{flex:1}.histori-time-item strong{color:var(--ion-color-dark)}.histori-keterangan{margin-top:12px;padding:8px 12px;background:var(--ion-color-light);border-radius:6px;font-size:.9rem}.histori-filter-content{padding:16px}.histori-filter-actions{margin-top:24px;display:flex;flex-direction:column;gap:12px}.histori-image-modal{display:flex;align-items:center;justify-content:center;min-height:300px;padding:20px}.histori-image-modal ion-img{max-width:100%;max-height:80vh;object-fit:contain;border-radius:8px;box-shadow:0 4px 16px rgba(0,0,0,.2)}@media (max-width: 576px){.histori-card-title{flex-direction:column;align-items:flex-start}.histori-time-item{flex-direction:column;gap:8px}.histori-time-item ion-icon{margin-top:0}}.histori-card{transition:transform .2s ease,box-shadow .2s ease}.histori-card:hover{transform:translateY(-2px);box-shadow:0 4px 16px rgba(0,0,0,.15)}.histori-card ion-badge{display:flex;align-items:center;gap:4px;font-size:.8rem;padding:4px 8px}.histori-time-item ion-button{--padding-start: 8px;--padding-end: 8px;height:24px;font-size:.7rem;margin-top:4px}@keyframes pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.histori-loading ion-spinner{animation:pulse 1.5s ease-in-out infinite}\n",document.head.appendChild(P),e("default",()=>{const e=JSON.parse(localStorage.getItem("user")||"{}"),[P,L]=t.useState([]),[O,H]=t.useState(!0),[Y,E]=t.useState(""),[q,U]=t.useState(null),[G,Q]=t.useState(!1),[V,X]=t.useState(!1),Z=new Date,ee=(Z.getMonth()+1).toString().padStart(2,"0"),ie=Z.getFullYear().toString(),[te,ae]=t.useState(ee),[ne,se]=t.useState(ie),[re,le]=t.useState(""),[oe,de]=t.useState(!1),[ce,me]=t.useState("");t.useEffect(()=>{(async()=>{try{const e=localStorage.getItem("jam_kerja_list"),t=localStorage.getItem("jam_kerja_bidang_list");if(!e||!t){const{fetchAndStoreJamKerja:e}=await C(()=>i.import("./jamKerja-legacy-BJSQBBXU.js"),void 0),{fetchAndStoreJamKerjaBidang:t}=await C(()=>i.import("./jamKerjaBidang-legacy-D-Z9PwNM.js"),void 0);await e(),await t()}}catch(e){}})()},[]);const he=async()=>{if(!e.id&&!e.nik)return E("Data user tidak ditemukan"),void H(!1);H(!0),E("");try{const i=e.id||e.nik,t=await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${i}`),a=await t.json();if("success"===a.status&&Array.isArray(a.data)){const t=a.data.filter(t=>t.user_id===i||t.user_id===e.id||t.user_id===e.nik);L(t)}else E("Gagal mengambil data absensi")}catch(i){E("Terjadi kesalahan koneksi")}finally{H(!1)}},xe=e=>e?`https://absensiku.trunois.my.id/uploads/${e}`:null,ge=e=>new Date(e).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),ue=e=>e?e.substring(0,5):"-",pe=e=>{switch(e){case"Tepat Waktu":return"success";case"Terlambat":return"danger";case"Pulang Awal":return"warning";default:return"medium"}},je=e=>{switch(e){case"Tepat Waktu":return K;case"Terlambat":return B;case"Pulang Awal":return W;default:return $}},fe=(e,i)=>{if(!e)return"Belum Absen";try{const t=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),a=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),n=new Date(i).toLocaleDateString("id-ID",{weekday:"long"}),s=a.find(e=>e.hari===n&&e.jam_kerja_id);if(!s||!s.jam_kerja_id)return"Tepat Waktu";const r=t.find(e=>e.id==s.jam_kerja_id);if(!r)return"Tepat Waktu";const l=new Date(`2000-01-01T${e}`);return l>new Date(`2000-01-01T${r.jam_masuk}`)?"Terlambat":"Tepat Waktu"}catch(t){return"Tepat Waktu"}},ke=e=>{let i=0;const t=[],a=fe(e.jam_masuk,e.tanggal);if(e.jam_masuk&&"Terlambat"===a){const a=e.jam_masuk.substring(0,5);i+=1e4,t.push(`Terlambat masuk (${a}): Rp 10.000`)}return e.jam_masuk&&!e.jam_pulang&&(i+=5e3,t.push("Tidak absen pulang: Rp 5.000")),{totalDenda:i,dendaMessages:t,hasDenda:i>0}},be=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),ye=(e,i)=>{U({url:e,title:i}),Q(!0)},ve=()=>{Q(!1),U(null)},Se=()=>{const e=new Date,i=(e.getMonth()+1).toString().padStart(2,"0"),t=e.getFullYear().toString();ae(i),se(t),le(""),X(!1),de(!0),me("Filter direset ke bulan ini")};t.useEffect(()=>{he()},[]);const we=(()=>{let e=P;return te&&ne&&(e=e.filter(e=>{const i=new Date(e.tanggal),t=(i.getMonth()+1).toString().padStart(2,"0"),a=i.getFullYear().toString();return t===te&&a===ne})),re&&(e=e.filter(e=>e.status===re)),e.sort((e,i)=>new Date(i.tanggal).getTime()-new Date(e.tanggal).getTime())})();return a.jsxs(n,{children:[a.jsx(s,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},children:a.jsxs(r,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[a.jsx(l,{slot:"start",children:a.jsx(o,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),a.jsx(d,{style:{color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center"},children:"Histori Absensi"}),a.jsx(l,{slot:"end",children:a.jsx(c,{onClick:()=>X(!0),style:{color:"#fff"},children:a.jsx(m,{icon:z})})})]})}),a.jsxs(h,{children:[a.jsx(x,{slot:"fixed",onIonRefresh:async e=>{await he(),e.detail.complete()},children:a.jsx(g,{})}),a.jsx("div",{className:"histori-header",children:a.jsx(u,{children:a.jsx(p,{children:a.jsxs("div",{className:"histori-user-info",children:[a.jsx(m,{icon:F,className:"histori-user-icon"}),a.jsxs("div",{children:[a.jsx("h3",{children:e.nama||"Nama User"}),a.jsx("p",{children:e.jabatan||"Jabatan"}),a.jsxs("p",{children:["NIK: ",e.nik||"N/A"]})]})]})})})}),(te||re)&&a.jsxs("div",{className:"histori-filter-summary",children:[a.jsxs(j,{color:"primary",children:[te&&ne&&`${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][parseInt(te)-1]} ${ne}`,te&&re&&" | ",re&&`Status: ${re}`]}),a.jsx(c,{size:"small",fill:"clear",onClick:Se,children:"Reset"})]}),!O&&!Y&&(()=>{if("koperasi"===e.keterangan)return null;const i=we.reduce((e,i)=>e+ke(i).totalDenda,0),t=we.filter(e=>ke(e).hasDenda).length;return i>0?a.jsx(u,{style:{marginBottom:"16px",border:"2px solid #f44336"},children:a.jsx(p,{children:a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px 0"},children:[a.jsxs("div",{children:[a.jsxs("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:"#d32f2f",marginBottom:"4px",display:"flex",alignItems:"center"},children:[a.jsx(m,{icon:W,style:{marginRight:"8px"}}),"Total Denda Periode Ini"]}),a.jsxs("div",{style:{fontSize:"0.9rem",color:"#666"},children:[t," pelanggaran dari ",we.length," hari kerja"]})]}),a.jsx("div",{style:{fontSize:"1.3rem",fontWeight:"bold",color:"#d32f2f",textAlign:"right"},children:be(i)})]})})}):null})(),O&&a.jsxs("div",{className:"histori-loading",children:[a.jsx(f,{name:"crescent"}),a.jsx("p",{children:"Memuat data absensi..."})]}),Y&&!O&&a.jsx("div",{className:"histori-error",children:a.jsx(u,{children:a.jsxs(p,{children:[a.jsx(k,{color:"danger",children:a.jsx("p",{children:Y})}),a.jsx(c,{expand:"block",onClick:he,children:"Coba Lagi"})]})})}),!O&&!Y&&a.jsx("div",{className:"histori-content",children:0===we.length?a.jsx("div",{className:"histori-empty",children:a.jsx(u,{children:a.jsx(p,{children:a.jsx(k,{color:"medium",children:a.jsx("p",{children:"Tidak ada data absensi ditemukan"})})})})}):a.jsx(b,{children:we.map(i=>a.jsxs(u,{className:"histori-card",children:[a.jsx(y,{children:a.jsxs(v,{className:"histori-card-title",children:[a.jsxs("div",{className:"histori-date",children:[a.jsx(m,{icon:M}),a.jsx("span",{children:ge(i.tanggal)})]}),a.jsxs(j,{color:pe(fe(i.jam_masuk,i.tanggal)),children:[a.jsx(m,{icon:je(fe(i.jam_masuk,i.tanggal))}),fe(i.jam_masuk,i.tanggal)]})]})}),a.jsxs(p,{children:[a.jsxs("div",{className:"histori-time-info",children:[a.jsxs("div",{className:"histori-time-item",children:[a.jsx(m,{icon:$,color:"primary"}),a.jsxs("div",{children:[a.jsx("strong",{children:"Masuk:"})," ",ue(i.jam_masuk),i.foto_masuk&&a.jsxs(c,{size:"small",fill:"clear",onClick:()=>ye(xe(i.foto_masuk),`Foto Absen Masuk - ${ge(i.tanggal)}`),children:[a.jsx(m,{icon:R}),"Lihat Foto"]})]})]}),a.jsxs("div",{className:"histori-time-item",children:[a.jsx(m,{icon:$,color:"secondary"}),a.jsxs("div",{children:[a.jsx("strong",{children:"Pulang:"})," ",ue(i.jam_pulang),i.foto_pulang&&a.jsxs(c,{size:"small",fill:"clear",onClick:()=>ye(xe(i.foto_pulang),`Foto Absen Pulang - ${ge(i.tanggal)}`),children:[a.jsx(m,{icon:R}),"Lihat Foto"]})]})]})]}),i.keterangan&&a.jsxs("div",{className:"histori-keterangan",children:[a.jsx("strong",{children:"Keterangan:"})," ",i.keterangan]}),(()=>{if("koperasi"===e.keterangan)return null;const t=ke(i);return t.hasDenda?a.jsxs("div",{style:{marginTop:"12px",padding:"12px",backgroundColor:"#ffebee",borderRadius:"8px",border:"1px solid #f44336"},children:[a.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px",color:"#d32f2f",fontWeight:"bold"},children:[a.jsx(m,{icon:W,style:{marginRight:"8px"}}),"Denda Keterlambatan"]}),t.dendaMessages.map((e,i)=>a.jsxs("div",{style:{fontSize:"0.9rem",color:"#666",marginBottom:"4px"},children:["• ",e]},i)),a.jsxs("div",{style:{marginTop:"8px",padding:"8px",backgroundColor:"#fff",borderRadius:"4px",textAlign:"center",fontWeight:"bold",color:"#d32f2f",fontSize:"1.1rem"},children:["Total Denda: ",be(t.totalDenda)]})]}):null})()]})]},i.id))})}),a.jsxs(S,{isOpen:V,onDidDismiss:()=>X(!1),children:[a.jsx(s,{children:a.jsxs(r,{children:[a.jsx(d,{children:"Filter Histori"}),a.jsx(l,{slot:"end",children:a.jsx(c,{onClick:()=>X(!1),children:a.jsx(m,{icon:J})})})]})}),a.jsx(h,{children:a.jsxs("div",{className:"histori-filter-content",children:[a.jsxs(w,{children:[a.jsx(_,{position:"stacked",children:"Bulan & Tahun"}),a.jsx(D,{value:`${ne}-${te}`,onIonChange:e=>{const i=Array.isArray(e.detail.value)?e.detail.value[0]:e.detail.value;if(i){const e=new Date(i),t=(e.getMonth()+1).toString().padStart(2,"0"),a=e.getFullYear().toString();ae(t),se(a)}},presentation:"month-year",locale:"id-ID"})]}),a.jsxs(w,{children:[a.jsx(_,{position:"stacked",children:"Status"}),a.jsxs(T,{value:re,onIonChange:e=>le(e.detail.value),children:[a.jsx(N,{value:"",children:"Semua Status"}),a.jsx(N,{value:"Tepat Waktu",children:"Tepat Waktu"}),a.jsx(N,{value:"Terlambat",children:"Terlambat"}),a.jsx(N,{value:"Pulang Awal",children:"Pulang Awal"})]})]}),a.jsxs("div",{className:"histori-filter-actions",children:[a.jsx(c,{expand:"block",onClick:()=>{X(!1),de(!0),me("Filter berhasil diterapkan")},children:"Terapkan Filter"}),a.jsx(c,{expand:"block",fill:"outline",onClick:Se,children:"Reset Filter"})]})]})})]}),a.jsxs(S,{isOpen:G,onDidDismiss:ve,children:[a.jsx(s,{children:a.jsxs(r,{children:[a.jsx(d,{children:q?.title}),a.jsx(l,{slot:"end",children:a.jsx(c,{onClick:ve,children:a.jsx(m,{icon:J})})})]})}),a.jsx(h,{children:a.jsx("div",{className:"histori-image-modal",children:q&&a.jsx(I,{src:q.url,alt:q.title})})})]}),a.jsx(A,{isOpen:oe,onDidDismiss:()=>de(!1),message:ce,duration:2e3,color:"success"})]})]})})}}});

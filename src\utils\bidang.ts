import { fetchWithCache } from './networkOptimizer';

export async function fetchAndStoreBidang() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const bidangId = user.bidang_id;
    if (!bidangId) return;

    // Gunakan cache untuk mengurangi request berulang
    const data = await fetchWithCache(
      'https://absensiku.trunois.my.id/api/bidang.php?api_key=absensiku_api_key_2023',
      {},
      30 * 60 * 1000 // Cache 30 menit karena data bidang jarang berubah
    );

    if (data.status === 'success' && Array.isArray(data.data)) {
      const bidang = data.data.filter((b: any) => b.id == bidangId);
      localStorage.setItem('bidang_list', JSON.stringify(bidang));
    }
  } catch (err) {
    console.error('Error fetching bidang:', err);
    // Fallback ke data yang sudah ada di localStorage jika ada
  }
}
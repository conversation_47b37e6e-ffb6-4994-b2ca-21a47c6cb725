import { fetchWithCache } from './networkOptimizer';

export async function fetchAndStoreJamKerja() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const bidangId = user.bidang_id;
    if (!bidangId) return;

    // Gunakan cache untuk mengurangi request berulang
    const data = await fetchWithCache(
      'https://absensiku.trunois.my.id/api/jam_kerja.php?api_key=absensiku_api_key_2023',
      {},
      30 * 60 * 1000 // Cache 30 menit karena data jam kerja jarang berubah
    );

    if (data.status === 'success' && Array.isArray(data.data)) {
      const jamKerja = data.data.filter((j: any) => j.bidang_id == bidangId);
      localStorage.setItem('jam_kerja_list', JSON.stringify(jamKerja));
    }
  } catch (err) {
    console.error('Error fetching jam kerja:', err);
    // Fallback ke data yang sudah ada di localStorage jika ada
  }
}
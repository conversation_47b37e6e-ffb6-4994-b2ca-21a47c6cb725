System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(i,e){"use strict";var n,t,a,s,r,o,d,l,c,p,g,x,h,u,m,f,b,z,j,y,v,k,w,S,D,_,C,I;return{setters:[i=>{n=i.r,t=i.j,a=i.I,s=i.J,r=i.K,o=i.M,d=i.P,l=i.L,c=i.h,p=i.k,g=i.p,x=i.o,h=i.C,u=i.v,m=i.Q,f=i.X,b=i.Y,z=i.w,j=i.O},i=>{y=i.c,v=i.w,k=i.d,w=i.u,S=i.s,D=i.f,_=i.t,C=i.j},i=>{I=i.u},null,null],execute:function(){var e=document.createElement("style");e.textContent=".histori-izin-dinas-page{--background: #f5f5f5}.histori-izin-dinas-header{background:linear-gradient(135deg,#1a65eb,#1a65eb);color:#fff;padding:20px 0}.histori-izin-dinas-content{padding:16px}.histori-izin-dinas-card{margin-bottom:16px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,.1);transition:transform .2s ease,box-shadow .2s ease}.histori-izin-dinas-card:hover{transform:translateY(-2px);box-shadow:0 4px 16px rgba(0,0,0,.15)}.histori-izin-dinas-quota-card{border-left:4px solid #1a65eb;margin-bottom:20px}.histori-izin-dinas-quota-card.limit-reached{border-left-color:#f44336}.histori-izin-dinas-quota-header{display:flex;align-items:center;margin-bottom:12px}.histori-izin-dinas-quota-icon{font-size:1.5rem;margin-right:12px}.histori-izin-dinas-quota-title{font-weight:700;font-size:1.1rem;color:#333}.histori-izin-dinas-quota-subtitle{font-size:.9rem;color:#666}.histori-izin-dinas-quota-count{font-size:1.5rem;font-weight:700;text-align:right}.histori-izin-dinas-quota-count.available{color:#4caf50}.histori-izin-dinas-quota-count.limit-reached{color:#f44336}.histori-izin-dinas-warning-box{margin-top:12px;padding:8px;background-color:#ffebee;border-radius:4px;font-size:.85rem;color:#d32f2f}.histori-izin-dinas-item-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}.histori-izin-dinas-item-date{font-size:.9rem;color:#666;display:flex;align-items:center}.histori-izin-dinas-item-status{display:flex;align-items:center}.histori-izin-dinas-periode{margin-bottom:8px;font-size:.95rem}.histori-izin-dinas-periode-badge{margin-left:8px;font-size:.8rem;color:#666;background-color:#f5f5f5;padding:2px 6px;border-radius:4px}.histori-izin-dinas-tujuan{margin-bottom:8px;font-size:.95rem}.histori-izin-dinas-keterangan{margin-bottom:12px;font-size:.95rem;line-height:1.4}.histori-izin-dinas-approval{font-size:.8rem;color:#666;border-top:1px solid #eee;padding-top:8px}.histori-izin-dinas-loading{text-align:center;padding:50px}.histori-izin-dinas-loading ion-spinner{margin-bottom:16px}.histori-izin-dinas-error{text-align:center;padding:20px;color:#f44336}.histori-izin-dinas-error ion-icon{font-size:3rem;margin-bottom:16px}.histori-izin-dinas-empty{text-align:center;padding:50px;color:#666}.histori-izin-dinas-empty ion-icon{font-size:3rem;margin-bottom:16px}.histori-izin-dinas-fab{--background: #1a65eb;--background-activated: #1557d1;--color: white;--box-shadow: 0 4px 16px rgba(26, 101, 235, .3)}.histori-izin-dinas-fab.disabled{--background: #9e9e9e;--background-activated: #757575;--box-shadow: 0 2px 8px rgba(0, 0, 0, .2)}.histori-izin-dinas-status-approved{color:#4caf50}.histori-izin-dinas-status-pending{color:#ff9800}.histori-izin-dinas-status-rejected{color:#f44336}@media (max-width: 768px){.histori-izin-dinas-content{padding:12px}.histori-izin-dinas-item-header{flex-direction:column;align-items:flex-start;gap:8px}.histori-izin-dinas-quota-header{flex-direction:column;align-items:flex-start;text-align:left}.histori-izin-dinas-quota-count{text-align:left;margin-top:8px}}.histori-izin-dinas-card{animation:slideInUp .3s ease-out}@keyframes slideInUp{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}.histori-izin-dinas-badge{display:inline-flex;align-items:center;padding:4px 8px;border-radius:12px;font-size:.8rem;font-weight:500}.histori-izin-dinas-badge.success{background-color:#e8f5e8;color:#2e7d32}.histori-izin-dinas-badge.warning{background-color:#fff3e0;color:#f57c00}.histori-izin-dinas-badge.danger{background-color:#ffebee;color:#d32f2f}.histori-izin-dinas-fab-container{position:fixed;bottom:20px;right:20px;z-index:1000}.histori-izin-dinas-fab-button{width:56px;height:56px;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 16px rgba(0,0,0,.2);transition:all .3s ease}.histori-izin-dinas-fab-button:hover{transform:scale(1.1);box-shadow:0 6px 20px rgba(0,0,0,.3)}.histori-izin-dinas-fab-button.disabled{opacity:.6;cursor:not-allowed}.histori-izin-dinas-fab-button.disabled:hover{transform:none;box-shadow:0 4px 16px rgba(0,0,0,.2)}.histori-izin-dinas-card-interactive{cursor:pointer;transition:all .2s ease}.histori-izin-dinas-card-interactive:hover{transform:translateY(-2px);box-shadow:0 8px 24px rgba(0,0,0,.15)}.histori-izin-dinas-card-interactive:active{transform:translateY(0);box-shadow:0 2px 8px rgba(0,0,0,.1)}\n",document.head.appendChild(e),i("default",()=>{const i=JSON.parse(localStorage.getItem("user")||"{}"),e=I(),[B,q]=n.useState([]),[A,T]=n.useState(!0),[Y,K]=n.useState(""),[L,M]=n.useState(!1),[R,H]=n.useState(""),[O,W]=n.useState("success"),[P,E]=n.useState(!1),[F,J]=n.useState(0),[N,U]=n.useState({pending:0,approved:0,rejected:0}),G=async()=>{if(!i.id&&!i.nik)return K("Data user tidak ditemukan"),void T(!1);T(!0),K("");try{const e=i.id||i.nik,n=await fetch(`https://absensiku.trunois.my.id/api/izin_dinas.php?api_key=absensiku_api_key_2023&user_id=${e}`),t=await n.json();"success"===t.status?(q(t.data||[]),Q(t.data||[])):K(t.message||"Gagal memuat data izin dinas")}catch(e){K("Terjadi kesalahan saat memuat data")}finally{T(!1)}},Q=i=>{const e=new Date,n=e.getMonth(),t=e.getFullYear(),a=i.filter(i=>{const e=new Date(i.created_at);return e.getMonth()===n&&e.getFullYear()===t}),s={pending:0,approved:0,rejected:0};a.forEach(i=>{const e=i.status.toLowerCase();"pending"===e||"menunggu"===e?s.pending++:"approved"===e||"disetujui"===e?s.approved++:"rejected"!==e&&"ditolak"!==e||s.rejected++}),U(s);const r=s.pending+s.approved;J(r)},X=i=>{switch(i.toLowerCase()){case"approved":case"disetujui":return C;case"pending":case"menunggu":return _;case"rejected":case"ditolak":return D;default:return S}},$=i=>new Date(i).toLocaleDateString("id-ID",{day:"2-digit",month:"short",year:"numeric"}),V=(i,e)=>{const n=new Date(i),t=new Date(e),a=Math.abs(t.getTime()-n.getTime());return Math.ceil(a/864e5)+1},Z=()=>{F>=4?E(!0):e.push("/izin-dinas")};return n.useEffect(()=>{G()},[]),t.jsxs(a,{children:[t.jsx(s,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},children:t.jsxs(r,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[t.jsx(o,{slot:"start",children:t.jsx(d,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),t.jsx(l,{style:{color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center"},children:"Histori Izin Dinas"})]})}),t.jsxs(c,{className:"ion-padding",children:[t.jsx(p,{style:{marginBottom:"16px",border:F>=4?"2px solid #f44336":"2px solid #4caf50"},children:t.jsxs(g,{children:[t.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px 0"},children:[t.jsxs("div",{children:[t.jsxs("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:"#333",marginBottom:"4px",display:"flex",alignItems:"center"},children:[t.jsx(x,{icon:y,style:{marginRight:"8px"}}),"Kuota Izin Dinas Bulan Ini"]}),t.jsx("div",{style:{fontSize:"0.9rem",color:"#666"},children:"Maksimal 4 kali per bulan (pending + approved)"})]}),t.jsxs("div",{style:{fontSize:"1.5rem",fontWeight:"bold",color:F>=4?"#f44336":"#4caf50",textAlign:"right"},children:[F,"/4"]})]}),(N.pending>0||N.approved>0||N.rejected>0)&&t.jsxs("div",{style:{marginTop:"12px",padding:"8px",backgroundColor:"#f8f9fa",borderRadius:"4px",fontSize:"0.8rem",color:"#666"},children:[t.jsx("div",{style:{marginBottom:"4px",fontWeight:"bold"},children:"Detail bulan ini:"}),t.jsxs("div",{style:{display:"flex",gap:"12px",flexWrap:"wrap"},children:[N.pending>0&&t.jsxs("span",{style:{color:"#ff9800"},children:["Pending: ",N.pending]}),N.approved>0&&t.jsxs("span",{style:{color:"#4caf50"},children:["Disetujui: ",N.approved]}),N.rejected>0&&t.jsxs("span",{style:{color:"#f44336"},children:["Ditolak: ",N.rejected," (tidak dihitung)"]})]})]}),F>=4&&t.jsx("div",{style:{marginTop:"12px",padding:"8px",backgroundColor:"#ffebee",borderRadius:"4px",fontSize:"0.85rem",color:"#d32f2f"},children:"⚠️ Kuota izin dinas bulan ini sudah habis. Anda dapat mengajukan izin dinas lagi bulan depan."})]})}),A&&t.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[t.jsx(h,{name:"crescent"}),t.jsx("p",{children:"Memuat data histori izin dinas..."})]}),Y&&!A&&t.jsx(p,{children:t.jsx(g,{children:t.jsxs("div",{style:{textAlign:"center",padding:"20px",color:"#f44336"},children:[t.jsx(x,{icon:v,style:{fontSize:"3rem",marginBottom:"16px"}}),t.jsx("p",{children:Y}),t.jsx(u,{onClick:G,size:"small",children:"Coba Lagi"})]})})}),!A&&!Y&&0===B.length&&t.jsx(p,{children:t.jsx(g,{children:t.jsxs("div",{style:{textAlign:"center",padding:"50px",color:"#666"},children:[t.jsx(x,{icon:k,style:{fontSize:"3rem",marginBottom:"16px"}}),t.jsx("p",{children:"Belum ada histori izin dinas"}),t.jsx(u,{onClick:Z,size:"small",disabled:F>=4,children:"Ajukan Izin Dinas Pertama"})]})})}),!A&&!Y&&B.length>0&&t.jsx(t.Fragment,{children:B.map((i,e)=>t.jsx(p,{style:{marginBottom:"16px"},children:t.jsxs(g,{children:[t.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[t.jsxs("div",{style:{fontSize:"0.9rem",color:"#666",display:"flex",alignItems:"center"},children:[t.jsx(x,{icon:y,style:{marginRight:"4px"}}),$(i.created_at)]}),t.jsxs(m,{color:"approved"===i.status||"disetujui"===i.status?"success":"pending"===i.status||"menunggu"===i.status?"warning":"danger",style:{display:"flex",alignItems:"center"},children:[t.jsx(x,{icon:X(i.status),style:{marginRight:"4px",fontSize:"0.8rem"}}),i.status]})]}),t.jsxs("div",{style:{marginBottom:"8px"},children:[t.jsx("strong",{children:"Periode:"})," ",$(i.tanggal_mulai)," - ",$(i.tanggal_selesai),t.jsxs("span",{style:{marginLeft:"8px",fontSize:"0.8rem",color:"#666",backgroundColor:"#f5f5f5",padding:"2px 6px",borderRadius:"4px"},children:[V(i.tanggal_mulai,i.tanggal_selesai)," hari"]})]}),t.jsxs("div",{style:{marginBottom:"8px"},children:[t.jsx("strong",{children:"Tujuan:"})," ",i.tujuan]}),t.jsxs("div",{style:{marginBottom:"12px"},children:[t.jsx("strong",{children:"Keterangan:"})," ",i.keterangan]}),i.approved_by&&i.approved_at&&t.jsxs("div",{style:{fontSize:"0.8rem",color:"#666",borderTop:"1px solid #eee",paddingTop:"8px"},children:["Direspon oleh: ",i.approved_by," pada ",$(i.approved_at)]})]})},i.id))}),t.jsx(f,{vertical:"bottom",horizontal:"end",slot:"fixed",children:t.jsx(b,{onClick:Z,disabled:F>=4,color:F>=4?"medium":"primary",children:t.jsx(x,{icon:w})})}),t.jsx(z,{isOpen:L,onDidDismiss:()=>M(!1),message:R,duration:3e3,color:O}),t.jsx(j,{isOpen:P,onDidDismiss:()=>E(!1),header:"Kuota Habis",message:"Anda sudah mengajukan 4 izin dinas bulan ini. Kuota maksimal adalah 4 kali per bulan. Silakan coba lagi bulan depan.",buttons:["OK"]})]})]})})}}});

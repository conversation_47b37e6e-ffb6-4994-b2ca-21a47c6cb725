function e(t){return e.result?e.result:t&&"function"==typeof t.getSerializer?(e.result=t.getSerializer(),e.result):Promise.reject(new Error("localforage.getSerializer() was not available! localforage v1.4+ is required!"))}function t(e,n){return t.result=t.result||{},t.result[n]?t.result[n]:e&&"function"==typeof e.getDriver?(t.result[n]=e.getDriver(n),t.result[n]):Promise.reject(new Error("localforage.getDriver() was not available! localforage v1.4+ is required!"))}function n(e){return t(e,e.WEBSQL)}var r=new Promise(function(e,t){"undefined"!=typeof sqlitePlugin?e():"undefined"==typeof cordova?t(new Error("cordova is not defined.")):document.addEventListener("deviceready",function(){return e()},!1)}).catch(function(){return Promise.resolve()});function i(){return r.then(function(){if("undefined"!=typeof sqlitePlugin&&"function"==typeof sqlitePlugin.openDatabase)return sqlitePlugin.openDatabase;throw new Error("SQLite plugin is not present.")})}var o={_driver:"cordovaSQLiteDriver",_initStorage:function(t){var r=this,o={db:null};if(t)for(var u in t)o[u]="string"!=typeof t[u]?t[u].toString():t[u];var a=i().then(function(e){return new Promise(function(t,n){try{o.location=o.location||"default",o.db=e({name:o.name,version:String(o.version),description:o.description,size:o.size,key:o.dbKey,location:o.location})}catch(i){n(i)}o.db.transaction(function(e){e.executeSql("CREATE TABLE IF NOT EXISTS "+o.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],function(){r._dbInfo=o,t()},function(e,t){n(t)})})})}),c=e(r),l=n(r);return Promise.all([c,l,a]).then(function(e){return o.serializer=e[0],a})},_support:function(){return i().then(function(e){return!!e}).catch(function(){return!1})}};!function(e){var t=["clear","getItem","iterate","key","keys","length","removeItem","setItem","dropInstance"];function r(e,t){e[t]=function(){var e=this,r=arguments;return n(e).then(function(n){return n[t].apply(e,r)})}}for(var i=0,o=t.length;i<o;i++)r(e,t[i])}(o);export{o as default};

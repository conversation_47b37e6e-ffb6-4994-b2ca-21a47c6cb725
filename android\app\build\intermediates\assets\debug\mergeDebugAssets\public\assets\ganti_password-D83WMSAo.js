import{r as s,j as a,I as e,J as i,K as n,L as o,h as r,s as t,t as d,u as l,v as p,x as c,O as u}from"./ionic-CJlrxXsE.js";import"./react-vendor-DCX9i6UF.js";const h=()=>{const[h,m]=s.useState(""),[j,k]=s.useState(""),[x,g]=s.useState(""),[w,y]=s.useState(!1),[f,v]=s.useState(""),[S,b]=s.useState(!1);return a.jsxs(e,{children:[a.jsx(i,{children:a.jsx(n,{children:a.jsx(o,{children:"Ganti Password"})})}),a.jsxs(r,{className:"ion-padding",children:[a.jsxs(t,{children:[a.jsx(d,{position:"floating",children:"Password Lama"}),a.jsx(l,{type:"password",value:h,onIonChange:s=>m(s.detail.value)})]}),a.jsxs(t,{children:[a.jsx(d,{position:"floating",children:"Password Baru"}),a.jsx(l,{type:"password",value:j,onIonChange:s=>k(s.detail.value)})]}),a.jsxs(t,{children:[a.jsx(d,{position:"floating",children:"Konfirmasi Password Baru"}),a.jsx(l,{type:"password",value:x,onIonChange:s=>g(s.detail.value)})]}),a.jsx(p,{expand:"block",color:"primary",style:{marginTop:"20px"},onClick:async()=>{if(!h||!j||!x)return v("Semua field wajib diisi"),void y(!0);if(j!==x)return v("Password baru dan konfirmasi password tidak sama"),void y(!0);b(!0);try{const s=localStorage.getItem("user");if(!s)return v("Data pengguna tidak ditemukan, silakan login kembali"),y(!0),void b(!1);const a=JSON.parse(s),e=Array.isArray(a)?a[0]:a,i=await fetch("https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=".concat(encodeURIComponent(e.nik)),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({nik:e.nik,old_password:h,new_password:j})}),n=await i.json();"success"===n.status?(v("Password berhasil diubah"),y(!0),m(""),k(""),g("")):(v(n.message||"Gagal mengganti password"),y(!0))}catch(s){v("Terjadi kesalahan koneksi ke server"),y(!0)}finally{b(!1)}},children:"Simpan Perubahan"}),a.jsx(c,{isOpen:S,message:"Memproses..."}),a.jsx(u,{isOpen:w,onDidDismiss:()=>y(!1),header:"Informasi",message:f,buttons:["OK"]})]})]})};export{h as default};

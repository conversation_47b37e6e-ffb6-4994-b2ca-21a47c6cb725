1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.absensi.pdam"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:55:5-67
13-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:55:22-64
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:56:5-68
14-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:56:22-65
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:57:5-81
15-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:57:22-78
16    <uses-permission android:name="android.permission.CAMERA" />
16-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:58:5-65
16-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:58:22-62
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:59:5-79
17-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:59:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:60:5-81
18-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:60:22-78
19
20    <!-- Camera features for barcode scanner -->
21    <uses-feature
21-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:63:5-85
22        android:name="android.hardware.camera"
22-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:63:19-57
23        android:required="false" />
23-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:63:58-82
24    <uses-feature
24-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:64:5-95
25        android:name="android.hardware.camera.autofocus"
25-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:64:19-67
26        android:required="false" />
26-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:64:68-92
27    <uses-feature
27-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:65:5-91
28        android:name="android.hardware.camera.flash"
28-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:65:19-63
29        android:required="false" />
29-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:65:64-88
30
31    <queries>
31-->[:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
32        <intent>
32-->[:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
33            <action android:name="android.media.action.IMAGE_CAPTURE" />
33-->[:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
33-->[:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
34        </intent>
35    </queries>
36
37    <uses-permission android:name="android.permission.VIBRATE" />
37-->[:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
37-->[:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
38
39    <permission
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
40        android:name="com.absensi.pdam.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.absensi.pdam.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
44
45    <uses-feature
45-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
46        android:name="android.hardware.camera.front"
46-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
47        android:required="false" />
47-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
49        android:name="android.hardware.screen.landscape"
49-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
50        android:required="false" />
50-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
51    <uses-feature
51-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
52        android:name="android.hardware.wifi"
52-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
53        android:required="false" />
53-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
54
55    <application
55-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:4:5-51:19
56        android:allowBackup="true"
56-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:5:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
58        android:debuggable="true"
59        android:extractNativeLibs="false"
60        android:icon="@mipmap/ic_launcher"
60-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:6:9-43
61        android:label="@string/app_name"
61-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:7:9-41
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:8:9-54
63        android:supportsRtl="true"
63-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:9:9-35
64        android:theme="@style/AppTheme" >
64-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:10:9-40
65        <activity
65-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:12:9-25:20
66            android:name="com.absensi.pdam.MainActivity"
66-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:14:13-41
67            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
67-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:13:13-140
68            android:exported="true"
68-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:18:13-36
69            android:label="@string/title_activity_main"
69-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:15:13-56
70            android:launchMode="singleTask"
70-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:17:13-44
71            android:theme="@style/AppTheme.NoActionBarLaunch" >
71-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:16:13-62
72            <intent-filter>
72-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:20:13-23:29
73                <action android:name="android.intent.action.MAIN" />
73-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:21:17-69
73-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:21:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:22:17-77
75-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:22:27-74
76            </intent-filter>
77        </activity>
78
79        <receiver
79-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:27:9-29:40
80            android:name="com.absensi.pdam.AlarmReceiver"
80-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:28:13-42
81            android:exported="false" />
81-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:29:13-37
82        <receiver
82-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:31:9-40:20
83            android:name="com.absensi.pdam.BootReceiver"
83-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:32:13-41
84            android:enabled="true"
84-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:33:13-35
85            android:exported="true" >
85-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:34:13-36
86            <intent-filter>
86-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:35:13-39:29
87                <action android:name="android.intent.action.BOOT_COMPLETED" />
87-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:36:17-79
87-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:36:25-76
88                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
88-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:37:17-86
88-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:37:25-83
89                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
89-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:38:17-84
89-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:38:25-81
90            </intent-filter>
91        </receiver>
92
93        <provider
94            android:name="androidx.core.content.FileProvider"
94-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:43:13-62
95            android:authorities="com.absensi.pdam.fileprovider"
95-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:44:13-64
96            android:exported="false"
96-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:45:13-37
97            android:grantUriPermissions="true" >
97-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:46:13-47
98            <meta-data
98-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:47:13-49:64
99                android:name="android.support.FILE_PROVIDER_PATHS"
99-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:48:17-67
100                android:resource="@xml/file_paths" />
100-->E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:49:17-51
101        </provider>
102
103        <activity
103-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
104            android:name="com.google.android.gms.common.api.GoogleApiActivity"
104-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
105            android:exported="false"
105-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
106            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
106-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
107
108        <provider
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
109            android:name="androidx.startup.InitializationProvider"
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
110            android:authorities="com.absensi.pdam.androidx-startup"
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
111            android:exported="false" >
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
112            <meta-data
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.emoji2.text.EmojiCompatInitializer"
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
114                android:value="androidx.startup" />
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
116-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
117                android:value="androidx.startup" />
117-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
118            <meta-data
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
120                android:value="androidx.startup" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
121        </provider>
122
123        <meta-data
123-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
124            android:name="com.google.android.gms.version"
124-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
125            android:value="@integer/google_play_services_version" />
125-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
126
127        <receiver
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
128            android:name="androidx.profileinstaller.ProfileInstallReceiver"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
129            android:directBootAware="false"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
130            android:enabled="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
131            android:exported="true"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
132            android:permission="android.permission.DUMP" >
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
134                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
137                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
140                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
143                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
144            </intent-filter>
145        </receiver>
146
147        <activity
147-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
148            android:name="com.journeyapps.barcodescanner.CaptureActivity"
148-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
149            android:clearTaskOnLaunch="true"
149-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
150            android:screenOrientation="sensorLandscape"
150-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
151            android:stateNotNeeded="true"
151-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
152            android:theme="@style/zxing_CaptureTheme"
152-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
153            android:windowSoftInputMode="stateAlwaysHidden" />
153-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
154    </application>
155
156</manifest>

import{r as a,j as s,I as t,J as e,K as r,M as n,P as i,L as c,v as l,o,h as d,E as u,F as p,C as h,N as m,k as g,m as j,n as x,Q as y,p as k,s as f,t as S,O as w,w as _,H as b}from"./ionic-CJlrxXsE.js";import{v as N,p as v,c as R,t as A,i as I,f as D}from"./index-BZ7jmVXp.js";import{B,N as T}from"./utils-W2Gk7u7g.js";import"./react-vendor-DCX9i6UF.js";import"./capacitor-DGgumwVn.js";const O="https://absensiku.trunois.my.id/api",P="absensiku_api_key_2023";class L{static async getAllRapat(){try{const a=await fetch("".concat(O,"/api_rapat.php?api_key=").concat(P)),s=await a.json();return"success"===s.status&&Array.isArray(s.data)?s.data:[]}catch(a){return[]}}static async getRapatById(a){try{const s=await fetch("".concat(O,"/api_rapat.php?api_key=").concat(P,"&id=").concat(a)),t=await s.json();return"success"===t.status&&Array.isArray(t.data)&&t.data.length>0?t.data[0]:null}catch(s){return null}}static async getAllRapatPeserta(){try{const a=await fetch("".concat(O,"/api_rapat_peserta.php?api_key=").concat(P)),s=await a.json();return"success"===s.status&&Array.isArray(s.data)?s.data:[]}catch(a){return[]}}static async getRapatPesertaByUserId(a){try{const s=await fetch("".concat(O,"/api_rapat_peserta.php?api_key=").concat(P)),t=await s.json();if("success"===t.status&&Array.isArray(t.data)){return t.data.filter(s=>s.user_id==a)}return[]}catch(s){return[]}}static async getRapatPesertaByRapatId(a){try{const s=await fetch("".concat(O,"/api_rapat_peserta.php?api_key=").concat(P,"&rapat_id=").concat(a)),t=await s.json();return"success"===t.status&&Array.isArray(t.data)?t.data:[]}catch(s){return[]}}static async updateStatusKehadiran(a,s,t,e){try{const n=e||(new Date).toISOString().slice(0,19).replace("T"," "),i={api_key:P,rapat_id:a,user_id:s,status:t,waktu_hadir:n},c=await fetch("".concat(O,"/api_rapat_peserta.php"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!c.ok){await c.text();return!1}const l=await c.text();try{const a=JSON.parse(l);return"success"===a.status?(localStorage.removeItem("rapat_peserta_cache"),!0):"warning"===a.status}catch(r){return!1}}catch(n){return!1}}static async getRapatByBarcode(a){try{const s=await this.getAllRapat();return s.find(s=>s.barcode_value===a)||null}catch(s){return null}}static async isUserRegisteredForRapat(a,s){try{const t=await this.getRapatPesertaByRapatId(s);return t.find(s=>s.user_id==a)||null}catch(t){return null}}static async processRapatAbsensi(a,s){try{const t=await this.getRapatByBarcode(a);if(!t)return{success:!1,message:"Barcode rapat tidak ditemukan"};const e=await this.isUserRegisteredForRapat(s,t.id);if(!e)return{success:!1,message:"Anda tidak terdaftar sebagai peserta rapat ini"};if("hadir"===e.status)return{success:!1,message:"Anda sudah melakukan absensi untuk rapat ini"};return await this.updateStatusKehadiran(t.id,s,"hadir")?{success:!0,message:"Berhasil absen untuk rapat: ".concat(t.judul),rapat:t}:{success:!1,message:"Gagal mengupdate status kehadiran. Silakan coba lagi."}}catch(t){return{success:!1,message:"Terjadi kesalahan saat memproses absensi: "+t}}}static formatTanggal(a){return new Date(a).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}static formatWaktu(a){return a.substring(0,5)}static isRapatActive(a){const s=new Date,t=new Date(a.tanggal),[e,r]=a.waktu_mulai.split(":").map(Number),[n,i]=a.waktu_selesai.split(":").map(Number),c=new Date(t);c.setHours(e,r,0,0);const l=new Date(t);return l.setHours(n,i,0,0),s>=c&&s<=l}static async saveRapatToLocalStorage(){try{const a=await this.getAllRapat(),s=await this.getAllRapatPeserta();localStorage.setItem("rapat_list",JSON.stringify(a)),localStorage.setItem("rapat_peserta_list",JSON.stringify(s)),localStorage.setItem("rapat_last_sync",(new Date).toISOString())}catch(a){}}static getRapatFromLocalStorage(){try{const a=JSON.parse(localStorage.getItem("rapat_list")||"[]"),s=JSON.parse(localStorage.getItem("rapat_peserta_list")||"[]");return{rapatList:a,rapatPesertaList:s,lastSync:localStorage.getItem("rapat_last_sync")}}catch(a){return{rapatList:[],rapatPesertaList:[],lastSync:null}}}}const J=()=>{const O=JSON.parse(localStorage.getItem("user")||"{}"),[P,J]=a.useState([]),[C,H]=a.useState([]),[F,K]=a.useState(!1),[U,W]=a.useState(!1),[E,z]=a.useState(!1),G=a.useRef(null),[M,Q]=a.useState(null),[V,q]=a.useState(null),[X,Y]=a.useState(!1),[Z,$]=a.useState(""),[aa,sa]=a.useState(!1),[ta,ea]=a.useState(""),[ra,na]=a.useState("success"),[ia,ca]=a.useState(!1),la=async()=>{K(!0);try{const a=await L.getAllRapat();J(a)}catch(a){J([])}finally{K(!1)}},oa=async()=>{try{const a=O.id||O.nik,s=await L.getRapatPesertaByUserId(a);H(s)}catch(a){H([])}},da=a=>L.formatWaktu(a),ua=async()=>{try{z(!0)}catch(a){ea("Gagal membuka scanner"),na("danger"),sa(!0)}};a.useEffect(()=>{const a=new B;return q(a),()=>{a.reset()}},[]),a.useEffect(()=>(E&&V?pa():ha(),()=>{ha()}),[E,V]);const pa=async()=>{if(V&&G.current)try{W(!0);const a=await V.decodeOnceFromVideoDevice(void 0,G.current);a&&(W(!1),await ja(a.getText()),z(!1))}catch(a){a instanceof T?U&&setTimeout(()=>{pa()},100):(ea("Gagal melakukan scan barcode"),na("danger"),sa(!0),W(!1))}},ha=()=>{V&&V.reset(),W(!1)},ma=()=>{z(!1),ha()},ga=a=>{const s=new Date,t=s.toISOString().split("T")[0];if(a.tanggal&&a.tanggal<t)return!0;if(a.tanggal&&a.tanggal>t)return!1;if(!a.waktu_selesai)return!1;return s>new Date("".concat(t,"T").concat(a.waktu_selesai))},ja=async a=>{try{const s=O.id||O.nik,t=await L.processRapatAbsensi(a,s);t.success?(ea(t.message),na("success"),sa(!0),localStorage.removeItem("rapat_peserta_cache"),setTimeout(async()=>{await oa(),H(a=>[...a])},1e3)):(ea(t.message),na(t.message.includes("tidak terdaftar")?"warning":"danger"),sa(!0))}catch(s){ea("Terjadi kesalahan saat memproses barcode"),na("danger"),sa(!0)}};a.useEffect(()=>{la(),oa()},[]);return s.jsxs(t,{children:[s.jsx(e,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:"80px"},children:s.jsxs(r,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[s.jsx(n,{slot:"start",children:s.jsx(i,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),s.jsx(c,{style:{color:"#fff",fontWeight:"600",fontSize:"1.2rem"},children:"Rapat"}),s.jsx(n,{slot:"end",children:s.jsx(l,{onClick:ua,disabled:U,style:{color:"#fff"},children:s.jsx(o,{icon:N})})})]})}),s.jsxs(d,{fullscreen:!0,className:"rapat-content",children:[s.jsx(u,{slot:"fixed",onIonRefresh:async a=>{ca(!0);try{await Promise.all([la(),oa()]),await L.saveRapatToLocalStorage()}catch(s){}finally{ca(!1),a.detail.complete()}},children:s.jsx(p,{})}),F?s.jsxs("div",{className:"loading-container",children:[s.jsx(h,{name:"crescent"}),s.jsx(m,{children:"Memuat data rapat..."})]}):s.jsx("div",{className:"rapat-container",children:0===P.length?s.jsxs("div",{className:"empty-state",children:[s.jsx(o,{icon:v,size:"large"}),s.jsxs(m,{children:[s.jsx("h3",{children:"Tidak ada rapat"}),s.jsx("p",{children:"Belum ada rapat yang tersedia saat ini"})]})]}):P.filter(a=>{const s=O.id||O.nik;return C.some(t=>t.rapat_id==a.id&&t.user_id==s)}).map(a=>{const t=O.id||O.nik,e=(a=>{const s=O.id||O.nik,t=C.find(t=>t.rapat_id==a&&t.user_id==s);return t?t.status:null})(a.id),r=C.some(s=>s.rapat_id==a.id&&s.user_id==t),n=L.isRapatActive(a);return s.jsxs(g,{className:"rapat-card ".concat(n?"active-rapat":""),children:[s.jsx(j,{children:s.jsxs("div",{className:"rapat-header",children:[s.jsx(x,{children:a.judul}),s.jsxs("div",{className:"badge-container",children:[n&&s.jsx(y,{color:"warning",className:"status-badge",children:"Sedang Berlangsung"}),r&&s.jsx(y,{color:"hadir"===e?"success":"medium",className:"status-badge",children:"hadir"===e?"Hadir":"Belum Absen"})]})]})}),s.jsxs(k,{children:[s.jsxs("div",{className:"rapat-details",children:[s.jsxs(f,{lines:"none",className:"detail-item",children:[s.jsx(o,{icon:R,slot:"start"}),s.jsxs(S,{children:[s.jsx("h3",{children:"Tanggal"}),s.jsx("p",{children:(i=a.tanggal,L.formatTanggal(i))})]})]}),s.jsxs(f,{lines:"none",className:"detail-item",children:[s.jsx(o,{icon:A,slot:"start"}),s.jsxs(S,{children:[s.jsx("h3",{children:"Waktu"}),s.jsxs("p",{children:[da(a.waktu_mulai)," - ",da(a.waktu_selesai)]})]})]}),s.jsxs(f,{lines:"none",className:"detail-item",children:[s.jsx(o,{icon:I,slot:"start"}),s.jsxs(S,{children:[s.jsx("h3",{children:"Lokasi"}),s.jsx("p",{children:a.lokasi})]})]}),a.deskripsi&&s.jsx(f,{lines:"none",className:"detail-item",children:s.jsxs(S,{children:[s.jsx("h3",{children:"Deskripsi"}),s.jsx("p",{children:a.deskripsi})]})})]}),r&&"hadir"!==e&&!ga(a)&&s.jsxs(l,{expand:"block",onClick:ua,disabled:U,className:"scan-button",children:[s.jsx(o,{icon:N,slot:"start"}),U?"Scanning...":"Scan Barcode Absensi"]}),r&&"hadir"!==e&&ga(a)&&s.jsx(m,{color:"medium",className:"time-expired",children:s.jsx("p",{children:"Waktu absensi telah berakhir"})})]})]},a.id);var i})}),s.jsx(w,{isOpen:X,onDidDismiss:()=>Y(!1),header:"Informasi",message:Z,buttons:["OK"]}),s.jsx(_,{isOpen:aa,onDidDismiss:()=>sa(!1),message:ta,duration:3e3,color:ra})]}),s.jsxs(b,{isOpen:E,onDidDismiss:ma,children:[s.jsx(e,{children:s.jsxs(r,{children:[s.jsx(c,{children:"Scan Barcode Rapat"}),s.jsx(n,{slot:"end",children:s.jsx(l,{onClick:ma,children:s.jsx(o,{icon:D})})})]})}),s.jsx(d,{className:"scanner-modal-content ".concat(U?"scanner-active":""),children:s.jsxs("div",{className:"scanner-container",children:[s.jsxs("div",{className:"scanner-preview ".concat(U?"scanning":""),id:"scanner-preview",children:[s.jsx("video",{ref:G,className:"scanner-video",autoPlay:!0,playsInline:!0,muted:!0}),s.jsxs("div",{className:"scanner-frame",children:[s.jsxs("div",{className:"scanner-corners",children:[s.jsx("div",{className:"corner top-left"}),s.jsx("div",{className:"corner top-right"}),s.jsx("div",{className:"corner bottom-left"}),s.jsx("div",{className:"corner bottom-right"})]}),U&&s.jsx("div",{className:"scanning-indicator",children:s.jsx("div",{className:"scan-line"})})]})]}),s.jsx("div",{className:"scanner-instructions",children:s.jsxs(m,{children:[s.jsx("h3",{children:U?"Scanning...":"Arahkan kamera ke barcode rapat"}),s.jsx("p",{children:U?"Pastikan barcode berada di dalam kotak dan terlihat jelas":"Kamera akan aktif secara otomatis setelah menekan tombol mulai scan"})]})}),s.jsxs("div",{className:"scanner-actions",children:[!U&&s.jsxs(l,{expand:"block",onClick:()=>{pa()},color:"primary",children:[s.jsx(o,{icon:N,slot:"start"}),"Mulai Scan"]}),s.jsxs(l,{expand:"block",fill:"outline",onClick:ma,color:"medium",children:[s.jsx(o,{icon:D,slot:"start"}),"Tutup Scanner"]})]})]})})]})]})};export{J as default};

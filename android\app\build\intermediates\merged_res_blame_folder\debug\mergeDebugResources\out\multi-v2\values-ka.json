{"logs": [{"outputFile": "com.absensi.pdam.app-mergeDebugResources-39:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b51adc917640d95afb5d4798abf9ab6\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4485,4590,4739,4867,4977,5131,5265,5387,5638,5811,5919,6074,6202,6363,6502,6568,6629", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4585,4734,4862,4972,5126,5260,5382,5490,5806,5914,6069,6197,6358,6497,6563,6624,6700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f6286dbecde1ffd3ab412947624b8daf\\transformed\\material-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1454,1518,1584,1644,1713,1774,1828,1943,2002,2062,2116,2188,2318,2406,2485,2583,2671,2755,2893,2971,3047,3186,3280,3360,3416,3470,3536,3609,3687,3758,3842,3915,3993,4066,4141,4251,4341,4416,4510,4608,4682,4759,4859,4912,4996,5064,5153,5242,5304,5369,5432,5502,5609,5709,5809,5905,5965,6023,6103,6193,6268", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1449,1513,1579,1639,1708,1769,1823,1938,1997,2057,2111,2183,2313,2401,2480,2578,2666,2750,2888,2966,3042,3181,3275,3355,3411,3465,3531,3604,3682,3753,3837,3910,3988,4061,4136,4246,4336,4411,4505,4603,4677,4754,4854,4907,4991,5059,5148,5237,5299,5364,5427,5497,5604,5704,5804,5900,5960,6018,6098,6188,6263,6344"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3104,3178,3262,3355,4173,4272,4397,6705,6768,6835,6932,7001,7064,7151,7215,7281,7341,7410,7471,7525,7640,7699,7759,7813,7885,8015,8103,8182,8280,8368,8452,8590,8668,8744,8883,8977,9057,9113,9167,9233,9306,9384,9455,9539,9612,9690,9763,9838,9948,10038,10113,10207,10305,10379,10456,10556,10609,10693,10761,10850,10939,11001,11066,11129,11199,11306,11406,11506,11602,11662,11720,11882,11972,12047", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "318,3099,3173,3257,3350,3444,4267,4392,4480,6763,6830,6927,6996,7059,7146,7210,7276,7336,7405,7466,7520,7635,7694,7754,7808,7880,8010,8098,8177,8275,8363,8447,8585,8663,8739,8878,8972,9052,9108,9162,9228,9301,9379,9450,9534,9607,9685,9758,9833,9943,10033,10108,10202,10300,10374,10451,10551,10604,10688,10756,10845,10934,10996,11061,11124,11194,11301,11401,11501,11597,11657,11715,11795,11967,12042,12123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\db15cf71e8af3edb1bafe5c13aaffcb3\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,11800", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,11877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\77bafeffa40caf3f8cdeb54ad021e60d\\transformed\\play-services-basement-18.4.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5495", "endColumns": "142", "endOffsets": "5633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6f0193beb95545e88dd0f0a864925327\\transformed\\core-1.15.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3545,3647,3746,3845,3951,4055,12128", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3540,3642,3741,3840,3946,4050,4168,12224"}}]}]}
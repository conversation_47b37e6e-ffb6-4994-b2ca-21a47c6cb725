import{r as s,a2 as a,j as l,I as i,J as n,K as e,L as o,h as d,a3 as c,k as r,m as t,n as h,a4 as j,p as m,q as u,s as x,o as v,t as f,v as p,G as N,a5 as b,Q as g}from"./ionic-CJlrxXsE.js";import{A as k,B as _,C as w,i as y,D as S,c as R,t as I}from"./index-BZ7jmVXp.js";import"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";import"./capacitor-DGgumwVn.js";const J=()=>{var J,K,A,P;const[C,L]=s.useState(null),[M,B]=s.useState(!0),G=a(),O=(()=>{try{const s=localStorage.getItem("user"),a=s?JSON.parse(s):{};return Array.isArray(a)?a[0]||{}:a}catch(s){return{}}})();return s.useEffect(()=>{const s=localStorage.getItem("user");if(s){const a=JSON.parse(s),l=Array.isArray(a)?a[0]:a,i=null==l?void 0:l.nik;if(!i)return void B(!1);fetch("https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=".concat(encodeURIComponent(i))).then(s=>s.json()).then(s=>{var a,n,e,o,d;if("success"===s.status){const r=(Array.isArray(s.data)?s.data:[s.data]).find(s=>String(null==s?void 0:s.nik)===String(i));if(r){L(r);try{const s={id:null!=(a=null==r?void 0:r.id)?a:null==l?void 0:l.id,nik:null==l?void 0:l.nik,nama:(null==r?void 0:r.nama)||(null==r?void 0:r.nama_karyawan)||(null==r?void 0:r.name)||(null==l?void 0:l.nama),jabatan:null!=(n=null==r?void 0:r.jabatan)?n:null==l?void 0:l.jabatan,bidang_id:null!=(e=null==r?void 0:r.bidang_id)?e:null==l?void 0:l.bidang_id,bidang:(null==r?void 0:r.bidang)||(null==r?void 0:r.nama_bidang)||(null==r?void 0:r.bidang_nama)||(null==l?void 0:l.bidang),foto_profil:(null==r?void 0:r.foto_profil)||(null==r?void 0:r.foto)||(null==l?void 0:l.foto_profil),lokasi_id:null!=(o=null==r?void 0:r.lokasi_id)?o:null==l?void 0:l.lokasi_id,nama_lokasi:(null==r?void 0:r.nama_lokasi)||(null==r?void 0:r.lokasi)||(null==r?void 0:r.nama_lokasi_kerja)||(null==l?void 0:l.nama_lokasi),allow_flexible_schedule:null!=(d=null==r?void 0:r.allow_flexible_schedule)?d:null==l?void 0:l.allow_flexible_schedule},i={...l,...s};localStorage.setItem("user",JSON.stringify(i))}catch(c){}}}B(!1)}).catch(s=>{B(!1)})}},[]),M?l.jsxs(i,{children:[l.jsx(n,{children:l.jsx(e,{children:l.jsx(o,{children:"Profil Karyawan"})})}),l.jsxs(d,{className:"profile-page",fullscreen:!0,children:[l.jsxs("div",{className:"profile-hero",children:[l.jsx("div",{className:"avatar-wrap skeleton",children:l.jsx(c,{animated:!0,style:{width:120,height:120,borderRadius:"50%"}})}),l.jsxs("div",{className:"name-wrap",children:[l.jsx(c,{animated:!0,style:{width:"60%",height:20,borderRadius:8}}),l.jsx(c,{animated:!0,style:{width:"40%",height:16,borderRadius:8,marginTop:8}})]}),l.jsxs("div",{className:"chip-wrap",children:[l.jsx(c,{animated:!0,style:{width:120,height:28,borderRadius:20}}),l.jsx(c,{animated:!0,style:{width:140,height:28,borderRadius:20}})]})]}),l.jsxs(r,{className:"profile-card glass",children:[l.jsxs(t,{children:[l.jsx(h,{children:"Informasi Karyawan"}),l.jsx(j,{children:"Memuat data..."})]}),l.jsxs(m,{children:[l.jsxs(u,{lines:"none",className:"info-list",children:[l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:k,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"NIK"}),l.jsx(c,{animated:!0,style:{width:"40%",height:16,borderRadius:6}})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:_,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Jabatan"}),l.jsx(c,{animated:!0,style:{width:"50%",height:16,borderRadius:6}})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:w,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Bidang"}),l.jsx(c,{animated:!0,style:{width:"35%",height:16,borderRadius:6}})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:y,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Lokasi"}),l.jsx(c,{animated:!0,style:{width:"45%",height:16,borderRadius:6}})]})]})]}),l.jsx("div",{className:"action-group",children:l.jsxs(p,{expand:"block",color:"warning",disabled:!0,children:[l.jsx(v,{icon:S,slot:"start"}),"Ganti Password"]})})]})]})]})]}):l.jsxs(i,{children:[l.jsx(n,{children:l.jsx(e,{children:l.jsx(o,{children:"Profil Karyawan"})})}),l.jsxs(d,{className:"profile-page",fullscreen:!0,children:[(()=>{const s={foto_profil:(null==O?void 0:O.foto_profil)||(null==C?void 0:C.foto_profil)||(null==C?void 0:C.foto)||null,nama:(null==O?void 0:O.nama)||(null==C?void 0:C.nama)||(null==C?void 0:C.nama_karyawan)||(null==O?void 0:O.name)||"-",jabatan:(null==O?void 0:O.jabatan)||(null==C?void 0:C.jabatan)||"-",bidang:(null==O?void 0:O.bidang)||(null==C?void 0:C.bidang)||(null==C?void 0:C.nama_bidang)||"-",nama_lokasi:(null==O?void 0:O.nama_lokasi)||(null==C?void 0:C.nama_lokasi)||(null==C?void 0:C.lokasi)||(null==C?void 0:C.nama_lokasi_kerja)||"-",nik:(null==O?void 0:O.nik)||(null==C?void 0:C.nik)||"-"};return l.jsxs("div",{className:"profile-hero",children:[l.jsx("div",{className:"avatar-wrap",children:l.jsx(N,{className:"profile-avatar",children:s.foto_profil?l.jsx("img",{src:"https://absensiku.trunois.my.id/uploads/".concat(s.foto_profil),alt:"Foto Profil"}):l.jsx("div",{className:"default-avatar",children:s.nama.charAt(0).toUpperCase()})})}),l.jsx("h2",{className:"profile-name",children:s.nama}),l.jsx("p",{className:"profile-role",children:s.jabatan}),l.jsxs("div",{className:"chip-wrap",children:[l.jsxs(b,{color:"light",className:"profile-chip",children:[l.jsx(v,{icon:w}),l.jsx("span",{children:s.bidang})]}),l.jsxs(b,{color:"light",className:"profile-chip",children:[l.jsx(v,{icon:y}),l.jsx("span",{children:s.nama_lokasi})]})]})]})})(),l.jsxs(r,{className:"profile-card glass",children:[l.jsxs(t,{children:[l.jsx(h,{children:"Informasi Karyawan"}),l.jsx(j,{children:"Detail singkat"})]}),l.jsxs(m,{children:[l.jsxs(u,{lines:"none",className:"info-list",children:[l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:k,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"NIK"}),l.jsx("h3",{className:"value",children:null!=(J=(null==O?void 0:O.nik)||(null==C?void 0:C.nik))?J:"-"})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:_,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Jabatan"}),l.jsx("h3",{className:"value",children:null!=(K=(null==O?void 0:O.jabatan)||(null==C?void 0:C.jabatan))?K:"-"})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:w,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Bidang"}),l.jsx("h3",{className:"value",children:null!=(A=(null==O?void 0:O.bidang)||(null==C?void 0:C.bidang)||(null==C?void 0:C.nama_bidang))?A:"-"})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:y,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Lokasi"}),l.jsx("h3",{className:"value",children:null!=(P=(null==O?void 0:O.nama_lokasi)||(null==C?void 0:C.nama_lokasi)||(null==C?void 0:C.lokasi)||(null==C?void 0:C.nama_lokasi_kerja))?P:"-"})]})]})]}),l.jsx("div",{className:"action-group",children:l.jsxs(p,{expand:"block",color:"warning",onClick:()=>G.push("/ganti-password","forward"),children:[l.jsx(v,{icon:S,slot:"start"}),"Ganti Password"]})})]})]}),(()=>{const s=(null==O?void 0:O.allow_flexible_schedule)||(null==C?void 0:C.allow_flexible_schedule);return 1===s||"1"===s?null:l.jsxs(r,{className:"profile-card glass",children:[l.jsxs(t,{children:[l.jsx(h,{children:"Jadwal Kerja"}),l.jsx(j,{children:"Jam kerja karyawan"})]}),l.jsx(m,{children:l.jsxs(u,{lines:"none",className:"info-list",children:[l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:R,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Senin - Kamis"}),l.jsxs("div",{className:"schedule-info",children:[l.jsxs(g,{color:"success",className:"schedule-badge",children:[l.jsx(v,{icon:I,slot:"start"}),"Masuk: 07:02"]}),l.jsxs(g,{color:"danger",className:"schedule-badge",children:[l.jsx(v,{icon:I,slot:"start"}),"Pulang: 15:30"]})]})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:R,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Jumat"}),l.jsxs("div",{className:"schedule-info",children:[l.jsxs(g,{color:"success",className:"schedule-badge",children:[l.jsx(v,{icon:I,slot:"start"}),"Masuk: 06:30"]}),l.jsxs(g,{color:"danger",className:"schedule-badge",children:[l.jsx(v,{icon:I,slot:"start"}),"Pulang: 11:30"]})]})]})]}),l.jsxs(x,{className:"info-item",children:[l.jsx(v,{icon:R,slot:"start",className:"info-icon"}),l.jsxs(f,{children:[l.jsx("p",{className:"label",children:"Sabtu - Minggu"}),l.jsx("div",{className:"schedule-info",children:l.jsxs(g,{color:"medium",className:"schedule-badge",children:[l.jsx(v,{icon:I,slot:"start"}),"Libur"]})})]})]})]})})]})})()]})]})};export{J as default};

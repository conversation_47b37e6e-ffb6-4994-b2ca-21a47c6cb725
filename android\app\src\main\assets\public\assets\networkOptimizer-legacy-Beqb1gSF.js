System.register([],function(e,t){"use strict";return{execute:function(){const t=new class{cache=new Map;pendingRequests=new Map;CACHE_DURATION={SHORT:3e5,MEDIUM:18e5,LONG:864e5};async fetchWithCache(e,t={},s=this.CACHE_DURATION.MEDIUM){const a=this.generateCacheKey(e,t),n=this.getFromCache(a);if(n)return n;if(this.pendingRequests.has(a))return this.pendingRequests.get(a);const r=this.makeRequest(e,t).then(e=>(this.setCache(a,e,s),e)).finally(()=>{this.pendingRequests.delete(a)});return this.pendingRequests.set(a,r),r}async fetchWithRetry(e,t={},s=3,a=1e3){let n;for(let c=0;c<=s;c++)try{const s=new AbortController,a=setTimeout(()=>s.abort(),1e4),n=await fetch(e,{...t,signal:s.signal});if(clearTimeout(a),!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);return await n.json()}catch(r){n=r,c<s&&await this.delay(a*Math.pow(2,c))}throw n}async batchRequests(e,t=3){const s=[];for(let a=0;a<e.length;a+=t){const n=e.slice(a,a+t);(await Promise.allSettled(n.map(e=>e()))).forEach((e,t)=>{"fulfilled"===e.status?s[a+t]=e.value:s[a+t]=null}),a+t<e.length&&await this.delay(500)}return s}preloadResources(e){e.forEach(e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)})}clearExpiredCache(){const e=Date.now();for(const[t,s]of this.cache.entries())e>s.expiry&&this.cache.delete(t)}getCacheStats(){return{size:this.cache.size,hitRate:0}}generateCacheKey(e,t){return`${t.method||"GET"}:${e}:${t.body?JSON.stringify(t.body):""}`}getFromCache(e){const t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),null):t.data:null}setCache(e,t,s){this.cache.set(e,{data:t,timestamp:Date.now(),expiry:Date.now()+s})}async makeRequest(e,t){const s=await fetch(e,t);if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return s.json()}delay(e){return new Promise(t=>setTimeout(t,e))}};e("f",(e,s,a)=>t.fetchWithCache(e,s,a)),e("b",(e,s)=>t.batchRequests(e,s)),setInterval(()=>{t.clearExpiredCache()},6e5)}}});

import{a9 as e,aa as t}from"./ionic-CJlrxXsE.js";import"./react-vendor-DCX9i6UF.js";
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const o=o=>{if(void 0===e)return;let d,l,f,v=0;const u=o.getBoolean("animated",!0)&&o.getBoolean("rippleEffect",!0),p=new WeakMap,m=()=>{f&&clearTimeout(f),f=void 0,d&&(E(!1),d=void 0)},L=(e,o)=>{if(e&&e===d)return;f&&clearTimeout(f),f=void 0;const{x:i,y:s}=t(o);if(d){if(p.has(d))throw new Error("internal error");d.classList.contains(a)||h(d,i,s),E(!0)}if(e){const t=p.get(e);t&&(clearTimeout(t),p.delete(e)),e.classList.remove(a);const o=()=>{h(e,i,s),f=void 0};n(e)?o():f=setTimeout(o,r)}d=e},h=(e,t,o)=>{if(v=Date.now(),e.classList.add(a),!u)return;const i=s(e);null!==i&&(w(),l=i.addRipple(t,o))},w=()=>{void 0!==l&&(l.then(e=>e()),l=void 0)},E=e=>{w();const t=d;if(!t)return;const o=c-Date.now()+v;if(e&&o>0&&!n(t)){const e=setTimeout(()=>{t.classList.remove(a),p.delete(t)},c);p.set(t,e)}else t.classList.remove(a)};e.addEventListener("ionGestureCaptured",m),e.addEventListener("pointerdown",e=>{d||2===e.button||L(i(e),e)},!0),e.addEventListener("pointerup",e=>{L(void 0,e)},!0),e.addEventListener("pointercancel",m,!0)},i=e=>{if(void 0===e.composedPath)return e.target.closest(".ion-activatable");{const t=e.composedPath();for(let e=0;e<t.length-2;e++){const o=t[e];if(!(o instanceof ShadowRoot)&&o.classList.contains("ion-activatable"))return o}}},n=e=>e.classList.contains("ion-activatable-instant"),s=e=>{if(e.shadowRoot){const t=e.shadowRoot.querySelector("ion-ripple-effect");if(t)return t}return e.querySelector("ion-ripple-effect")},a="ion-activated",r=100,c=150;export{o as startTapClick};

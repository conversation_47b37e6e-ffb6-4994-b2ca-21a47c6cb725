// Konfigurasi performa untuk aplikasi Ionic React
export const PERFORMANCE_CONFIG = {
  // Network settings
  NETWORK: {
    TIMEOUT: 10000, // 10 detik timeout untuk perangkat lambat
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
    BATCH_SIZE: 2, // Ukuran batch kecil untuk perangkat spesifikasi rendah
    CACHE_DURATION: {
      SHORT: 5 * 60 * 1000,    // 5 menit
      MEDIUM: 30 * 60 * 1000,  // 30 menit
      LONG: 24 * 60 * 60 * 1000 // 24 jam
    }
  },

  // UI settings
  UI: {
    DEBOUNCE_DELAY: 300,
    THROTTLE_DELAY: 1000,
    LAZY_LOAD_THRESHOLD: 0.1,
    LAZY_LOAD_ROOT_MARGIN: '50px',
    CLOCK_UPDATE_INTERVAL: 30000, // Update clock setiap 30 detik untuk hemat battery
    SYNC_DELAY: 3000 // Delay sync untuk perangkat lambat
  },

  // Memory management
  MEMORY: {
    MAX_CACHE_SIZE: 50, // Maksimal 50 item dalam cache
    CACHE_CLEANUP_INTERVAL: 10 * 60 * 1000, // Cleanup setiap 10 menit
    MAX_BACKUP_ITEMS: 30, // Maksimal 30 item backup
    IMAGE_QUALITY: 0.8, // Kualitas gambar 80% untuk menghemat memory
    MAX_IMAGE_SIZE: 1024 * 1024 // Maksimal 1MB per gambar
  },

  // Storage settings
  STORAGE: {
    COMPRESSION_ENABLED: true,
    AUTO_CLEANUP_ENABLED: true,
    SYNC_BATCH_SIZE: 5,
    OFFLINE_QUEUE_LIMIT: 100
  },

  // Performance monitoring
  MONITORING: {
    ENABLED: true,
    LOG_SLOW_OPERATIONS: true,
    SLOW_OPERATION_THRESHOLD: 1000, // 1 detik
    MEMORY_WARNING_THRESHOLD: 50 * 1024 * 1024, // 50MB
    FPS_MONITORING: false // Disable untuk perangkat lambat
  }
};

// Utility functions untuk performance monitoring
export class PerformanceMonitor {
  private static measurements = new Map<string, number>();

  static startMeasurement(name: string): void {
    if (!PERFORMANCE_CONFIG.MONITORING.ENABLED) return;
    this.measurements.set(name, performance.now());
  }

  static endMeasurement(name: string): number {
    if (!PERFORMANCE_CONFIG.MONITORING.ENABLED) return 0;
    
    const startTime = this.measurements.get(name);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.measurements.delete(name);

    if (PERFORMANCE_CONFIG.MONITORING.LOG_SLOW_OPERATIONS && 
        duration > PERFORMANCE_CONFIG.MONITORING.SLOW_OPERATION_THRESHOLD) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  static getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  static checkMemoryUsage(): void {
    const memoryUsage = this.getMemoryUsage();
    if (memoryUsage > PERFORMANCE_CONFIG.MONITORING.MEMORY_WARNING_THRESHOLD) {
      console.warn(`High memory usage detected: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }
  }
}

// Device capability detection
export class DeviceCapability {
  private static _isLowEndDevice: boolean | null = null;

  static isLowEndDevice(): boolean {
    if (this._isLowEndDevice !== null) {
      return this._isLowEndDevice;
    }

    // Deteksi perangkat spesifikasi rendah berdasarkan berbagai faktor
    const factors = {
      memory: this.getDeviceMemory() < 4, // Kurang dari 4GB RAM
      cores: this.getCPUCores() < 4, // Kurang dari 4 core
      connection: this.isSlowConnection(),
      userAgent: this.isLowEndUserAgent()
    };

    // Jika 2 atau lebih faktor menunjukkan perangkat lambat
    const lowEndFactors = Object.values(factors).filter(Boolean).length;
    this._isLowEndDevice = lowEndFactors >= 2;

    return this._isLowEndDevice;
  }

  private static getDeviceMemory(): number {
    // @ts-ignore
    return navigator.deviceMemory || 4; // Default 4GB jika tidak tersedia
  }

  private static getCPUCores(): number {
    return navigator.hardwareConcurrency || 4; // Default 4 core jika tidak tersedia
  }

  private static isSlowConnection(): boolean {
    // @ts-ignore
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    if (!connection) return false;

    const slowTypes = ['slow-2g', '2g', '3g'];
    return slowTypes.includes(connection.effectiveType) || connection.downlink < 1.5;
  }

  private static isLowEndUserAgent(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    const lowEndIndicators = [
      'android 4', 'android 5', 'android 6',
      'cpu os 9', 'cpu os 10', 'cpu os 11',
      'webview', 'wv'
    ];

    return lowEndIndicators.some(indicator => userAgent.includes(indicator));
  }

  static getOptimizedConfig(): typeof PERFORMANCE_CONFIG {
    const config = { ...PERFORMANCE_CONFIG };

    if (this.isLowEndDevice()) {
      // Adjust settings untuk perangkat spesifikasi rendah
      config.NETWORK.TIMEOUT = 15000; // Timeout lebih lama
      config.NETWORK.BATCH_SIZE = 1; // Batch size lebih kecil
      config.UI.DEBOUNCE_DELAY = 500; // Debounce lebih lama
      config.UI.CLOCK_UPDATE_INTERVAL = 60000; // Update clock setiap menit
      config.UI.SYNC_DELAY = 5000; // Delay sync lebih lama
      config.MEMORY.MAX_CACHE_SIZE = 25; // Cache lebih kecil
      config.MEMORY.IMAGE_QUALITY = 0.6; // Kualitas gambar lebih rendah
      config.MONITORING.FPS_MONITORING = false; // Disable FPS monitoring
    }

    return config;
  }
}

// Export optimized config berdasarkan device capability
export const OPTIMIZED_CONFIG = DeviceCapability.getOptimizedConfig();

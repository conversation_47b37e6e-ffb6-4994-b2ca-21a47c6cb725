System.register(["./ionic-legacy-DbGqp7zN.js","./index-legacy-Cg4ub26U.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(a,s){"use strict";var e,i,l,n,r,o,c,t,d,x,h,m,p,j,f,g,u,b,N,k,w,y,_,v,S,R,I,J,K;return{setters:[a=>{e=a.r,i=a.a2,l=a.j,n=a.I,r=a.J,o=a.K,c=a.L,t=a.h,d=a.a3,x=a.k,h=a.m,m=a.n,p=a.a4,j=a.p,f=a.q,g=a.s,u=a.o,b=a.t,N=a.v,k=a.G,w=a.a5,y=a.Q},a=>{_=a.A,v=a.B,S=a.C,R=a.i,I=a.D,J=a.c,K=a.t},null,null,null],execute:function(){var s=document.createElement("style");s.textContent=".profile-page{--background: linear-gradient(180deg, rgba(56, 128, 255, .12) 0%, rgba(0,0,0,0) 40%)}.profile-hero{position:relative;display:flex;flex-direction:column;align-items:center;padding:32px 16px 16px;background:linear-gradient(135deg,var(--ion-color-primary) 0%,var(--ion-color-secondary) 100%);color:#fff;border-bottom-left-radius:28px;border-bottom-right-radius:28px}.avatar-wrap{width:128px;height:128px;border-radius:50%;padding:4px;background:linear-gradient(135deg,#fff,rgba(255,255,255,.2));box-shadow:0 10px 30px rgba(0,0,0,.25)}.avatar-wrap.skeleton{display:flex;align-items:center;justify-content:center}.profile-avatar{width:120px;height:120px;border:4px solid rgba(255,255,255,.85)}.default-avatar{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:rgba(255,255,255,.2);color:#fff;font-size:48px;border-radius:50%;font-weight:600}.profile-name{margin:12px 0 0;font-size:22px;font-weight:700}.profile-role{margin:4px 0 0;opacity:.9}.chip-wrap{display:flex;gap:8px;margin-top:12px;flex-wrap:wrap;justify-content:center}.profile-chip{background:rgba(255,255,255,.16);color:#fff;border-radius:16px}.profile-card{margin:20px 16px;border-radius:16px}.glass{background:rgba(255,255,255,.85);backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px);box-shadow:0 10px 30px rgba(0,0,0,.08)}.info-list{margin-top:4px}.info-item{--background: transparent;margin:6px 0;border-radius:12px}.info-icon{color:var(--ion-color-primary);font-size:20px}.label{margin:0;font-size:12px;color:var(--ion-color-medium)}.value{margin:4px 0 0;font-size:16px;font-weight:600;color:var(--ion-color-dark)}.action-group{margin-top:12px}.schedule-info{display:flex;flex-direction:column;gap:8px;margin-top:8px}.schedule-badge{display:flex;align-items:center;gap:6px;font-size:12px;font-weight:500;padding:6px 10px;border-radius:8px;width:fit-content}.schedule-badge ion-icon{font-size:14px}@media (max-width: 480px){.schedule-info{gap:6px}.schedule-badge{font-size:11px;padding:5px 8px}}\n",document.head.appendChild(s),a("default",()=>{const[a,s]=e.useState(null),[z,A]=e.useState(!0),P=i(),C=(()=>{try{const a=localStorage.getItem("user"),s=a?JSON.parse(a):{};return Array.isArray(s)?s[0]||{}:s}catch{return{}}})();return e.useEffect(()=>{const a=localStorage.getItem("user");if(a){const e=JSON.parse(a),i=Array.isArray(e)?e[0]:e,l=i?.nik;if(!l)return void A(!1);fetch(`https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(l)}`).then(a=>a.json()).then(a=>{if("success"===a.status){const e=(Array.isArray(a.data)?a.data:[a.data]).find(a=>String(a?.nik)===String(l));if(e){s(e);try{const a={id:e?.id??i?.id,nik:i?.nik,nama:e?.nama||e?.nama_karyawan||e?.name||i?.nama,jabatan:e?.jabatan??i?.jabatan,bidang_id:e?.bidang_id??i?.bidang_id,bidang:e?.bidang||e?.nama_bidang||e?.bidang_nama||i?.bidang,foto_profil:e?.foto_profil||e?.foto||i?.foto_profil,lokasi_id:e?.lokasi_id??i?.lokasi_id,nama_lokasi:e?.nama_lokasi||e?.lokasi||e?.nama_lokasi_kerja||i?.nama_lokasi,allow_flexible_schedule:e?.allow_flexible_schedule??i?.allow_flexible_schedule},s={...i,...a};localStorage.setItem("user",JSON.stringify(s))}catch{}}}A(!1)}).catch(a=>{A(!1)})}},[]),z?l.jsxs(n,{children:[l.jsx(r,{children:l.jsx(o,{children:l.jsx(c,{children:"Profil Karyawan"})})}),l.jsxs(t,{className:"profile-page",fullscreen:!0,children:[l.jsxs("div",{className:"profile-hero",children:[l.jsx("div",{className:"avatar-wrap skeleton",children:l.jsx(d,{animated:!0,style:{width:120,height:120,borderRadius:"50%"}})}),l.jsxs("div",{className:"name-wrap",children:[l.jsx(d,{animated:!0,style:{width:"60%",height:20,borderRadius:8}}),l.jsx(d,{animated:!0,style:{width:"40%",height:16,borderRadius:8,marginTop:8}})]}),l.jsxs("div",{className:"chip-wrap",children:[l.jsx(d,{animated:!0,style:{width:120,height:28,borderRadius:20}}),l.jsx(d,{animated:!0,style:{width:140,height:28,borderRadius:20}})]})]}),l.jsxs(x,{className:"profile-card glass",children:[l.jsxs(h,{children:[l.jsx(m,{children:"Informasi Karyawan"}),l.jsx(p,{children:"Memuat data..."})]}),l.jsxs(j,{children:[l.jsxs(f,{lines:"none",className:"info-list",children:[l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:_,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"NIK"}),l.jsx(d,{animated:!0,style:{width:"40%",height:16,borderRadius:6}})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:v,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Jabatan"}),l.jsx(d,{animated:!0,style:{width:"50%",height:16,borderRadius:6}})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:S,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Bidang"}),l.jsx(d,{animated:!0,style:{width:"35%",height:16,borderRadius:6}})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:R,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Lokasi"}),l.jsx(d,{animated:!0,style:{width:"45%",height:16,borderRadius:6}})]})]})]}),l.jsx("div",{className:"action-group",children:l.jsxs(N,{expand:"block",color:"warning",disabled:!0,children:[l.jsx(u,{icon:I,slot:"start"}),"Ganti Password"]})})]})]})]})]}):l.jsxs(n,{children:[l.jsx(r,{children:l.jsx(o,{children:l.jsx(c,{children:"Profil Karyawan"})})}),l.jsxs(t,{className:"profile-page",fullscreen:!0,children:[(()=>{const s={foto_profil:C?.foto_profil||a?.foto_profil||a?.foto||null,nama:C?.nama||a?.nama||a?.nama_karyawan||C?.name||"-",jabatan:C?.jabatan||a?.jabatan||"-",bidang:C?.bidang||a?.bidang||a?.nama_bidang||"-",nama_lokasi:C?.nama_lokasi||a?.nama_lokasi||a?.lokasi||a?.nama_lokasi_kerja||"-",nik:C?.nik||a?.nik||"-"};return l.jsxs("div",{className:"profile-hero",children:[l.jsx("div",{className:"avatar-wrap",children:l.jsx(k,{className:"profile-avatar",children:s.foto_profil?l.jsx("img",{src:`https://absensiku.trunois.my.id/uploads/${s.foto_profil}`,alt:"Foto Profil"}):l.jsx("div",{className:"default-avatar",children:s.nama.charAt(0).toUpperCase()})})}),l.jsx("h2",{className:"profile-name",children:s.nama}),l.jsx("p",{className:"profile-role",children:s.jabatan}),l.jsxs("div",{className:"chip-wrap",children:[l.jsxs(w,{color:"light",className:"profile-chip",children:[l.jsx(u,{icon:S}),l.jsx("span",{children:s.bidang})]}),l.jsxs(w,{color:"light",className:"profile-chip",children:[l.jsx(u,{icon:R}),l.jsx("span",{children:s.nama_lokasi})]})]})]})})(),l.jsxs(x,{className:"profile-card glass",children:[l.jsxs(h,{children:[l.jsx(m,{children:"Informasi Karyawan"}),l.jsx(p,{children:"Detail singkat"})]}),l.jsxs(j,{children:[l.jsxs(f,{lines:"none",className:"info-list",children:[l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:_,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"NIK"}),l.jsx("h3",{className:"value",children:(C?.nik||a?.nik)??"-"})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:v,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Jabatan"}),l.jsx("h3",{className:"value",children:(C?.jabatan||a?.jabatan)??"-"})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:S,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Bidang"}),l.jsx("h3",{className:"value",children:(C?.bidang||a?.bidang||a?.nama_bidang)??"-"})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:R,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Lokasi"}),l.jsx("h3",{className:"value",children:(C?.nama_lokasi||a?.nama_lokasi||a?.lokasi||a?.nama_lokasi_kerja)??"-"})]})]})]}),l.jsx("div",{className:"action-group",children:l.jsxs(N,{expand:"block",color:"warning",onClick:()=>P.push("/ganti-password","forward"),children:[l.jsx(u,{icon:I,slot:"start"}),"Ganti Password"]})})]})]}),(()=>{const s=C?.allow_flexible_schedule||a?.allow_flexible_schedule;return 1===s||"1"===s?null:l.jsxs(x,{className:"profile-card glass",children:[l.jsxs(h,{children:[l.jsx(m,{children:"Jadwal Kerja"}),l.jsx(p,{children:"Jam kerja karyawan"})]}),l.jsx(j,{children:l.jsxs(f,{lines:"none",className:"info-list",children:[l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:J,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Senin - Kamis"}),l.jsxs("div",{className:"schedule-info",children:[l.jsxs(y,{color:"success",className:"schedule-badge",children:[l.jsx(u,{icon:K,slot:"start"}),"Masuk: 07:02"]}),l.jsxs(y,{color:"danger",className:"schedule-badge",children:[l.jsx(u,{icon:K,slot:"start"}),"Pulang: 15:30"]})]})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:J,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Jumat"}),l.jsxs("div",{className:"schedule-info",children:[l.jsxs(y,{color:"success",className:"schedule-badge",children:[l.jsx(u,{icon:K,slot:"start"}),"Masuk: 06:30"]}),l.jsxs(y,{color:"danger",className:"schedule-badge",children:[l.jsx(u,{icon:K,slot:"start"}),"Pulang: 11:30"]})]})]})]}),l.jsxs(g,{className:"info-item",children:[l.jsx(u,{icon:J,slot:"start",className:"info-icon"}),l.jsxs(b,{children:[l.jsx("p",{className:"label",children:"Sabtu - Minggu"}),l.jsx("div",{className:"schedule-info",children:l.jsxs(y,{color:"medium",className:"schedule-badge",children:[l.jsx(u,{icon:K,slot:"start"}),"Libur"]})})]})]})]})})]})})()]})]})})}}});

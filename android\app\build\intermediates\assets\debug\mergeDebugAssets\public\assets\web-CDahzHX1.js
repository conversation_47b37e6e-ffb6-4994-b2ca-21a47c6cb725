import{W as e}from"./capacitor-DGgumwVn.js";import"./ionic-CJlrxXsE.js";import"./react-vendor-DCX9i6UF.js";class t extends e{async getCurrentPosition(e){return new Promise((t,i)=>{navigator.geolocation.getCurrentPosition(e=>{t(e)},e=>{i(e)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0},e))})}async watchPosition(e,t){const i=navigator.geolocation.watchPosition(e=>{t(e)},e=>{t(null,e)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0,minimumUpdateInterval:5e3},e));return"".concat(i)}async clearWatch(e){navigator.geolocation.clearWatch(parseInt(e.id,10))}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");const e=await navigator.permissions.query({name:"geolocation"});return{location:e.state,coarseLocation:e.state}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}}const i=new t;export{i as Geolocation,t as GeolocationWeb};

const __vite__fileDeps=["assets/jamKerja-odpqnhzg.js","assets/networkOptimizer-CUziElFO.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{r as e,j as a,I as s,J as t,K as i,M as n,P as r,L as l,v as o,o as c,h as d,E as h,F as u,k as j,p as m,Q as x,C as g,N as p,q as f,m as k,n as b,H as S,s as _,t as y,S as v,T as D,U as w,V as T,w as N,d as I}from"./ionic-CJlrxXsE.js";import{n as A,o as F,w as C,c as R,t as W,q as M,g as z,f as J,j as P}from"./index-BZ7jmVXp.js";import"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";import"./capacitor-DGgumwVn.js";const L=()=>{const L=JSON.parse(localStorage.getItem("user")||"{}"),[O,B]=e.useState([]),[K,E]=e.useState(!0),[H,Y]=e.useState(""),[q,V]=e.useState(null),[U,G]=e.useState(!1),[Q,X]=e.useState(!1),Z=new Date,$=(Z.getMonth()+1).toString().padStart(2,"0"),ee=Z.getFullYear().toString(),[ae,se]=e.useState($),[te,ie]=e.useState(ee),[ne,re]=e.useState(""),[le,oe]=e.useState(!1),[ce,de]=e.useState("");e.useEffect(()=>{(async()=>{try{const e=localStorage.getItem("jam_kerja_list"),a=localStorage.getItem("jam_kerja_bidang_list");if(!e||!a){const{fetchAndStoreJamKerja:e}=await I(()=>import("./jamKerja-odpqnhzg.js"),__vite__mapDeps([0,1])),{fetchAndStoreJamKerjaBidang:a}=await I(()=>import("./jamKerjaBidang-UCleSGcG.js"),[]);await e(),await a()}}catch(e){}})()},[]);const he=async()=>{if(!L.id&&!L.nik)return Y("Data user tidak ditemukan"),void E(!1);E(!0),Y("");try{const e=L.id||L.nik,a=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(e)),s=await a.json();if("success"===s.status&&Array.isArray(s.data)){const a=s.data.filter(a=>a.user_id===e||a.user_id===L.id||a.user_id===L.nik);B(a)}else Y("Gagal mengambil data absensi")}catch(e){Y("Terjadi kesalahan koneksi")}finally{E(!1)}},ue=e=>e?"https://absensiku.trunois.my.id/uploads/".concat(e):null,je=e=>new Date(e).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),me=e=>e?e.substring(0,5):"-",xe=e=>{switch(e){case"Tepat Waktu":return"success";case"Terlambat":return"danger";case"Pulang Awal":return"warning";default:return"medium"}},ge=e=>{switch(e){case"Tepat Waktu":return P;case"Terlambat":return J;case"Pulang Awal":return C;default:return W}},pe=(e,a)=>{if(!e)return"Belum Absen";try{const s=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),t=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),i=new Date(a).toLocaleDateString("id-ID",{weekday:"long"}),n=t.find(e=>e.hari===i&&e.jam_kerja_id);if(!n||!n.jam_kerja_id)return"Tepat Waktu";const r=s.find(e=>e.id==n.jam_kerja_id);if(!r)return"Tepat Waktu";const l=new Date("2000-01-01T".concat(e));return l>new Date("2000-01-01T".concat(r.jam_masuk))?"Terlambat":"Tepat Waktu"}catch(s){return"Tepat Waktu"}},fe=e=>{let a=0;const s=[],t=pe(e.jam_masuk,e.tanggal);if(e.jam_masuk&&"Terlambat"===t){const t=e.jam_masuk.substring(0,5);a+=1e4,s.push("Terlambat masuk (".concat(t,"): Rp 10.000"))}return e.jam_masuk&&!e.jam_pulang&&(a+=5e3,s.push("Tidak absen pulang: Rp 5.000")),{totalDenda:a,dendaMessages:s,hasDenda:a>0}},ke=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e),be=(e,a)=>{V({url:e,title:a}),G(!0)},Se=()=>{G(!1),V(null)},_e=()=>{const e=new Date,a=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getFullYear().toString();se(a),ie(s),re(""),X(!1),oe(!0),de("Filter direset ke bulan ini")};e.useEffect(()=>{he()},[]);const ye=(()=>{let e=O;return ae&&te&&(e=e.filter(e=>{const a=new Date(e.tanggal),s=(a.getMonth()+1).toString().padStart(2,"0"),t=a.getFullYear().toString();return s===ae&&t===te})),ne&&(e=e.filter(e=>e.status===ne)),e.sort((e,a)=>new Date(a.tanggal).getTime()-new Date(e.tanggal).getTime())})();return a.jsxs(s,{children:[a.jsx(t,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},children:a.jsxs(i,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[a.jsx(n,{slot:"start",children:a.jsx(r,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),a.jsx(l,{style:{color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center"},children:"Histori Absensi"}),a.jsx(n,{slot:"end",children:a.jsx(o,{onClick:()=>X(!0),style:{color:"#fff"},children:a.jsx(c,{icon:A})})})]})}),a.jsxs(d,{children:[a.jsx(h,{slot:"fixed",onIonRefresh:async e=>{await he(),e.detail.complete()},children:a.jsx(u,{})}),a.jsx("div",{className:"histori-header",children:a.jsx(j,{children:a.jsx(m,{children:a.jsxs("div",{className:"histori-user-info",children:[a.jsx(c,{icon:F,className:"histori-user-icon"}),a.jsxs("div",{children:[a.jsx("h3",{children:L.nama||"Nama User"}),a.jsx("p",{children:L.jabatan||"Jabatan"}),a.jsxs("p",{children:["NIK: ",L.nik||"N/A"]})]})]})})})}),(ae||ne)&&a.jsxs("div",{className:"histori-filter-summary",children:[a.jsxs(x,{color:"primary",children:[ae&&te&&(()=>{const e=parseInt(ae)-1;return"".concat(["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][e]," ").concat(te)})(),ae&&ne&&" | ",ne&&"Status: ".concat(ne)]}),a.jsx(o,{size:"small",fill:"clear",onClick:_e,children:"Reset"})]}),!K&&!H&&(()=>{if("koperasi"===L.keterangan)return null;const e=ye.reduce((e,a)=>e+fe(a).totalDenda,0),s=ye.filter(e=>fe(e).hasDenda).length;return e>0?a.jsx(j,{style:{marginBottom:"16px",border:"2px solid #f44336"},children:a.jsx(m,{children:a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px 0"},children:[a.jsxs("div",{children:[a.jsxs("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:"#d32f2f",marginBottom:"4px",display:"flex",alignItems:"center"},children:[a.jsx(c,{icon:C,style:{marginRight:"8px"}}),"Total Denda Periode Ini"]}),a.jsxs("div",{style:{fontSize:"0.9rem",color:"#666"},children:[s," pelanggaran dari ",ye.length," hari kerja"]})]}),a.jsx("div",{style:{fontSize:"1.3rem",fontWeight:"bold",color:"#d32f2f",textAlign:"right"},children:ke(e)})]})})}):null})(),K&&a.jsxs("div",{className:"histori-loading",children:[a.jsx(g,{name:"crescent"}),a.jsx("p",{children:"Memuat data absensi..."})]}),H&&!K&&a.jsx("div",{className:"histori-error",children:a.jsx(j,{children:a.jsxs(m,{children:[a.jsx(p,{color:"danger",children:a.jsx("p",{children:H})}),a.jsx(o,{expand:"block",onClick:he,children:"Coba Lagi"})]})})}),!K&&!H&&a.jsx("div",{className:"histori-content",children:0===ye.length?a.jsx("div",{className:"histori-empty",children:a.jsx(j,{children:a.jsx(m,{children:a.jsx(p,{color:"medium",children:a.jsx("p",{children:"Tidak ada data absensi ditemukan"})})})})}):a.jsx(f,{children:ye.map(e=>a.jsxs(j,{className:"histori-card",children:[a.jsx(k,{children:a.jsxs(b,{className:"histori-card-title",children:[a.jsxs("div",{className:"histori-date",children:[a.jsx(c,{icon:R}),a.jsx("span",{children:je(e.tanggal)})]}),a.jsxs(x,{color:xe(pe(e.jam_masuk,e.tanggal)),children:[a.jsx(c,{icon:ge(pe(e.jam_masuk,e.tanggal))}),pe(e.jam_masuk,e.tanggal)]})]})}),a.jsxs(m,{children:[a.jsxs("div",{className:"histori-time-info",children:[a.jsxs("div",{className:"histori-time-item",children:[a.jsx(c,{icon:W,color:"primary"}),a.jsxs("div",{children:[a.jsx("strong",{children:"Masuk:"})," ",me(e.jam_masuk),e.foto_masuk&&a.jsxs(o,{size:"small",fill:"clear",onClick:()=>be(ue(e.foto_masuk),"Foto Absen Masuk - ".concat(je(e.tanggal))),children:[a.jsx(c,{icon:M}),"Lihat Foto"]})]})]}),a.jsxs("div",{className:"histori-time-item",children:[a.jsx(c,{icon:W,color:"secondary"}),a.jsxs("div",{children:[a.jsx("strong",{children:"Pulang:"})," ",me(e.jam_pulang),e.foto_pulang&&a.jsxs(o,{size:"small",fill:"clear",onClick:()=>be(ue(e.foto_pulang),"Foto Absen Pulang - ".concat(je(e.tanggal))),children:[a.jsx(c,{icon:M}),"Lihat Foto"]})]})]})]}),e.keterangan&&a.jsxs("div",{className:"histori-keterangan",children:[a.jsx("strong",{children:"Keterangan:"})," ",e.keterangan]}),(()=>{if("koperasi"===L.keterangan)return null;const s=fe(e);return s.hasDenda?a.jsxs("div",{style:{marginTop:"12px",padding:"12px",backgroundColor:"#ffebee",borderRadius:"8px",border:"1px solid #f44336"},children:[a.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px",color:"#d32f2f",fontWeight:"bold"},children:[a.jsx(c,{icon:C,style:{marginRight:"8px"}}),"Denda Keterlambatan"]}),s.dendaMessages.map((e,s)=>a.jsxs("div",{style:{fontSize:"0.9rem",color:"#666",marginBottom:"4px"},children:["• ",e]},s)),a.jsxs("div",{style:{marginTop:"8px",padding:"8px",backgroundColor:"#fff",borderRadius:"4px",textAlign:"center",fontWeight:"bold",color:"#d32f2f",fontSize:"1.1rem"},children:["Total Denda: ",ke(s.totalDenda)]})]}):null})()]})]},e.id))})}),a.jsxs(S,{isOpen:Q,onDidDismiss:()=>X(!1),children:[a.jsx(t,{children:a.jsxs(i,{children:[a.jsx(l,{children:"Filter Histori"}),a.jsx(n,{slot:"end",children:a.jsx(o,{onClick:()=>X(!1),children:a.jsx(c,{icon:z})})})]})}),a.jsx(d,{children:a.jsxs("div",{className:"histori-filter-content",children:[a.jsxs(_,{children:[a.jsx(y,{position:"stacked",children:"Bulan & Tahun"}),a.jsx(v,{value:"".concat(te,"-").concat(ae),onIonChange:e=>{const a=Array.isArray(e.detail.value)?e.detail.value[0]:e.detail.value;if(a){const e=new Date(a),s=(e.getMonth()+1).toString().padStart(2,"0"),t=e.getFullYear().toString();se(s),ie(t)}},presentation:"month-year",locale:"id-ID"})]}),a.jsxs(_,{children:[a.jsx(y,{position:"stacked",children:"Status"}),a.jsxs(D,{value:ne,onIonChange:e=>re(e.detail.value),children:[a.jsx(w,{value:"",children:"Semua Status"}),a.jsx(w,{value:"Tepat Waktu",children:"Tepat Waktu"}),a.jsx(w,{value:"Terlambat",children:"Terlambat"}),a.jsx(w,{value:"Pulang Awal",children:"Pulang Awal"})]})]}),a.jsxs("div",{className:"histori-filter-actions",children:[a.jsx(o,{expand:"block",onClick:()=>{X(!1),oe(!0),de("Filter berhasil diterapkan")},children:"Terapkan Filter"}),a.jsx(o,{expand:"block",fill:"outline",onClick:_e,children:"Reset Filter"})]})]})})]}),a.jsxs(S,{isOpen:U,onDidDismiss:Se,children:[a.jsx(t,{children:a.jsxs(i,{children:[a.jsx(l,{children:null==q?void 0:q.title}),a.jsx(n,{slot:"end",children:a.jsx(o,{onClick:Se,children:a.jsx(c,{icon:z})})})]})}),a.jsx(d,{children:a.jsx("div",{className:"histori-image-modal",children:q&&a.jsx(T,{src:q.url,alt:q.title})})})]}),a.jsx(N,{isOpen:le,onDidDismiss:()=>oe(!1),message:ce,duration:2e3,color:"success"})]})]})};export{L as default};

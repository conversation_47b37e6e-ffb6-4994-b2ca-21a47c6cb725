/// <reference types="vitest" />

import legacy from '@vitejs/plugin-legacy'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Optimasi React untuk production
      babel: {
        plugins: [
          // Remove console.log in production
          process.env.NODE_ENV === 'production' && ['transform-remove-console', { exclude: ['error', 'warn'] }]
        ].filter(Boolean)
      }
    }),
    legacy({
      // Target browser yang lebih spesifik untuk mengurangi polyfill
      targets: ['defaults', 'not IE 11']
    })
  ],
  build: {
    // Optimasi build untuk performa
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      }
    },
    rollupOptions: {
      output: {
        // Code splitting untuk vendor libraries
        manualChunks: {
          // Ionic framework dalam chunk terpisah
          'ionic': ['@ionic/react', '@ionic/react-router'],
          // React dalam chunk terpisah
          'react-vendor': ['react', 'react-dom', 'react-router', 'react-router-dom'],
          // Maps libraries dalam chunk terpisah (lazy loaded)
          'maps': ['@react-google-maps/api', 'leaflet', 'react-leaflet', 'maplibre-gl', 'react-map-gl'],
          // Capacitor plugins dalam chunk terpisah
          'capacitor': ['@capacitor/core', '@capacitor/camera', '@capacitor/geolocation', '@capacitor/app'],
          // Barcode dan utility libraries
          'utils': ['@capacitor-community/barcode-scanner', '@zxing/library', 'jsqr', '@ionic/storage']
        }
      }
    },
    // Chunk size warning threshold
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    // Pre-bundle dependencies untuk development yang lebih cepat
    include: [
      '@ionic/react',
      '@ionic/react-router',
      'react',
      'react-dom',
      'react-router',
      'react-router-dom'
    ],
    // Exclude heavy libraries dari pre-bundling
    exclude: [
      '@react-google-maps/api',
      'leaflet',
      'maplibre-gl'
    ]
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/setupTests.ts',
  }
})

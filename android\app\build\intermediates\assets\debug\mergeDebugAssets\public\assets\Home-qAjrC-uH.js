const __vite__fileDeps=["assets/jamKerja-odpqnhzg.js","assets/networkOptimizer-CUziElFO.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{r as e,D as a,d as s,j as t,I as n,h as i,E as r,F as l,G as o,v as c,o as d,C as m,H as u,J as p,K as g,L as h,M as j,N as x,O as f}from"./ionic-CJlrxXsE.js";import{l as b,c as _,d as y,p as k,a as w,b as v,t as S,e as N,f as T,g as I,h as A}from"./index-BZ7jmVXp.js";import{f as D,b as O}from"./networkOptimizer-CUziElFO.js";import{fetchAndStoreJamKerja as C}from"./jamKerja-odpqnhzg.js";import{fetchAndStoreJamKerjaBidang as L}from"./jamKerjaBidang-UCleSGcG.js";import{fetchAndStoreBidang as E}from"./bidang-BAynerGC.js";import{fetchAndStoreLokasi as M}from"./lokasi-24PHrENq.js";import{C as J}from"./capacitor-DGgumwVn.js";import{u as K}from"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";async function z(){try{const e=await D("https://absensiku.trunois.my.id/api/hari_libur.php?api_key=absensiku_api_key_2023",{},864e5);"success"===e.status&&Array.isArray(e.data)&&localStorage.setItem("hari_libur_list",JSON.stringify(e.data))}catch(e){}}function B(){const e=JSON.parse(localStorage.getItem("hari_libur_list")||"[]"),a=(new Date).toISOString().slice(0,10),s=e.find(e=>e.tanggal===a);return s?{libur:!0,nama:s.nama_libur}:{libur:!1}}const H=Object.freeze(Object.defineProperty({__proto__:null,fetchAndStoreHariLibur:z,isTodayLibur:B},Symbol.toStringTag,{value:"Module"}));const F=()=>{var D;const F=e.useMemo(()=>{try{const e=localStorage.getItem("user"),a=e?JSON.parse(e):{};return Array.isArray(a)?a[0]||{}:a}catch(e){return{}}},[]),[P,R]=e.useState({loading:!1,downloading:!1,isModalOpen:!1,showDownloadAlert:!1,showStatusAlert:!1}),[W,q]=e.useState({h:"--",m:"--"}),[V,G]=e.useState(null),[U,Q]=e.useState(null),[X,Y]=e.useState({libur:!1}),[Z,$]=e.useState(null),[ee]=a(),ae=e.useRef(null),se=K(),te=e.useMemo(()=>(new Date).toISOString().split("T")[0],[]),ne=e.useMemo(()=>F.id||F.nik,[F.id,F.nik]),ie=e.useCallback(()=>{try{const e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]").filter(e=>new Date(e.timestamp).toISOString().split("T")[0]===te&&e.user_id===ne),a=JSON.parse(localStorage.getItem("absensi_backup")||"[]").filter(e=>new Date(e.timestamp).toISOString().split("T")[0]===te&&e.user_id===ne),s=[...e,...a];if(s.length>0){const e={id:"offline_"+te,user_id:ne,tanggal:te,jam_masuk:null,foto_masuk:null,lokasi_masuk:null,jam_pulang:null,foto_pulang:null,lokasi_pulang:null,status:"Hadir",keterangan:"Data offline"};return s.forEach(a=>{("masuk"===a.jenisAbsensi||a.jam_masuk)&&(e.jam_masuk=a.jam_masuk||a.timestamp.split("T")[1].substring(0,8),e.foto_masuk=a.foto_masuk_base64||null,e.lokasi_masuk=a.lokasi_masuk||null,e.status=a.status||"Hadir"),("pulang"===a.jenisAbsensi||a.jam_pulang)&&(e.jam_pulang=a.jam_pulang||a.timestamp.split("T")[1].substring(0,8),e.foto_pulang=a.foto_pulang_base64||null,e.lokasi_pulang=a.lokasi_pulang||null)}),e}return null}catch(e){return null}},[te,ne]),re=e.useCallback(async()=>{if(ne){R(e=>({...e,loading:!0}));try{let a=null;try{const e=new AbortController,s=setTimeout(()=>e.abort(),5e3),t=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(ne,"&tanggal=").concat(te),{signal:e.signal});clearTimeout(s);const n=await t.json();"success"===n.status&&n.data.length>0&&(a=n.data[0])}catch(e){}const s=ie();G(a||(s||null))}catch(e){const a=ie();G(a)}finally{R(e=>({...e,loading:!1}))}}},[ne,te,ie]);e.useEffect(()=>{const e=()=>{const e=new Date,a={h:e.getHours().toString().padStart(2,"0"),m:e.getMinutes().toString().padStart(2,"0")};q(e=>e.h!==a.h||e.m!==a.m?a:e)};e();const a=setInterval(e,3e4);return()=>clearInterval(a)},[]);const le=e.useCallback(async()=>{const e=localStorage.getItem("sync_in_progress");if(e){const a=parseInt(e);if(!(Date.now()-a>3e5))return;localStorage.removeItem("sync_in_progress")}try{localStorage.setItem("sync_in_progress",Date.now().toString());let e=JSON.parse(localStorage.getItem("offline_absensi_queue")||"[]");if(0===e.length)return void localStorage.removeItem("sync_in_progress");let s=0;for(let t=e.length-1;t>=0;t--){const n=e[t];if(n.synced)e.splice(t,1);else try{let a;const i={...n};if(delete i.id,delete i.timestamp,delete i.synced,delete i.jenisAbsensi,"masuk"===n.jenisAbsensi)a=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});else{const e=n.tanggal,s=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=".concat(n.user_id,"&tanggal=").concat(e)),t=await s.json();if(!("success"===t.status&&t.data.length>0))continue;i.id=t.data[0].id,a=await fetch("https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})}if("success"===(await a.json()).status){e.splice(t,1),s++;const a={...n,synced:!0,syncedAt:(new Date).toISOString()},i=JSON.parse(localStorage.getItem("absensi_backup")||"[]");i.push(a),i.length>30&&i.splice(0,i.length-30),localStorage.setItem("absensi_backup",JSON.stringify(i))}}catch(a){}}localStorage.setItem("offline_absensi_queue",JSON.stringify(e)),s>0&&(ee({message:"".concat(s," data offline berhasil disinkronkan"),color:"success",duration:3e3,position:"top"}),re())}catch(a){}finally{localStorage.removeItem("sync_in_progress")}},[]),oe=e.useCallback(()=>{ae.current&&clearTimeout(ae.current),ae.current=setTimeout(()=>{le()},3e3)},[le]),ce=e.useCallback(()=>{const e=localStorage.getItem("sync_in_progress");if(e){Date.now()-parseInt(e)>3e4&&localStorage.removeItem("sync_in_progress")}},[]),de=e.useCallback(async e=>{try{await re(),navigator.onLine&&await le();const{fetchAndStoreHariLibur:e,isTodayLibur:a}=await s(()=>Promise.resolve().then(()=>H),void 0);await e(),Y(a()),ee({message:"Data berhasil diperbarui",color:"success",duration:2e3,position:"top"})}catch(a){ee({message:"Gagal memperbarui data",color:"danger",duration:2e3,position:"top"})}finally{e.detail.complete()}},[re,le,ee]);e.useEffect(()=>{re(),ce(),navigator.onLine&&setTimeout(()=>{oe()},1e3)},[re,ce,oe]);const me=e.useCallback(()=>{document.hidden||(re(),navigator.onLine&&oe())},[re,oe]);e.useEffect(()=>(document.addEventListener("visibilitychange",me),()=>{document.removeEventListener("visibilitychange",me)}),[me]),e.useEffect(()=>{const e=()=>{oe()},a=()=>{};return window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a),ae.current&&clearTimeout(ae.current)}},[]),e.useEffect(()=>{(async()=>{try{await z(),Y(B());const e=localStorage.getItem("jam_kerja_list"),a=localStorage.getItem("jam_kerja_bidang_list");if(!e||!a){const{fetchAndStoreJamKerja:e}=await s(()=>import("./jamKerja-odpqnhzg.js"),__vite__mapDeps([0,1])),{fetchAndStoreJamKerjaBidang:a}=await s(()=>import("./jamKerjaBidang-UCleSGcG.js"),[]);await e(),await a()}}catch(e){}})()},[]),e.useEffect(()=>{Y(B())},[P.loading]),e.useEffect(()=>{(async()=>{try{await J.requestPermissions()}catch(e){}})()},[]);const ue=e.useCallback(async()=>{R(e=>({...e,downloading:!0}));const e=await async function(){const e={hariLibur:!1,jamKerja:!1,jamKerjaBidang:!1,bidang:!1,lokasi:!1},a=[async()=>{try{return await z(),e.hariLibur=!!localStorage.getItem("hari_libur_list"),!0}catch(a){return e.hariLibur=!1,!1}},async()=>{try{return await C(),e.jamKerja=!!localStorage.getItem("jam_kerja_list"),!0}catch(a){return e.jamKerja=!1,!1}},async()=>{try{return await L(),e.jamKerjaBidang=!!localStorage.getItem("jam_kerja_bidang_list"),!0}catch(a){return e.jamKerjaBidang=!1,!1}},async()=>{try{return await E(),e.bidang=!!localStorage.getItem("bidang_list"),!0}catch(a){return e.bidang=!1,!1}},async()=>{try{return await M(),e.lokasi=!!localStorage.getItem("lokasi_list"),!0}catch(a){return e.lokasi=!1,!1}}];return await O(a,2),e}();R(e=>({...e,downloading:!1,showStatusAlert:!0})),$(e)},[]),pe=e=>e?"https://absensiku.trunois.my.id/uploads/".concat(e):null,ge=e=>e?e.substring(0,5):"- : -",he=(e,a)=>{if(!e)return"Belum Absen";try{const a=JSON.parse(localStorage.getItem("jam_kerja_list")||"[]"),s=JSON.parse(localStorage.getItem("jam_kerja_bidang_list")||"[]"),t=(new Date).toLocaleDateString("id-ID",{weekday:"long"}),n=s.find(e=>e.hari===t&&e.jam_kerja_id);if(!n||!n.jam_kerja_id)return"Tepat Waktu";const i=a.find(e=>e.id==n.jam_kerja_id);if(!i)return"Tepat Waktu";const r=new Date("2000-01-01T".concat(e));return r>new Date("2000-01-01T".concat(i.jam_masuk))?"Terlambat":"Tepat Waktu"}catch(s){return"Tepat Waktu"}},je=e.useCallback((e,a)=>{Q({url:e,title:a}),R(e=>({...e,isModalOpen:!0}))},[]),xe=e.useCallback(()=>{R(e=>({...e,isModalOpen:!1})),Q(null)},[]);return t.jsxs(n,{children:[t.jsxs(i,{fullscreen:!0,className:"home2-bg",children:[t.jsx(r,{slot:"fixed",onIonRefresh:de,children:t.jsx(l,{pullingIcon:"chevron-down-circle-outline",pullingText:"Tarik untuk memperbarui...",refreshingSpinner:"circles",refreshingText:"Memperbarui data..."})}),t.jsx("div",{className:"home2-header",children:t.jsxs("div",{className:"home2-header-content",style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[t.jsx("div",{style:{flex:1,display:"flex",justifyContent:"flex-start"}}),t.jsxs("div",{style:{flex:2,textAlign:"center"},children:[t.jsx("div",{className:"home-header-avatar",children:t.jsx(o,{children:F.foto_profil?t.jsx("img",{src:"https://absensiku.trunois.my.id/uploads/".concat(F.foto_profil),alt:"Avatar"}):t.jsx("div",{className:"home-avatar-initial",children:(F.nama||"?").charAt(0).toUpperCase()})})}),t.jsx("h1",{className:"home2-nama",style:{margin:0},children:F.nama||"Nama user"}),t.jsx("p",{className:"home2-jabatan",style:{margin:0},children:F.jabatan||"Jabatan"})]}),t.jsx("div",{style:{flex:1,display:"flex",justifyContent:"flex-end"},children:t.jsx(c,{onClick:()=>{localStorage.removeItem("user"),window.location.replace("/login")},color:"medium",size:"small",fill:"clear",style:{minWidth:0,padding:0,marginRight:12},children:t.jsx(d,{icon:b,slot:"icon-only",style:{fontSize:30,color:"#fff"}})})})]})}),t.jsxs("div",{className:"home2-main",children:[t.jsxs("div",{className:"home2-clock-row",children:[t.jsx("div",{className:"home2-clock-box",children:W.h}),t.jsx("div",{className:"home2-clock-sep",children:":"}),t.jsx("div",{className:"home2-clock-box",children:W.m})]}),X.libur&&t.jsxs("div",{className:"holiday-banner",children:[t.jsx(d,{icon:_,style:{marginRight:8}}),"Hari ini libur: ",X.nama]}),t.jsxs("div",{className:"home2-menu-row",children:[t.jsxs("div",{className:"home2-menu-btn menu-histori",onClick:()=>se.push("/histori"),children:[t.jsx("div",{className:"menu-icon-wrap",children:t.jsx(d,{icon:y})}),t.jsx("span",{className:"menu-label",children:"Histori"})]}),t.jsxs("div",{className:"home2-menu-btn menu-izin",onClick:()=>se.push("/histori-izin-dinas"),children:[t.jsx("div",{className:"menu-icon-wrap",children:t.jsx(d,{icon:_})}),t.jsx("span",{className:"menu-label",children:"Izin"})]}),t.jsxs("div",{className:"home2-menu-btn menu-rapat",onClick:()=>se.push("/rapat"),children:[t.jsx("div",{className:"menu-icon-wrap",children:t.jsx(d,{icon:k})}),t.jsx("span",{className:"menu-label",children:"Rapat/Apel"})]})]}),t.jsxs("div",{className:"home2-menu-row",style:{marginTop:"12px"},children:[t.jsxs("div",{className:"home2-menu-btn menu-laporan",onClick:()=>se.push("/laporan-harian"),children:[t.jsx("div",{className:"menu-icon-wrap",children:t.jsx(d,{icon:w})}),t.jsx("span",{className:"menu-label",children:"Lap. Harian"})]}),t.jsxs("div",{className:"home2-menu-btn menu-profil",onClick:()=>se.push("/profile"),children:[t.jsx("div",{className:"menu-icon-wrap",children:t.jsx(d,{icon:v})}),t.jsx("span",{className:"menu-label",children:"Profil"})]}),t.jsxs("div",{className:"home2-menu-btn menu-lembur",onClick:()=>se.push("/lembur"),children:[t.jsx("div",{className:"menu-icon-wrap",children:t.jsx(d,{icon:S})}),t.jsx("span",{className:"menu-label",children:"Lembur"})]})]}),t.jsx("div",{className:"download-horizontal-wrap",children:t.jsxs("button",{className:"download-horizontal-btn".concat(P.downloading?" disabled":""),onClick:P.downloading?void 0:ue,children:[t.jsx("span",{className:"download-icon",children:t.jsx(d,{icon:N})}),t.jsx("span",{className:"download-text",children:P.downloading?"Memproses...":"Download Data"}),P.downloading&&t.jsx(m,{name:"crescent",className:"download-spinner"})]})}),t.jsxs("div",{className:"home2-status-wrap",children:[t.jsx("div",{className:"home2-status-title",children:"Status Absen Hari Ini"}),P.loading?t.jsxs("div",{style:{textAlign:"center",padding:"20px"},children:[t.jsx(m,{name:"crescent"}),t.jsx("p",{style:{margin:"10px 0 0 0",color:"#666"},children:"Memuat data..."})]}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"home2-status-row",children:[t.jsxs("div",{className:"home2-status-box",style:{display:"flex",flexDirection:"column",alignItems:"center",padding:"12px",minHeight:"180px"},children:[t.jsx("span",{className:"home2-status-label",children:"Masuk"}),t.jsx("span",{className:"home2-status-time",children:ge(null==V?void 0:V.jam_masuk)}),(null==V?void 0:V.jam_masuk)&&t.jsxs("div",{style:{fontSize:"0.7rem",color:(e=>{switch(e){case"Tepat Waktu":return"#4caf50";case"Terlambat":return"#f44336";case"Pulang Awal":return"#ff9800";default:return"#9e9e9e"}})(he(V.jam_masuk,V.jam_pulang)),fontWeight:"bold",marginTop:"2px",marginBottom:"8px"},children:[he(V.jam_masuk,V.jam_pulang),(null==(D=V.id)?void 0:D.startsWith("offline_"))&&t.jsx("span",{style:{fontSize:"0.6rem",color:"#ff9800",marginLeft:"4px",fontWeight:"normal"},children:"(Offline)"})]}),(null==V?void 0:V.foto_masuk)?t.jsx("div",{style:{marginTop:"8px"},children:pe(V.foto_masuk)&&t.jsx("img",{src:pe(V.foto_masuk),alt:"Foto Absen Masuk",style:{width:"80px",height:"80px",objectFit:"cover",borderRadius:"8px",border:"2px solid #ddd",cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease"},onClick:()=>je(pe(V.foto_masuk),"Foto Absen Masuk - ".concat(ge(V.jam_masuk)," (").concat(he(V.jam_masuk,V.jam_pulang),")")),onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.05)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none"},onError:e=>{e.target.style.display="none"}})}):(null==V?void 0:V.jam_masuk)&&t.jsx("div",{style:{marginTop:"8px",fontSize:"0.7rem",color:"#999",textAlign:"center"},children:"📸 Foto tersedia"})]}),t.jsxs("div",{className:"home2-status-box",style:{display:"flex",flexDirection:"column",alignItems:"center",padding:"12px",minHeight:"180px"},children:[t.jsx("span",{className:"home2-status-label",children:"Pulang"}),t.jsx("span",{className:"home2-status-time",children:ge(null==V?void 0:V.jam_pulang)}),(null==V?void 0:V.jam_pulang)&&t.jsx("div",{style:{fontSize:"0.7rem",color:"#4caf50",fontWeight:"bold",marginTop:"2px",marginBottom:"8px"},children:"Selesai"}),(null==V?void 0:V.foto_pulang)?t.jsx("div",{style:{marginTop:"8px"},children:pe(V.foto_pulang)&&t.jsx("img",{src:pe(V.foto_pulang),alt:"Foto Absen Pulang",style:{width:"80px",height:"80px",objectFit:"cover",borderRadius:"8px",border:"2px solid #ddd",cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease"},onClick:()=>je(pe(V.foto_pulang),"Foto Absen Pulang - ".concat(ge(V.jam_pulang))),onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.05)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none"},onError:e=>{e.target.style.display="none"}})}):(null==V?void 0:V.jam_pulang)&&t.jsx("div",{style:{marginTop:"8px",fontSize:"0.7rem",color:"#999",textAlign:"center"},children:"📸 Foto tersedia"})]})]}),!V&&t.jsxs("div",{style:{textAlign:"center",padding:"20px",color:"#666"},children:[t.jsx(d,{icon:T,style:{fontSize:"2rem",color:"#ff9800",marginBottom:"8px"}}),t.jsx("p",{style:{margin:"0",fontSize:"0.9rem"},children:"Belum ada absensi hari ini"})]})]})]})]})]}),t.jsxs(u,{isOpen:P.isModalOpen,onDidDismiss:xe,children:[t.jsx(p,{children:t.jsxs(g,{children:[t.jsx(h,{children:(null==U?void 0:U.title)||"Foto Absensi"}),t.jsx(j,{slot:"end",children:t.jsx(c,{onClick:xe,children:t.jsx(d,{icon:I})})})]})}),t.jsx(i,{className:"ion-padding",children:t.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"},children:U&&t.jsxs(t.Fragment,{children:[t.jsx("img",{src:U.url,alt:U.title,style:{maxWidth:"100%",maxHeight:"70vh",objectFit:"contain",borderRadius:"12px",boxShadow:"0 4px 20px rgba(0,0,0,0.1)"},onError:e=>{e.target.style.display="none"}}),t.jsx("div",{style:{marginTop:"16px",textAlign:"center",padding:"12px",backgroundColor:"#f8f9fa",borderRadius:"8px",maxWidth:"300px"},children:t.jsx(x,{color:"medium",children:t.jsx("p",{style:{margin:"0",fontSize:"0.9rem"},children:U.title})})})]})})})]}),t.jsx(f,{isOpen:P.showDownloadAlert,onDidDismiss:()=>R(e=>({...e,showDownloadAlert:!1})),header:"Download Data",message:"Data berhasil di-download dan disimpan di perangkat.",buttons:["OK"]}),t.jsx(f,{isOpen:P.showStatusAlert,onDidDismiss:()=>R(e=>({...e,showStatusAlert:!1})),header:"Status Data Anda",message:"\n          Hari Libur: ".concat((null==Z?void 0:Z.hariLibur)?"✅":"❌","\n          Jam Kerja: ").concat((null==Z?void 0:Z.jamKerja)?"✅":"❌","\n          Jam Kerja Bidang: ").concat((null==Z?void 0:Z.jamKerjaBidang)?"✅":"❌","\n          Bidang: ").concat((null==Z?void 0:Z.bidang)?"✅":"❌","\n          Lokasi: ").concat((null==Z?void 0:Z.lokasi)?"✅":"❌","\n        "),buttons:["OK"]}),t.jsx("div",{className:"floating-camera-container",children:t.jsx("button",{className:"floating-camera-btn",onClick:()=>se.push("/absensi"),disabled:X.libur,style:X.libur?{opacity:.5,cursor:"not-allowed"}:{},children:t.jsx(d,{icon:A,className:"floating-camera-icon"})})})]})};export{F as default};

import{r as e,j as a,I as s,J as i,K as n,M as t,P as r,L as d,h as o,k as l,p as c,o as p,C as g,v as x,Q as u,X as m,Y as h,w as j,O as f}from"./ionic-CJlrxXsE.js";import{c as y,w as b,d as v,u as k,s as S,f as z,t as D,j as w}from"./index-BZ7jmVXp.js";import{u as _}from"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";import"./capacitor-DGgumwVn.js";const B=()=>{const B=JSON.parse(localStorage.getItem("user")||"{}"),C=_(),[I,A]=e.useState([]),[T,K]=e.useState(!0),[L,M]=e.useState(""),[R,H]=e.useState(!1),[O,W]=e.useState(""),[P,F]=e.useState("success"),[Y,E]=e.useState(!1),[J,N]=e.useState(0),[G,Q]=e.useState({pending:0,approved:0,rejected:0}),X=async()=>{if(!B.id&&!B.nik)return M("Data user tidak ditemukan"),void K(!1);K(!0),M("");try{const e=B.id||B.nik,a=await fetch("https://absensiku.trunois.my.id/api/izin_dinas.php?api_key=absensiku_api_key_2023&user_id=".concat(e)),s=await a.json();"success"===s.status?(A(s.data||[]),q(s.data||[])):M(s.message||"Gagal memuat data izin dinas")}catch(e){M("Terjadi kesalahan saat memuat data")}finally{K(!1)}},q=e=>{const a=new Date,s=a.getMonth(),i=a.getFullYear(),n=e.filter(e=>{const a=new Date(e.created_at);return a.getMonth()===s&&a.getFullYear()===i}),t={pending:0,approved:0,rejected:0};n.forEach(e=>{const a=e.status.toLowerCase();"pending"===a||"menunggu"===a?t.pending++:"approved"===a||"disetujui"===a?t.approved++:"rejected"!==a&&"ditolak"!==a||t.rejected++}),Q(t);const r=t.pending+t.approved;N(r)},U=e=>{switch(e.toLowerCase()){case"approved":case"disetujui":return w;case"pending":case"menunggu":return D;case"rejected":case"ditolak":return z;default:return S}},V=e=>new Date(e).toLocaleDateString("id-ID",{day:"2-digit",month:"short",year:"numeric"}),Z=(e,a)=>{const s=new Date(e),i=new Date(a),n=Math.abs(i.getTime()-s.getTime());return Math.ceil(n/864e5)+1},$=()=>{J>=4?E(!0):C.push("/izin-dinas")};return e.useEffect(()=>{X()},[]),a.jsxs(s,{children:[a.jsx(i,{style:{background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},children:a.jsxs(n,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[a.jsx(t,{slot:"start",children:a.jsx(r,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),a.jsx(d,{style:{color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center"},children:"Histori Izin Dinas"})]})}),a.jsxs(o,{className:"ion-padding",children:[a.jsx(l,{style:{marginBottom:"16px",border:J>=4?"2px solid #f44336":"2px solid #4caf50"},children:a.jsxs(c,{children:[a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"8px 0"},children:[a.jsxs("div",{children:[a.jsxs("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:"#333",marginBottom:"4px",display:"flex",alignItems:"center"},children:[a.jsx(p,{icon:y,style:{marginRight:"8px"}}),"Kuota Izin Dinas Bulan Ini"]}),a.jsx("div",{style:{fontSize:"0.9rem",color:"#666"},children:"Maksimal 4 kali per bulan (pending + approved)"})]}),a.jsxs("div",{style:{fontSize:"1.5rem",fontWeight:"bold",color:J>=4?"#f44336":"#4caf50",textAlign:"right"},children:[J,"/4"]})]}),(G.pending>0||G.approved>0||G.rejected>0)&&a.jsxs("div",{style:{marginTop:"12px",padding:"8px",backgroundColor:"#f8f9fa",borderRadius:"4px",fontSize:"0.8rem",color:"#666"},children:[a.jsx("div",{style:{marginBottom:"4px",fontWeight:"bold"},children:"Detail bulan ini:"}),a.jsxs("div",{style:{display:"flex",gap:"12px",flexWrap:"wrap"},children:[G.pending>0&&a.jsxs("span",{style:{color:"#ff9800"},children:["Pending: ",G.pending]}),G.approved>0&&a.jsxs("span",{style:{color:"#4caf50"},children:["Disetujui: ",G.approved]}),G.rejected>0&&a.jsxs("span",{style:{color:"#f44336"},children:["Ditolak: ",G.rejected," (tidak dihitung)"]})]})]}),J>=4&&a.jsx("div",{style:{marginTop:"12px",padding:"8px",backgroundColor:"#ffebee",borderRadius:"4px",fontSize:"0.85rem",color:"#d32f2f"},children:"⚠️ Kuota izin dinas bulan ini sudah habis. Anda dapat mengajukan izin dinas lagi bulan depan."})]})}),T&&a.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[a.jsx(g,{name:"crescent"}),a.jsx("p",{children:"Memuat data histori izin dinas..."})]}),L&&!T&&a.jsx(l,{children:a.jsx(c,{children:a.jsxs("div",{style:{textAlign:"center",padding:"20px",color:"#f44336"},children:[a.jsx(p,{icon:b,style:{fontSize:"3rem",marginBottom:"16px"}}),a.jsx("p",{children:L}),a.jsx(x,{onClick:X,size:"small",children:"Coba Lagi"})]})})}),!T&&!L&&0===I.length&&a.jsx(l,{children:a.jsx(c,{children:a.jsxs("div",{style:{textAlign:"center",padding:"50px",color:"#666"},children:[a.jsx(p,{icon:v,style:{fontSize:"3rem",marginBottom:"16px"}}),a.jsx("p",{children:"Belum ada histori izin dinas"}),a.jsx(x,{onClick:$,size:"small",disabled:J>=4,children:"Ajukan Izin Dinas Pertama"})]})})}),!T&&!L&&I.length>0&&a.jsx(a.Fragment,{children:I.map((e,s)=>a.jsx(l,{style:{marginBottom:"16px"},children:a.jsxs(c,{children:[a.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[a.jsxs("div",{style:{fontSize:"0.9rem",color:"#666",display:"flex",alignItems:"center"},children:[a.jsx(p,{icon:y,style:{marginRight:"4px"}}),V(e.created_at)]}),a.jsxs(u,{color:"approved"===e.status||"disetujui"===e.status?"success":"pending"===e.status||"menunggu"===e.status?"warning":"danger",style:{display:"flex",alignItems:"center"},children:[a.jsx(p,{icon:U(e.status),style:{marginRight:"4px",fontSize:"0.8rem"}}),e.status]})]}),a.jsxs("div",{style:{marginBottom:"8px"},children:[a.jsx("strong",{children:"Periode:"})," ",V(e.tanggal_mulai)," - ",V(e.tanggal_selesai),a.jsxs("span",{style:{marginLeft:"8px",fontSize:"0.8rem",color:"#666",backgroundColor:"#f5f5f5",padding:"2px 6px",borderRadius:"4px"},children:[Z(e.tanggal_mulai,e.tanggal_selesai)," hari"]})]}),a.jsxs("div",{style:{marginBottom:"8px"},children:[a.jsx("strong",{children:"Tujuan:"})," ",e.tujuan]}),a.jsxs("div",{style:{marginBottom:"12px"},children:[a.jsx("strong",{children:"Keterangan:"})," ",e.keterangan]}),e.approved_by&&e.approved_at&&a.jsxs("div",{style:{fontSize:"0.8rem",color:"#666",borderTop:"1px solid #eee",paddingTop:"8px"},children:["Direspon oleh: ",e.approved_by," pada ",V(e.approved_at)]})]})},e.id))}),a.jsx(m,{vertical:"bottom",horizontal:"end",slot:"fixed",children:a.jsx(h,{onClick:$,disabled:J>=4,color:J>=4?"medium":"primary",children:a.jsx(p,{icon:k})})}),a.jsx(j,{isOpen:R,onDidDismiss:()=>H(!1),message:O,duration:3e3,color:P}),a.jsx(f,{isOpen:Y,onDidDismiss:()=>E(!1),header:"Kuota Habis",message:"Anda sudah mengajukan 4 izin dinas bulan ini. Kuota maksimal adalah 4 kali per bulan. Silakan coba lagi bulan depan.",buttons:["OK"]})]})]})};export{B as default};

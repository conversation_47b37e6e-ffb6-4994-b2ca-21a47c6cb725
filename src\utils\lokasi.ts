import { fetchWithCache } from './networkOptimizer';

export async function fetchAndStoreLokasi() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const lokasiId = user.lokasi_id;
    if (!lokasiId) return;

    // Gunakan cache untuk mengurangi request berulang
    const data = await fetchWithCache(
      'https://absensiku.trunois.my.id/api/lokasi.php?api_key=absensiku_api_key_2023',
      {},
      30 * 60 * 1000 // Cache 30 menit karena data lokasi jarang berubah
    );

    if (data.status === 'success' && Array.isArray(data.data)) {
      const lokasi = data.data.filter((l: any) => l.id == lokasiId);
      localStorage.setItem('lokasi_list', JSON.stringify(lokasi));
    }
  } catch (err) {
    console.error('Error fetching lokasi:', err);
    // Fallback ke data yang sudah ada di localStorage jika ada
  }
}
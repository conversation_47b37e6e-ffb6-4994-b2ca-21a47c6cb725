[{"pkg": "@capacitor-community/barcode-scanner", "classpath": "com.getcapacitor.community.barcodescanner.BarcodeScanner"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/geolocation", "classpath": "com.capacitorjs.plugins.geolocation.GeolocationPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}]
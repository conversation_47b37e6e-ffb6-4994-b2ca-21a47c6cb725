import{r as e,D as a,j as n,E as i,F as t,Z as r,s as o,t as s,T as l,U as d,C as c,N as p,v as g,o as u,k as m,m as h,n as x,p as j,H as f,J as k,K as y,L as b,M as v,h as w,u as S,$ as C,W as T,x as D,I,P as F,a0 as L,a1 as _}from"./ionic-CJlrxXsE.js";import{r as z,q as E,x as A,g as H,h as O,y as R,z as B,j as M}from"./index-BZ7jmVXp.js";import"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";import"./capacitor-DGgumwVn.js";const P=()=>{const[S,C]=e.useState([]),[T,D]=e.useState([]),[I,F]=e.useState(!0),[L,_]=e.useState(""),[O,R]=e.useState(""),[B,M]=e.useState(null),[P,K]=e.useState(!1),[N]=a(),U=JSON.parse(localStorage.getItem("user")||"{}"),G=async()=>{try{F(!0);const e=await fetch("https://absensiku.trunois.my.id/api/laporan_harian.php?api_key=absensiku_api_key_2023&nama_karyawan=".concat(encodeURIComponent(U.nama||""))),a=await e.json();"success"===a.status?(C(a.data),D(a.data)):N({message:"Gagal memuat data laporan",color:"danger",duration:3e3,position:"top"})}catch(e){N({message:"Terjadi kesalahan saat memuat data",color:"danger",duration:3e3,position:"top"})}finally{F(!1)}},J=e=>new Date(e).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"});return e.useEffect(()=>{G()},[]),e.useEffect(()=>{(()=>{let e=S;L&&(e=e.filter(e=>e.keterangan.toLowerCase().includes(L.toLowerCase())||e.periode.toLowerCase().includes(L.toLowerCase()))),O&&(e=e.filter(e=>e.periode===O)),D(e)})()},[L,O,S]),n.jsxs(n.Fragment,{children:[n.jsx(i,{slot:"fixed",onIonRefresh:async e=>{await G(),e.detail.complete()},children:n.jsx(t,{})}),n.jsxs("div",{style:{marginBottom:"16px"},children:[n.jsx(r,{value:L,onIonInput:e=>_(e.detail.value),placeholder:"Cari laporan berdasarkan keterangan atau bulan...",style:{"--background":"#f8f9fa","--border-radius":"12px"}}),n.jsxs(o,{style:{"--background":"#f8f9fa","--border-radius":"12px",marginTop:"8px"},children:[n.jsx(s,{children:"Filter Bulan:"}),n.jsxs(l,{value:O,onIonChange:e=>R(e.detail.value),placeholder:"Semua Bulan",children:[n.jsx(d,{value:"",children:"Semua Bulan"}),(()=>{const e=new Set(S.map(e=>e.periode));return Array.from(e).sort()})().map(e=>n.jsx(d,{value:e,children:e},e))]})]})]}),I&&n.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[n.jsx(c,{name:"crescent"}),n.jsx("p",{style:{marginTop:"16px",color:"#666"},children:"Memuat laporan..."})]}),!I&&0===T.length&&n.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[n.jsxs(p,{color:"medium",children:[n.jsx("h3",{children:"📋 Belum Ada Laporan"}),n.jsx("p",{children:"Belum ada laporan harian yang dibuat."})]}),n.jsxs(g,{fill:"outline",onClick:G,children:[n.jsx(u,{icon:z,slot:"start"}),"Refresh"]})]}),!I&&T.map(e=>n.jsxs(m,{style:{marginBottom:"16px"},children:[n.jsxs(h,{children:[n.jsx(x,{style:{fontSize:"1.1rem",color:"#1880ff"},children:e.periode}),n.jsx(p,{color:"medium",children:n.jsx("small",{children:J(e.tanggal)})})]}),n.jsxs(j,{children:[n.jsx("p",{style:{margin:"0 0 12px 0",lineHeight:1.5},children:e.keterangan.length>100?e.keterangan.substring(0,100)+"...":e.keterangan}),n.jsxs("div",{style:{display:"flex",gap:"8px",justifyContent:"flex-end"},children:[n.jsxs(g,{size:"small",fill:"outline",color:"primary",onClick:()=>(e=>{M(e),K(!0)})(e),children:[n.jsx(u,{icon:E,slot:"start"}),"Detail"]}),n.jsxs(g,{size:"small",fill:"outline",color:"danger",onClick:()=>(async e=>{try{const a=await fetch("https://absensiku.trunois.my.id/api/laporan_harian.php",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({api_key:"absensiku_api_key_2023",id:e})}),n=await a.json();"success"===n.status?(N({message:"Laporan berhasil dihapus",color:"success",duration:3e3,position:"top"}),G()):N({message:n.message||"Gagal menghapus laporan",color:"danger",duration:3e3,position:"top"})}catch(a){N({message:"Terjadi kesalahan saat menghapus laporan",color:"danger",duration:3e3,position:"top"})}})(e.id),children:[n.jsx(u,{icon:A,slot:"start"}),"Hapus"]})]})]})]},e.id)),n.jsxs(f,{isOpen:P,onDidDismiss:()=>K(!1),children:[n.jsx(k,{children:n.jsxs(y,{children:[n.jsx(b,{children:"Detail Laporan"}),n.jsx(v,{slot:"end",children:n.jsx(g,{onClick:()=>K(!1),children:n.jsx(u,{icon:H})})})]})}),n.jsx(w,{className:"ion-padding",children:B&&n.jsxs("div",{children:[n.jsx("h2",{style:{color:"#1880ff",marginBottom:"8px"},children:B.periode}),n.jsx("p",{style:{color:"#666",marginBottom:"16px"},children:J(B.tanggal)}),n.jsxs("div",{style:{marginBottom:"20px"},children:[n.jsx("h3",{children:"Keterangan:"}),n.jsx("p",{style:{lineHeight:1.6,whiteSpace:"pre-wrap"},children:B.keterangan})]}),B.foto&&n.jsxs("div",{style:{marginBottom:"20px"},children:[n.jsx("h3",{children:"Foto:"}),n.jsx("img",{src:(W=B.foto,W?"https://absensiku.trunois.my.id/uploads/".concat(W):null),alt:"Foto Laporan",style:{width:"100%",maxHeight:"300px",objectFit:"cover",borderRadius:"8px",border:"1px solid #ddd"},onError:e=>{e.target.style.display="none"}})]}),n.jsxs("div",{style:{fontSize:"0.9rem",color:"#999"},children:[n.jsxs("p",{children:["Dibuat: ",new Date(B.created_at).toLocaleString("id-ID")]}),B.updated_at!==B.created_at&&n.jsxs("p",{children:["Diupdate: ",new Date(B.updated_at).toLocaleString("id-ID")]})]})]})})]})]});var W},K=({onLaporanAdded:i})=>{var t;const r=()=>{const e=new Date;return"".concat(["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][e.getMonth()]," ").concat(e.getFullYear())},[l,d]=e.useState({periode:r(),tanggal:(new Date).toISOString().split("T")[0],keterangan:""}),[c,f]=e.useState(null),[k,y]=e.useState(!1),[b]=a(),v=e.useRef(null),w=e.useRef(null),I=e.useRef(null),[F,L]=e.useState(!1),[_,z]=e.useState(null),E=JSON.parse(localStorage.getItem("user")||"{}");e.useEffect(()=>()=>{K()},[]);const{minDate:A,maxDate:H}=(()=>{const e=new Date,a=e.getFullYear(),n=e.getMonth();return{minDate:new Date(a,n,1).toISOString().split("T")[0],maxDate:new Date(a,n+1,0).toISOString().split("T")[0]}})(),P=async()=>{try{if(z(null),"https:"!==location.protocol&&"localhost"!==location.hostname)throw new Error("Camera requires HTTPS or localhost");if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw new Error("Camera API not supported on this device/browser");try{if("denied"===(await navigator.permissions.query({name:"camera"})).state)throw new Error("Camera permission denied. Please allow camera access in browser settings.")}catch(e){}L(!0);let n=null;const i=[{video:{facingMode:{exact:"environment"},width:{ideal:1280},height:{ideal:720}}},{video:{facingMode:"environment",width:{ideal:640},height:{ideal:480}}},{video:{facingMode:"user",width:{ideal:640},height:{ideal:480}}},{video:{width:{ideal:640},height:{ideal:480}}},{video:!0}];for(let e=0;e<i.length;e++)try{n=await navigator.mediaDevices.getUserMedia(i[e]);break}catch(a){if(e===i.length-1)throw a}if(!n||!v.current)throw new Error("Failed to get video stream or video element not available");v.current.srcObject=n,v.current.onloadedmetadata=()=>{v.current&&v.current.play().then(()=>{}).catch(e=>{})},v.current.oncanplay=()=>{},v.current.onerror=e=>{z("Video stream error"),K()}}catch(n){L(!1);let e="Tidak dapat mengakses kamera.";"NotAllowedError"===n.name||n.message.includes("permission")?e="Izin kamera ditolak. Silakan izinkan akses kamera di pengaturan browser.":"NotFoundError"===n.name?e="Kamera tidak ditemukan pada perangkat ini.":"NotSupportedError"===n.name?e="Kamera tidak didukung pada browser ini.":n.message.includes("HTTPS")&&(e="Kamera memerlukan koneksi HTTPS yang aman."),z(e),b({message:"".concat(e," Silakan gunakan upload file sebagai alternatif."),color:"warning",duration:5e3,position:"top"})}},K=()=>{try{if(v.current&&v.current.srcObject){const e=v.current.srcObject;e.getTracks().forEach(e=>{e.stop()}),v.current.srcObject=null,v.current.onloadedmetadata=null,v.current.oncanplay=null,v.current.onerror=null}L(!1),z(null)}catch(e){L(!1)}},N=()=>{I.current&&I.current.click()};return n.jsxs(n.Fragment,{children:[n.jsxs(m,{children:[n.jsx(h,{children:n.jsx(x,{style:{color:"#1880ff"},children:"➕ Tambah Laporan Harian"})}),n.jsxs(j,{children:[n.jsxs(o,{children:[n.jsx(s,{position:"stacked",children:"Periode Bulan *"}),n.jsx(S,{value:l.periode,readonly:!0,placeholder:"Periode bulan laporan",style:{"--color":"#666","--placeholder-color":"#999",cursor:"not-allowed"}}),n.jsx(C,{slot:"helper",color:"medium",children:"📅 Periode otomatis sesuai bulan saat ini"})]}),n.jsxs(o,{children:[n.jsx(s,{position:"stacked",children:"Tanggal *"}),n.jsx(S,{type:"date",value:l.tanggal,min:A,max:H,onIonInput:e=>d({...l,tanggal:e.detail.value})}),n.jsxs(C,{slot:"helper",color:"medium",children:["📅 Hanya bisa memilih tanggal di bulan ini (",A," s/d ",H,")"]})]}),n.jsxs(o,{children:[n.jsx(s,{position:"stacked",children:"Keterangan Kegiatan *"}),n.jsx(T,{value:l.keterangan,onIonInput:e=>d({...l,keterangan:e.detail.value}),placeholder:"Deskripsikan kegiatan yang dilakukan...",rows:4,maxlength:1e3})]}),n.jsxs("div",{style:{margin:"20px 0"},children:[n.jsx(p,{children:n.jsx("h3",{style:{margin:"0 0 12px 0"},children:"📷 Foto Kegiatan (Opsional)"})}),!c&&!F&&n.jsxs("div",{children:[n.jsxs("div",{style:{display:"flex",gap:"8px",margin:"12px 0"},children:[n.jsxs(g,{expand:"block",fill:"outline",onClick:P,style:{flex:1},children:[n.jsx(u,{icon:O,slot:"start"}),"Ambil Foto"]}),n.jsxs(g,{expand:"block",fill:"outline",color:"secondary",onClick:N,style:{flex:1},children:[n.jsx(u,{icon:R,slot:"start"}),"Upload File"]})]}),n.jsx("div",{style:{fontSize:"0.8rem",color:"#666",textAlign:"center",margin:"8px 0"},children:"💡 Tips: Pastikan aplikasi dizinkan akses kamera"})]}),n.jsx("input",{ref:I,type:"file",accept:"image/*",onChange:e=>{var a;const n=null==(a=e.target.files)?void 0:a[0];if(n){if(!n.type.startsWith("image/"))return void b({message:"Hanya file gambar yang diperbolehkan",color:"warning",duration:3e3,position:"top"});if(n.size>5242880)return void b({message:"Ukuran file maksimal 5MB",color:"warning",duration:3e3,position:"top"});const e=new FileReader;e.onload=e=>{var a;(null==(a=e.target)?void 0:a.result)&&(f(e.target.result),K())},e.onerror=()=>{b({message:"Gagal membaca file",color:"danger",duration:3e3,position:"top"})},e.readAsDataURL(n)}e.target&&(e.target.value="")},style:{display:"none"}}),_&&!F&&n.jsxs("div",{style:{background:"#fff3cd",color:"#856404",padding:"12px",borderRadius:"8px",margin:"12px 0",fontSize:"0.9rem"},children:[n.jsxs("div",{style:{marginBottom:"8px"},children:["⚠️ ",_]}),n.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"8px"},children:[_.includes("izin")||_.includes("permission")?n.jsx(g,{size:"small",fill:"outline",color:"warning",onClick:async()=>{try{(await navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(e=>e.stop()),b({message:"Izin kamera berhasil diberikan! Silakan coba ambil foto lagi.",color:"success",duration:3e3,position:"top"}),setTimeout(()=>{P()},500)}catch(e){b({message:"Gagal mendapatkan izin kamera. Silakan izinkan akses kamera di pengaturan browser.",color:"danger",duration:5e3,position:"top"})}},children:"🔓 Minta Izin Kamera"}):n.jsx(g,{size:"small",fill:"outline",color:"warning",onClick:P,children:"🔄 Coba Lagi"}),n.jsx(g,{size:"small",fill:"outline",color:"secondary",onClick:N,children:"📁 Upload File"})]})]}),F&&n.jsxs("div",{style:{marginBottom:"16px"},children:[n.jsxs("div",{style:{position:"relative"},children:[n.jsx("video",{ref:v,autoPlay:!0,playsInline:!0,muted:!0,style:{width:"100%",height:"250px",borderRadius:"8px",background:"#000",objectFit:"cover",display:"block"},onLoadedMetadata:()=>{},onCanPlay:()=>{},onError:e=>{z("Video stream error"),K()}}),F&&!(null==(t=v.current)?void 0:t.srcObject)&&n.jsx("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:"white",background:"rgba(0,0,0,0.8)",padding:"12px 20px",borderRadius:"8px",fontSize:"0.9rem",textAlign:"center"}})]}),n.jsx("canvas",{ref:w,style:{display:"none"}}),n.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"12px"},children:[n.jsxs(g,{expand:"block",onClick:()=>{try{if(v.current&&w.current){const e=v.current,a=w.current;if(e.readyState!==e.HAVE_ENOUGH_DATA)return void b({message:"Kamera belum siap. Tunggu sebentar dan coba lagi.",color:"warning",duration:3e3,position:"top"});a.width=e.videoWidth||640,a.height=e.videoHeight||480;const n=a.getContext("2d");if(n){n.drawImage(e,0,0,a.width,a.height);const i=a.toDataURL("image/jpeg",.8);f(i),b({message:"Foto berhasil diambil!",color:"success",duration:2e3,position:"top"})}K()}}catch(e){b({message:"Gagal mengambil foto. Coba lagi atau gunakan upload file.",color:"danger",duration:3e3,position:"top"})}},style:{flex:1},children:[n.jsx(u,{icon:O,slot:"start"}),"Ambil Foto"]}),n.jsx(g,{expand:"block",fill:"outline",color:"medium",onClick:K,style:{flex:1},children:"Batal"}),n.jsxs(g,{expand:"block",fill:"outline",color:"secondary",onClick:N,style:{flex:1},children:[n.jsx(u,{icon:B,slot:"start"}),"File"]})]})]}),c&&n.jsxs("div",{style:{marginBottom:"16px"},children:[n.jsx("img",{src:c,alt:"Foto Kegiatan",style:{width:"100%",maxHeight:"250px",borderRadius:"8px",objectFit:"cover",border:"1px solid #ddd"},onError:e=>{b({message:"Gagal memuat gambar",color:"danger",duration:3e3,position:"top"})}}),n.jsxs("div",{style:{display:"flex",gap:"8px",marginTop:"12px"},children:[n.jsxs(g,{expand:"block",fill:"outline",onClick:()=>{f(null),P()},style:{flex:1},children:[n.jsx(u,{icon:O,slot:"start"}),"Kamera"]}),n.jsxs(g,{expand:"block",fill:"outline",color:"secondary",onClick:N,style:{flex:1},children:[n.jsx(u,{icon:R,slot:"start"}),"Ganti File"]}),n.jsx(g,{expand:"block",fill:"outline",color:"danger",onClick:()=>{f(null),K()},style:{flex:1},children:"Hapus"})]})]})]}),n.jsxs(g,{expand:"block",onClick:async()=>{if(!l.periode)return void b({message:"Periode bulan wajib dipilih",color:"warning",duration:3e3,position:"top"});if(!l.tanggal)return void b({message:"Tanggal wajib diisi",color:"warning",duration:3e3,position:"top"});const e=l.tanggal;if(e<A||e>H)b({message:"Tanggal harus dalam bulan ini (".concat(A," s/d ").concat(H,")"),color:"warning",duration:4e3,position:"top"});else if(l.keterangan.trim())if(E.nama)if(l.keterangan.trim().length<10)b({message:"Keterangan minimal 10 karakter",color:"warning",duration:3e3,position:"top"});else{y(!0);try{let e="";if(c)try{c.startsWith("data:image/")?e=c:b({message:"Format foto tidak valid, laporan akan dikirim tanpa foto",color:"warning",duration:3e3,position:"top"})}catch(a){b({message:"Ada masalah dengan foto, laporan akan dikirim tanpa foto",color:"warning",duration:3e3,position:"top"})}const t={api_key:"absensiku_api_key_2023",nama_karyawan:E.nama,periode:l.periode,tanggal:l.tanggal,keterangan:l.keterangan.trim(),foto:e},o=await fetch("https://absensiku.trunois.my.id/api/laporan_harian.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!o.ok){await o.text();return void b({message:"Server error (".concat(o.status,"): ").concat(o.statusText),color:"danger",duration:5e3,position:"top"})}let s;const p=await o.text();try{s=JSON.parse(p)}catch(n){return void b({message:"Server mengembalikan response yang tidak valid. Coba lagi nanti.",color:"danger",duration:5e3,position:"top"})}"success"===s.status?(d({periode:r(),tanggal:(new Date).toISOString().split("T")[0],keterangan:""}),f(null),K(),i()):b({message:s.message||"Gagal menambahkan laporan",color:"danger",duration:3e3,position:"top"})}catch(t){t instanceof TypeError&&t.message.includes("fetch")?b({message:"Tidak dapat terhubung ke server. Periksa koneksi internet Anda.",color:"danger",duration:5e3,position:"top"}):b({message:"Terjadi kesalahan saat mengirim laporan. Coba lagi nanti.",color:"danger",duration:3e3,position:"top"})}finally{y(!1)}}else b({message:"Data user tidak valid. Silakan login ulang.",color:"warning",duration:3e3,position:"top"});else b({message:"Keterangan wajib diisi",color:"warning",duration:3e3,position:"top"})},disabled:k,style:{margin:"10px 0 0 0","--background":"#1880ff","--background-activated":"#005be7"},children:[n.jsx(u,{icon:M,slot:"start"}),k?"Mengirim...":"Simpan Laporan"]})]})]}),n.jsx(D,{isOpen:k,message:"Menyimpan laporan...",duration:0})]})},N={background:"linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)",minHeight:80,boxShadow:"none"},U={color:"#fff",fontSize:"1.2rem",fontWeight:"bold",textAlign:"center",marginRight:"50px"},G=()=>{const[i,t]=e.useState("riwayat"),[r]=a();return n.jsxs(I,{children:[n.jsx(k,{style:N,children:n.jsxs(y,{color:"transparent",style:{background:"transparent",minHeight:80,boxShadow:"none"},children:[n.jsx(v,{slot:"start",children:n.jsx(F,{defaultHref:"/home",text:"",style:{color:"#fff",fontSize:28,marginLeft:4,background:"rgba(0, 0, 0, 0)",borderRadius:12,padding:4}})}),n.jsx(b,{style:U,children:"Laporan Harian"})]})}),n.jsxs(w,{fullscreen:!0,children:[n.jsx("div",{style:{padding:"16px 16px 0 16px"},children:n.jsxs(L,{value:i,onIonChange:e=>t(e.detail.value),style:{background:"#f8f9fa",borderRadius:"12px",padding:"4px"},children:[n.jsx(_,{value:"riwayat",children:n.jsx(s,{style:{fontWeight:600},children:"📋 Riwayat"})}),n.jsx(_,{value:"tambah",children:n.jsx(s,{style:{fontWeight:600},children:"➕ Tambah"})})]})}),n.jsxs("div",{style:{padding:"16px"},children:["riwayat"===i&&n.jsx(P,{}),"tambah"===i&&n.jsx(K,{onLaporanAdded:()=>{t("riwayat"),r({message:"Laporan bulanan berhasil ditambahkan!",color:"success",duration:3e3,position:"top"})}})]})]})]})};export{G as default};

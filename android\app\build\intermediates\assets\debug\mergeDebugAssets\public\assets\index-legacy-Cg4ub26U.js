System.register(["./ionic-legacy-DbGqp7zN.js","./react-vendor-legacy-wCcNgjsd.js","./utils-legacy-DvNNcox0.js","./capacitor-legacy-cVgeOc-7.js"],function(e,t){"use strict";var n,r,o,a,i,l,s,u,c,d,f,p,m,g,h,v,b,y,w,k,x,S,z,E,C,P,N,_,j;return{setters:[e=>{n=e.r,r=e.f,o=e.j,a=e.I,i=e.h,l=e.k,s=e.m,u=e.n,c=e.o,d=e.p,f=e.q,p=e.s,m=e.t,g=e.u,h=e.v,v=e.w,b=e.x,y=e.d,w=e.y,k=e.z,x=e.A,S=e.B,z=e.C,E=e.R},e=>{C=e.u,P=e.R,N=e.b},e=>{_=e.S,j=e.D},null],execute:function(){var M=document.createElement("style");M.textContent='.login-bg{--background: linear-gradient(135deg, #eef2ff 0%, #f0fdfa 100%);min-height:100vh;display:flex;align-items:center;justify-content:center}.login-hero{position:absolute;inset:0 0 auto 0;height:36vh;background:linear-gradient(135deg,#1880ff 60%,#005be7);border-bottom-left-radius:48px;border-bottom-right-radius:48px;box-shadow:0 8px 32px rgba(24,128,255,.13);overflow:hidden}.login-blob{position:absolute;border-radius:50%;opacity:.15;filter:blur(20px)}.login-blob-1{width:220px;height:220px;background:#fff;top:-40px;right:-30px}.login-blob-2{width:280px;height:280px;background:#c7d2fe;bottom:-80px;left:-60px}.login-brand{position:relative;height:100%;max-width:420px;margin:0 auto;padding:24px 16px;display:flex;align-items:center;gap:14px;color:#fff}.login-container{max-width:420px;margin:0 auto;padding-top:28vh;display:flex;flex-direction:column;align-items:center}.login-logo{width:56px;height:56px;border-radius:16px;box-shadow:0 6px 22px rgba(0,0,0,.18);background:#fff}.login-title{font-size:1.8rem;font-weight:800;margin:0}.login-desc{color:#e0e7ff;font-size:1rem;margin:2px 0 0}.login-card{width:100%;box-shadow:0 4px 24px rgba(0,0,0,.07);border-radius:18px;border:1px solid rgba(24,128,255,.12)}.glass{background:rgba(255,255,255,.88)!important;-webkit-backdrop-filter:blur(12px);backdrop-filter:blur(12px)}.login-input{margin-bottom:12px}.login-input .input-wrapper.sc-ion-input-md{display:flex;align-items:center}.login-input ion-icon{font-size:1.5em;margin-right:6px;color:#6366f1;align-self:center}.login-input .label-floating.sc-ion-label-md{display:flex;align-items:center;height:100%;margin-left:.2em}.login-input .sc-ion-label-md-h{display:flex;align-items:center;height:100%}.login-btn{font-weight:600;letter-spacing:.5px;font-size:1.1rem;border-radius:24px;transition:background .2s}.login-btn.primary{box-shadow:0 10px 24px rgba(24,128,255,.18);transition:transform .12s}.login-btn.primary:hover{transform:translateY(-1px);box-shadow:0 12px 28px rgba(24,128,255,.24)}.login-btn.primary:active{transform:translateY(0)}.login-list{margin-bottom:8px}.login-input-item{margin-bottom:16px;border-radius:12px;--background: #f8fafc;box-shadow:0 1px 4px rgba(0,0,0,.03);min-height:54px}.input-with-icon{padding:6px;border:1px solid #e5e7eb;transition:box-shadow .2s,border-color .2s}.input-with-icon:focus-within{border-color:#1880ff;box-shadow:0 0 0 4px rgba(24,128,255,.1)}.stacked-field{flex:1;display:flex;flex-direction:column}.password-field{display:flex;align-items:center;gap:6px;position:relative}.password-field ion-input .native-input,.password-field .native-input.sc-ion-input-md{padding-right:44px}.eye-click-area{position:absolute;right:6px;top:50%;transform:translateY(-50%);width:36px;height:36px;display:flex;align-items:center;justify-content:center;background:transparent;border:none;padding:0}.eye-click-area:disabled{opacity:.6}.login-input-icon{font-size:1.4em;color:#6366f1;margin-right:4px;align-self:flex-start;margin-top:8px}.login-input-item .sc-ion-label-md-h,.login-input-item .label-floating.sc-ion-label-md{align-items:flex-start!important;margin-top:8px;font-size:1em}.login-input-item .label-floating.sc-ion-label-md{transform:translateY(-6px)}.login-input-item .native-input.sc-ion-input-md{padding-top:12px;padding-bottom:8px;font-size:1.08em}.eye-btn{margin-left:6px;--padding-start: 8px;--padding-end: 8px}.eye-btn ion-icon{font-size:1.4rem;color:#64748b}.eye-btn:hover ion-icon{color:#6366f1}.login-card-title{display:flex;align-items:center;gap:8px}.login-card-title-icon{font-size:1.2rem;color:#1880ff}@media (max-width: 480px){.login-container{max-width:94vw;padding-top:24vh}.login-card{border-radius:12px}.login-title{font-size:1.2rem}}:root{--ion-color-primary: #0054e9;--ion-color-primary-rgb: 0, 84, 233;--ion-color-primary-contrast: #fff;--ion-color-primary-contrast-rgb: 255, 255, 255;--ion-color-primary-shade: #004acd;--ion-color-primary-tint: #1a65eb;--ion-color-secondary: #0163aa;--ion-color-secondary-rgb: 1, 99, 170;--ion-color-secondary-contrast: #fff;--ion-color-secondary-contrast-rgb: 255, 255, 255;--ion-color-secondary-shade: #015796;--ion-color-secondary-tint: #1a73b3;--ion-color-tertiary: #6030ff;--ion-color-tertiary-rgb: 96, 48, 255;--ion-color-tertiary-contrast: #fff;--ion-color-tertiary-contrast-rgb: 255, 255, 255;--ion-color-tertiary-shade: #542ae0;--ion-color-tertiary-tint: #7045ff;--ion-color-success: #2dd55b;--ion-color-success-rgb: 45, 213, 91;--ion-color-success-contrast: #000;--ion-color-success-contrast-rgb: 0, 0, 0;--ion-color-success-shade: #28bb50;--ion-color-success-tint: #42d96b;--ion-color-warning: #ffc409;--ion-color-warning-rgb: 255, 196, 9;--ion-color-warning-contrast: #000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0ac08;--ion-color-warning-tint: #ffca22;--ion-color-danger: #c5000f;--ion-color-danger-rgb: 197, 0, 15;--ion-color-danger-contrast: #fff;--ion-color-danger-contrast-rgb: 255, 255, 255;--ion-color-danger-shade: #ad000d;--ion-color-danger-tint: #cb1a27;--ion-color-light: #f4f5f8;--ion-color-light-rgb: 244, 245, 248;--ion-color-light-contrast: #000;--ion-color-light-contrast-rgb: 0, 0, 0;--ion-color-light-shade: #d7d8da;--ion-color-light-tint: #f5f6f9;--ion-color-medium: #636469;--ion-color-medium-rgb: 99, 100, 105;--ion-color-medium-contrast: #fff;--ion-color-medium-contrast-rgb: 255, 255, 255;--ion-color-medium-shade: #57585c;--ion-color-medium-tint: #737478;--ion-color-dark: #222428;--ion-color-dark-rgb: 34, 36, 40;--ion-color-dark-contrast: #fff;--ion-color-dark-contrast-rgb: 255, 255, 255;--ion-color-dark-shade: #1e2023;--ion-color-dark-tint: #383a3e}html.ios{--ion-default-font: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Roboto", sans-serif}html.md{--ion-default-font: "Roboto", "Helvetica Neue", sans-serif}html{--ion-dynamic-font: -apple-system-body;--ion-font-family: var(--ion-default-font)}body{background:var(--ion-background-color);color:var(--ion-text-color)}body.backdrop-no-scroll{overflow:hidden}html.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,html.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,html.ios ion-modal ion-footer ion-toolbar:first-of-type,html.ios ion-footer.modal-footer-moving ion-toolbar:first-of-type{padding-top:6px}html.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,html.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type{padding-bottom:6px}html.ios ion-modal ion-toolbar,html.ios .modal-footer-moving ion-toolbar{padding-right:calc(var(--ion-safe-area-right) + 8px);padding-left:calc(var(--ion-safe-area-left) + 8px)}@media screen and (min-width: 768px){html.ios ion-modal.modal-card:first-of-type{--backdrop-opacity: .18}}ion-modal.modal-default.show-modal~ion-modal.modal-default{--backdrop-opacity: 0;--box-shadow: none}html.ios ion-modal.modal-card .ion-page{border-top-left-radius:var(--border-radius)}.ion-color-primary{--ion-color-base: var(--ion-color-primary, #0054e9) !important;--ion-color-base-rgb: var(--ion-color-primary-rgb, 0, 84, 233) !important;--ion-color-contrast: var(--ion-color-primary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-primary-shade, #004acd) !important;--ion-color-tint: var(--ion-color-primary-tint, #1a65eb) !important}.ion-color-secondary{--ion-color-base: var(--ion-color-secondary, #0163aa) !important;--ion-color-base-rgb: var(--ion-color-secondary-rgb, 1, 99, 170) !important;--ion-color-contrast: var(--ion-color-secondary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-secondary-shade, #015796) !important;--ion-color-tint: var(--ion-color-secondary-tint, #1a73b3) !important}.ion-color-tertiary{--ion-color-base: var(--ion-color-tertiary, #6030ff) !important;--ion-color-base-rgb: var(--ion-color-tertiary-rgb, 96, 48, 255) !important;--ion-color-contrast: var(--ion-color-tertiary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-tertiary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-tertiary-shade, #542ae0) !important;--ion-color-tint: var(--ion-color-tertiary-tint, #7045ff) !important}.ion-color-success{--ion-color-base: var(--ion-color-success, #2dd55b) !important;--ion-color-base-rgb: var(--ion-color-success-rgb, 45, 213, 91) !important;--ion-color-contrast: var(--ion-color-success-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-success-shade, #28bb50) !important;--ion-color-tint: var(--ion-color-success-tint, #42d96b) !important}.ion-color-warning{--ion-color-base: var(--ion-color-warning, #ffc409) !important;--ion-color-base-rgb: var(--ion-color-warning-rgb, 255, 196, 9) !important;--ion-color-contrast: var(--ion-color-warning-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-warning-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-warning-shade, #e0ac08) !important;--ion-color-tint: var(--ion-color-warning-tint, #ffca22) !important}.ion-color-danger{--ion-color-base: var(--ion-color-danger, #c5000f) !important;--ion-color-base-rgb: var(--ion-color-danger-rgb, 197, 0, 15) !important;--ion-color-contrast: var(--ion-color-danger-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-danger-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-danger-shade, #ad000d) !important;--ion-color-tint: var(--ion-color-danger-tint, #cb1a27) !important}.ion-color-light{--ion-color-base: var(--ion-color-light, #f4f5f8) !important;--ion-color-base-rgb: var(--ion-color-light-rgb, 244, 245, 248) !important;--ion-color-contrast: var(--ion-color-light-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-light-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-light-shade, #d7d8da) !important;--ion-color-tint: var(--ion-color-light-tint, #f5f6f9) !important}.ion-color-medium{--ion-color-base: var(--ion-color-medium, #636469) !important;--ion-color-base-rgb: var(--ion-color-medium-rgb, 99, 100, 105) !important;--ion-color-contrast: var(--ion-color-medium-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-medium-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-medium-shade, #57585c) !important;--ion-color-tint: var(--ion-color-medium-tint, #737478) !important}.ion-color-dark{--ion-color-base: var(--ion-color-dark, #222428) !important;--ion-color-base-rgb: var(--ion-color-dark-rgb, 34, 36, 40) !important;--ion-color-contrast: var(--ion-color-dark-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-dark-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-dark-shade, #1e2023) !important;--ion-color-tint: var(--ion-color-dark-tint, #383a3e) !important}.ion-page{left:0;right:0;top:0;bottom:0;display:flex;position:absolute;flex-direction:column;justify-content:space-between;contain:layout size style;z-index:0}ion-modal>.ion-page{position:relative;contain:layout style;height:100%}.split-pane-visible>.ion-page.split-pane-main{position:relative}ion-route,ion-route-redirect,ion-router,ion-select-option,ion-nav-controller,ion-menu-controller,ion-action-sheet-controller,ion-alert-controller,ion-loading-controller,ion-modal-controller,ion-picker-controller,ion-popover-controller,ion-toast-controller,.ion-page-hidden{display:none!important}.ion-page-invisible{opacity:0}.can-go-back>ion-header ion-back-button{display:block}html.plt-ios.plt-hybrid,html.plt-ios.plt-pwa{--ion-statusbar-padding: 20px}@supports (padding-top: 20px){html{--ion-safe-area-top: var(--ion-statusbar-padding)}}@supports (padding-top: env(safe-area-inset-top)){html{--ion-safe-area-top: env(safe-area-inset-top);--ion-safe-area-bottom: env(safe-area-inset-bottom);--ion-safe-area-left: env(safe-area-inset-left);--ion-safe-area-right: env(safe-area-inset-right)}}ion-card.ion-color .ion-inherit-color,ion-card-header.ion-color .ion-inherit-color{color:inherit}.menu-content{transform:translateZ(0)}.menu-content-open{cursor:pointer;touch-action:manipulation;pointer-events:none;overflow-y:hidden}.menu-content-open ion-content{--overflow: hidden}.menu-content-open .ion-content-scroll-host{overflow:hidden}.ios .menu-content-reveal{box-shadow:-8px 0 42px rgba(0,0,0,.08)}[dir=rtl].ios .menu-content-reveal{box-shadow:8px 0 42px rgba(0,0,0,.08)}.md .menu-content-reveal,.md .menu-content-push{box-shadow:4px 0 16px rgba(0,0,0,.18)}ion-accordion-group.accordion-group-expand-inset>ion-accordion:first-of-type{border-top-left-radius:8px;border-top-right-radius:8px}ion-accordion-group.accordion-group-expand-inset>ion-accordion:last-of-type{border-bottom-left-radius:8px;border-bottom-right-radius:8px}ion-accordion-group>ion-accordion:last-of-type ion-item[slot=header]{--border-width: 0px}ion-accordion.accordion-animated>[slot=header] .ion-accordion-toggle-icon{transition:.3s transform cubic-bezier(.25,.8,.5,1)}@media (prefers-reduced-motion: reduce){ion-accordion .ion-accordion-toggle-icon{transition:none!important}}ion-accordion.accordion-expanding>[slot=header] .ion-accordion-toggle-icon,ion-accordion.accordion-expanded>[slot=header] .ion-accordion-toggle-icon{transform:rotate(180deg)}ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-previous ion-item[slot=header]{--border-width: 0px;--inner-border-width: 0px}ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-expanding:first-of-type,ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-expanded:first-of-type{margin-top:0}ion-input input::-webkit-date-and-time-value{text-align:start}.ion-datetime-button-overlay{--width: fit-content;--height: fit-content}.ion-datetime-button-overlay ion-datetime.datetime-grid{width:320px;min-height:320px}[ion-last-focus],header[tabindex="-1"]:focus,[role=banner][tabindex="-1"]:focus,main[tabindex="-1"]:focus,[role=main][tabindex="-1"]:focus,h1[tabindex="-1"]:focus,[role=heading][aria-level="1"][tabindex="-1"]:focus{outline:none}.popover-viewport:has(>ion-content){overflow:hidden}@supports not selector(:has(> ion-content)){.popover-viewport{overflow:hidden}}audio,canvas,progress,video{vertical-align:baseline}audio:not([controls]){display:none;height:0}b,strong{font-weight:700}img{max-width:100%}hr{height:1px;border-width:0;box-sizing:content-box}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}label,input,select,textarea{font-family:inherit;line-height:normal}textarea{overflow:auto;height:auto;font:inherit;color:inherit}textarea::placeholder{padding-left:2px}form,input,optgroup,select{margin:0;font:inherit;color:inherit}html input[type=button],input[type=reset],input[type=submit]{cursor:pointer;-webkit-appearance:button}a,a div,a span,a ion-icon,a ion-label,button,button div,button span,button ion-icon,button ion-label,.ion-tappable,[tappable],[tappable] div,[tappable] span,[tappable] ion-icon,[tappable] ion-label,input,textarea{touch-action:manipulation}a ion-label,button ion-label{pointer-events:none}button{padding:0;border:0;border-radius:0;font-family:inherit;font-style:inherit;font-variant:inherit;line-height:1;text-transform:none;cursor:pointer;-webkit-appearance:button}[tappable]{cursor:pointer}a[disabled],button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*{box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none}html{width:100%;height:100%;-webkit-text-size-adjust:100%;text-size-adjust:100%}html.ion-ce body{display:block}html.plt-pwa{height:100vh}body{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin:0;padding:0;position:fixed;width:100%;max-width:100%;height:100%;max-height:100%;transform:translateZ(0);text-rendering:optimizeLegibility;overflow:hidden;touch-action:manipulation;-webkit-user-drag:none;-ms-content-zooming:none;word-wrap:break-word;overscroll-behavior-y:none;-webkit-text-size-adjust:none;text-size-adjust:none}html{font-family:var(--ion-font-family)}@supports (-webkit-touch-callout: none){html{font:var(--ion-dynamic-font, 16px var(--ion-font-family))}}a{background-color:transparent;color:var(--ion-color-primary, #0054e9)}h1,h2,h3,h4,h5,h6{margin-top:16px;margin-bottom:10px;font-weight:500;line-height:1.2}h1{margin-top:20px;font-size:1.625rem}h2{margin-top:18px;font-size:1.5rem}h3{font-size:1.375rem}h4{font-size:1.25rem}h5{font-size:1.125rem}h6{font-size:1rem}small{font-size:75%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}.ion-no-padding{--padding-start: 0;--padding-end: 0;--padding-top: 0;--padding-bottom: 0;padding:0}.ion-padding{--padding-start: var(--ion-padding, 16px);--padding-end: var(--ion-padding, 16px);--padding-top: var(--ion-padding, 16px);--padding-bottom: var(--ion-padding, 16px);-webkit-padding-start:var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px);-webkit-padding-end:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-top{--padding-top: var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px)}.ion-padding-start{--padding-start: var(--ion-padding, 16px);-webkit-padding-start:var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px)}.ion-padding-end{--padding-end: var(--ion-padding, 16px);-webkit-padding-end:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px)}.ion-padding-bottom{--padding-bottom: var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-vertical{--padding-top: var(--ion-padding, 16px);--padding-bottom: var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-horizontal{--padding-start: var(--ion-padding, 16px);--padding-end: var(--ion-padding, 16px);-webkit-padding-start:var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px);-webkit-padding-end:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px)}.ion-no-margin{--margin-start: 0;--margin-end: 0;--margin-top: 0;--margin-bottom: 0;margin:0}.ion-margin{--margin-start: var(--ion-margin, 16px);--margin-end: var(--ion-margin, 16px);--margin-top: var(--ion-margin, 16px);--margin-bottom: var(--ion-margin, 16px);-webkit-margin-start:var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px);-webkit-margin-end:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-top{--margin-top: var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px)}.ion-margin-start{--margin-start: var(--ion-margin, 16px);-webkit-margin-start:var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px)}.ion-margin-end{--margin-end: var(--ion-margin, 16px);-webkit-margin-end:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px)}.ion-margin-bottom{--margin-bottom: var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-vertical{--margin-top: var(--ion-margin, 16px);--margin-bottom: var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-horizontal{--margin-start: var(--ion-margin, 16px);--margin-end: var(--ion-margin, 16px);-webkit-margin-start:var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px);-webkit-margin-end:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px)}.ion-float-left{float:left!important}.ion-float-right{float:right!important}.ion-float-start{float:left!important}:host-context([dir=rtl]) .ion-float-start{float:right!important}[dir=rtl] .ion-float-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-start:dir(rtl){float:right!important}}.ion-float-end{float:right!important}:host-context([dir=rtl]) .ion-float-end{float:left!important}[dir=rtl] .ion-float-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-end:dir(rtl){float:left!important}}@media (min-width: 576px){.ion-float-sm-left{float:left!important}.ion-float-sm-right{float:right!important}.ion-float-sm-start{float:left!important}:host-context([dir=rtl]) .ion-float-sm-start{float:right!important}[dir=rtl] .ion-float-sm-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-sm-start:dir(rtl){float:right!important}}.ion-float-sm-end{float:right!important}:host-context([dir=rtl]) .ion-float-sm-end{float:left!important}[dir=rtl] .ion-float-sm-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-sm-end:dir(rtl){float:left!important}}}@media (min-width: 768px){.ion-float-md-left{float:left!important}.ion-float-md-right{float:right!important}.ion-float-md-start{float:left!important}:host-context([dir=rtl]) .ion-float-md-start{float:right!important}[dir=rtl] .ion-float-md-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-md-start:dir(rtl){float:right!important}}.ion-float-md-end{float:right!important}:host-context([dir=rtl]) .ion-float-md-end{float:left!important}[dir=rtl] .ion-float-md-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-md-end:dir(rtl){float:left!important}}}@media (min-width: 992px){.ion-float-lg-left{float:left!important}.ion-float-lg-right{float:right!important}.ion-float-lg-start{float:left!important}:host-context([dir=rtl]) .ion-float-lg-start{float:right!important}[dir=rtl] .ion-float-lg-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-lg-start:dir(rtl){float:right!important}}.ion-float-lg-end{float:right!important}:host-context([dir=rtl]) .ion-float-lg-end{float:left!important}[dir=rtl] .ion-float-lg-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-lg-end:dir(rtl){float:left!important}}}@media (min-width: 1200px){.ion-float-xl-left{float:left!important}.ion-float-xl-right{float:right!important}.ion-float-xl-start{float:left!important}:host-context([dir=rtl]) .ion-float-xl-start{float:right!important}[dir=rtl] .ion-float-xl-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-xl-start:dir(rtl){float:right!important}}.ion-float-xl-end{float:right!important}:host-context([dir=rtl]) .ion-float-xl-end{float:left!important}[dir=rtl] .ion-float-xl-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-xl-end:dir(rtl){float:left!important}}}.ion-text-center{text-align:center!important}.ion-text-justify{text-align:justify!important}.ion-text-start{text-align:start!important}.ion-text-end{text-align:end!important}.ion-text-left{text-align:left!important}.ion-text-right{text-align:right!important}.ion-text-nowrap{white-space:nowrap!important}.ion-text-wrap{white-space:normal!important}@media (min-width: 576px){.ion-text-sm-center{text-align:center!important}.ion-text-sm-justify{text-align:justify!important}.ion-text-sm-start{text-align:start!important}.ion-text-sm-end{text-align:end!important}.ion-text-sm-left{text-align:left!important}.ion-text-sm-right{text-align:right!important}.ion-text-sm-nowrap{white-space:nowrap!important}.ion-text-sm-wrap{white-space:normal!important}}@media (min-width: 768px){.ion-text-md-center{text-align:center!important}.ion-text-md-justify{text-align:justify!important}.ion-text-md-start{text-align:start!important}.ion-text-md-end{text-align:end!important}.ion-text-md-left{text-align:left!important}.ion-text-md-right{text-align:right!important}.ion-text-md-nowrap{white-space:nowrap!important}.ion-text-md-wrap{white-space:normal!important}}@media (min-width: 992px){.ion-text-lg-center{text-align:center!important}.ion-text-lg-justify{text-align:justify!important}.ion-text-lg-start{text-align:start!important}.ion-text-lg-end{text-align:end!important}.ion-text-lg-left{text-align:left!important}.ion-text-lg-right{text-align:right!important}.ion-text-lg-nowrap{white-space:nowrap!important}.ion-text-lg-wrap{white-space:normal!important}}@media (min-width: 1200px){.ion-text-xl-center{text-align:center!important}.ion-text-xl-justify{text-align:justify!important}.ion-text-xl-start{text-align:start!important}.ion-text-xl-end{text-align:end!important}.ion-text-xl-left{text-align:left!important}.ion-text-xl-right{text-align:right!important}.ion-text-xl-nowrap{white-space:nowrap!important}.ion-text-xl-wrap{white-space:normal!important}}.ion-text-uppercase{text-transform:uppercase!important}.ion-text-lowercase{text-transform:lowercase!important}.ion-text-capitalize{text-transform:capitalize!important}@media (min-width: 576px){.ion-text-sm-uppercase{text-transform:uppercase!important}.ion-text-sm-lowercase{text-transform:lowercase!important}.ion-text-sm-capitalize{text-transform:capitalize!important}}@media (min-width: 768px){.ion-text-md-uppercase{text-transform:uppercase!important}.ion-text-md-lowercase{text-transform:lowercase!important}.ion-text-md-capitalize{text-transform:capitalize!important}}@media (min-width: 992px){.ion-text-lg-uppercase{text-transform:uppercase!important}.ion-text-lg-lowercase{text-transform:lowercase!important}.ion-text-lg-capitalize{text-transform:capitalize!important}}@media (min-width: 1200px){.ion-text-xl-uppercase{text-transform:uppercase!important}.ion-text-xl-lowercase{text-transform:lowercase!important}.ion-text-xl-capitalize{text-transform:capitalize!important}}.ion-align-self-start{align-self:flex-start!important}.ion-align-self-end{align-self:flex-end!important}.ion-align-self-center{align-self:center!important}.ion-align-self-stretch{align-self:stretch!important}.ion-align-self-baseline{align-self:baseline!important}.ion-align-self-auto{align-self:auto!important}.ion-wrap{flex-wrap:wrap!important}.ion-nowrap{flex-wrap:nowrap!important}.ion-wrap-reverse{flex-wrap:wrap-reverse!important}.ion-justify-content-start{justify-content:flex-start!important}.ion-justify-content-center{justify-content:center!important}.ion-justify-content-end{justify-content:flex-end!important}.ion-justify-content-around{justify-content:space-around!important}.ion-justify-content-between{justify-content:space-between!important}.ion-justify-content-evenly{justify-content:space-evenly!important}.ion-align-items-start{align-items:flex-start!important}.ion-align-items-center{align-items:center!important}.ion-align-items-end{align-items:flex-end!important}.ion-align-items-stretch{align-items:stretch!important}.ion-align-items-baseline{align-items:baseline!important}.ion-hide,.ion-hide-up,.ion-hide-down{display:none!important}@media (min-width: 576px){.ion-hide-sm-up{display:none!important}}@media (max-width: 575.98px){.ion-hide-sm-down{display:none!important}}@media (min-width: 768px){.ion-hide-md-up{display:none!important}}@media (max-width: 767.98px){.ion-hide-md-down{display:none!important}}@media (min-width: 992px){.ion-hide-lg-up{display:none!important}}@media (max-width: 991.98px){.ion-hide-lg-down{display:none!important}}@media (min-width: 1200px){.ion-hide-xl-up{display:none!important}}@media (max-width: 1199.98px){.ion-hide-xl-down{display:none!important}}@media (prefers-color-scheme: dark){:root{--ion-color-primary: #4d8dff;--ion-color-primary-rgb: 77, 141, 255;--ion-color-primary-contrast: #000;--ion-color-primary-contrast-rgb: 0, 0, 0;--ion-color-primary-shade: #447ce0;--ion-color-primary-tint: #5f98ff;--ion-color-secondary: #46b1ff;--ion-color-secondary-rgb: 70, 177, 255;--ion-color-secondary-contrast: #000;--ion-color-secondary-contrast-rgb: 0, 0, 0;--ion-color-secondary-shade: #3e9ce0;--ion-color-secondary-tint: #59b9ff;--ion-color-tertiary: #8482fb;--ion-color-tertiary-rgb: 132, 130, 251;--ion-color-tertiary-contrast: #000;--ion-color-tertiary-contrast-rgb: 0, 0, 0;--ion-color-tertiary-shade: #7472dd;--ion-color-tertiary-tint: #908ffb;--ion-color-success: #2dd55b;--ion-color-success-rgb: 45, 213, 91;--ion-color-success-contrast: #000;--ion-color-success-contrast-rgb: 0, 0, 0;--ion-color-success-shade: #28bb50;--ion-color-success-tint: #42d96b;--ion-color-warning: #ffce31;--ion-color-warning-rgb: 255, 206, 49;--ion-color-warning-contrast: #000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0b52b;--ion-color-warning-tint: #ffd346;--ion-color-danger: #f24c58;--ion-color-danger-rgb: 242, 76, 88;--ion-color-danger-contrast: #000;--ion-color-danger-contrast-rgb: 0, 0, 0;--ion-color-danger-shade: #d5434d;--ion-color-danger-tint: #f35e69;--ion-color-light: #222428;--ion-color-light-rgb: 34, 36, 40;--ion-color-light-contrast: #fff;--ion-color-light-contrast-rgb: 255, 255, 255;--ion-color-light-shade: #1e2023;--ion-color-light-tint: #383a3e;--ion-color-medium: #989aa2;--ion-color-medium-rgb: 152, 154, 162;--ion-color-medium-contrast: #000;--ion-color-medium-contrast-rgb: 0, 0, 0;--ion-color-medium-shade: #86888f;--ion-color-medium-tint: #a2a4ab;--ion-color-dark: #f4f5f8;--ion-color-dark-rgb: 244, 245, 248;--ion-color-dark-contrast: #000;--ion-color-dark-contrast-rgb: 0, 0, 0;--ion-color-dark-shade: #d7d8da;--ion-color-dark-tint: #f5f6f9}:root.ios{--ion-background-color: #000000;--ion-background-color-rgb: 0, 0, 0;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-background-color-step-50: #0d0d0d;--ion-background-color-step-100: #1a1a1a;--ion-background-color-step-150: #262626;--ion-background-color-step-200: #333333;--ion-background-color-step-250: #404040;--ion-background-color-step-300: #4d4d4d;--ion-background-color-step-350: #595959;--ion-background-color-step-400: #666666;--ion-background-color-step-450: #737373;--ion-background-color-step-500: #808080;--ion-background-color-step-550: #8c8c8c;--ion-background-color-step-600: #999999;--ion-background-color-step-650: #a6a6a6;--ion-background-color-step-700: #b3b3b3;--ion-background-color-step-750: #bfbfbf;--ion-background-color-step-800: #cccccc;--ion-background-color-step-850: #d9d9d9;--ion-background-color-step-900: #e6e6e6;--ion-background-color-step-950: #f2f2f2;--ion-text-color-step-50: #f2f2f2;--ion-text-color-step-100: #e6e6e6;--ion-text-color-step-150: #d9d9d9;--ion-text-color-step-200: #cccccc;--ion-text-color-step-250: #bfbfbf;--ion-text-color-step-300: #b3b3b3;--ion-text-color-step-350: #a6a6a6;--ion-text-color-step-400: #999999;--ion-text-color-step-450: #8c8c8c;--ion-text-color-step-500: #808080;--ion-text-color-step-550: #737373;--ion-text-color-step-600: #666666;--ion-text-color-step-650: #595959;--ion-text-color-step-700: #4d4d4d;--ion-text-color-step-750: #404040;--ion-text-color-step-800: #333333;--ion-text-color-step-850: #262626;--ion-text-color-step-900: #1a1a1a;--ion-text-color-step-950: #0d0d0d;--ion-item-background: #000000;--ion-card-background: #1c1c1d}:root.ios ion-modal{--ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));--ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));--ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250))}:root.md{--ion-background-color: #121212;--ion-background-color-rgb: 18, 18, 18;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-background-color-step-50: #1e1e1e;--ion-background-color-step-100: #2a2a2a;--ion-background-color-step-150: #363636;--ion-background-color-step-200: #414141;--ion-background-color-step-250: #4d4d4d;--ion-background-color-step-300: #595959;--ion-background-color-step-350: #656565;--ion-background-color-step-400: #717171;--ion-background-color-step-450: #7d7d7d;--ion-background-color-step-500: #898989;--ion-background-color-step-550: #949494;--ion-background-color-step-600: #a0a0a0;--ion-background-color-step-650: #acacac;--ion-background-color-step-700: #b8b8b8;--ion-background-color-step-750: #c4c4c4;--ion-background-color-step-800: #d0d0d0;--ion-background-color-step-850: #dbdbdb;--ion-background-color-step-900: #e7e7e7;--ion-background-color-step-950: #f3f3f3;--ion-text-color-step-50: #f3f3f3;--ion-text-color-step-100: #e7e7e7;--ion-text-color-step-150: #dbdbdb;--ion-text-color-step-200: #d0d0d0;--ion-text-color-step-250: #c4c4c4;--ion-text-color-step-300: #b8b8b8;--ion-text-color-step-350: #acacac;--ion-text-color-step-400: #a0a0a0;--ion-text-color-step-450: #949494;--ion-text-color-step-500: #898989;--ion-text-color-step-550: #7d7d7d;--ion-text-color-step-600: #717171;--ion-text-color-step-650: #656565;--ion-text-color-step-700: #595959;--ion-text-color-step-750: #4d4d4d;--ion-text-color-step-800: #414141;--ion-text-color-step-850: #363636;--ion-text-color-step-900: #2a2a2a;--ion-text-color-step-950: #1e1e1e;--ion-item-background: #1e1e1e;--ion-toolbar-background: #1f1f1f;--ion-tab-bar-background: #1f1f1f;--ion-card-background: #1e1e1e}}\n',document.head.appendChild(M);var L={exports:{}},T={},A={exports:{}},O={};
/**
             * @license React
             * scheduler.production.js
             *
             * Copyright (c) Meta Platforms, Inc. and affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */
!function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a=performance;e.unstable_now=function(){return a.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}var s=[],u=[],c=1,d=null,f=3,p=!1,m=!1,g=!1,h="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function y(e){for(var o=n(u);null!==o;){if(null===o.callback)r(u);else{if(!(o.startTime<=e))break;r(u),o.sortIndex=o.expirationTime,t(s,o)}o=n(u)}}function w(e){if(g=!1,y(e),!m)if(null!==n(s))m=!0,j();else{var t=n(u);null!==t&&M(w,t.startTime-e)}}var k,x=!1,S=-1,z=5,E=-1;function C(){return!(e.unstable_now()-E<z)}function P(){if(x){var t=e.unstable_now();E=t;var o=!0;try{e:{m=!1,g&&(g=!1,v(S),S=-1),p=!0;var a=f;try{t:{for(y(t),d=n(s);null!==d&&!(d.expirationTime>t&&C());){var i=d.callback;if("function"==typeof i){d.callback=null,f=d.priorityLevel;var l=i(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof l){d.callback=l,y(t),o=!0;break t}d===n(s)&&r(s),y(t)}else r(s);d=n(s)}if(null!==d)o=!0;else{var c=n(u);null!==c&&M(w,c.startTime-t),o=!1}}break e}finally{d=null,f=a,p=!1}o=void 0}}finally{o?k():x=!1}}}if("function"==typeof b)k=function(){b(P)};else if("undefined"!=typeof MessageChannel){var N=new MessageChannel,_=N.port2;N.port1.onmessage=P,k=function(){_.postMessage(null)}}else k=function(){h(P,0)};function j(){x||(x=!0,k())}function M(t,n){S=h(function(){t(e.unstable_now())},n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){m||p||(m=!0,j())},e.unstable_forceFrameRate=function(e){0>e||125<e||(z=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,o,a){var i=e.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?i+a:i,r){case 1:var l=-1;break;case 2:l=250;break;case 5:l=**********;break;case 4:l=1e4;break;default:l=5e3}return r={id:c++,callback:o,priorityLevel:r,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(r.sortIndex=a,t(u,r),null===n(s)&&r===n(u)&&(g?(v(S),S=-1):g=!0,M(w,a-i))):(r.sortIndex=l,t(s,r),m||p||(m=!0,j())),r},e.unstable_shouldYield=C,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(O),A.exports=O;var D=A.exports,F=n,R=r;
/**
             * @license React
             * react-dom-client.production.js
             *
             * Copyright (c) Meta Platforms, Inc. and affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */function I(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function B(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}var U=Symbol.for("react.element"),V=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),$=Symbol.for("react.fragment"),Q=Symbol.for("react.strict_mode"),W=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),K=Symbol.for("react.consumer"),Y=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),X=Symbol.for("react.suspense"),J=Symbol.for("react.suspense_list"),Z=Symbol.for("react.memo"),ee=Symbol.for("react.lazy"),te=Symbol.for("react.offscreen"),ne=Symbol.for("react.memo_cache_sentinel"),re=Symbol.iterator;function oe(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=re&&e[re]||e["@@iterator"])?e:null}var ae=Symbol.for("react.client.reference");function ie(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===ae?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case $:return"Fragment";case H:return"Portal";case W:return"Profiler";case Q:return"StrictMode";case X:return"Suspense";case J:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Y:return(e.displayName||"Context")+".Provider";case K:return(e._context.displayName||"Context")+".Consumer";case G:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case Z:return null!==(t=e.displayName||null)?t:ie(e.type)||"Memo";case ee:t=e._payload,e=e._init;try{return ie(e(t))}catch(n){}}return null}var le,se,ue=F.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ce=Object.assign;function de(e){if(void 0===le)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);le=t&&t[1]||"",se=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+le+e+se}var fe=!1;function pe(e,t){if(!e||fe)return"";fe=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"==typeof n.catch&&n.catch(function(){})}}catch(l){if(l&&r&&"string"==typeof l.stack)return[l.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),i=a[0],l=a[1];if(i&&l){var s=i.split("\n"),u=l.split("\n");for(o=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;o<u.length&&!u[o].includes("DetermineComponentFrameRoot");)o++;if(r===s.length||o===u.length)for(r=s.length-1,o=u.length-1;1<=r&&0<=o&&s[r]!==u[o];)o--;for(;1<=r&&0<=o;r--,o--)if(s[r]!==u[o]){if(1!==r||1!==o)do{if(r--,0>--o||s[r]!==u[o]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=o);break}}}finally{fe=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?de(n):""}function me(e){switch(e.tag){case 26:case 27:case 5:return de(e.type);case 16:return de("Lazy");case 13:return de("Suspense");case 19:return de("SuspenseList");case 0:case 15:return e=pe(e.type,!1);case 11:return e=pe(e.type.render,!1);case 1:return e=pe(e.type,!0);default:return""}}function ge(e){try{var t="";do{t+=me(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function he(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function be(e){if(he(e)!==e)throw Error(I(188))}function ye(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=ye(e)))return t;e=e.sibling}return null}var we=Array.isArray,ke=R.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,xe={pending:!1,data:null,method:null,action:null},Se=[],ze=-1;function Ee(e){return{current:e}}function Ce(e){0>ze||(e.current=Se[ze],Se[ze]=null,ze--)}function Pe(e,t){ze++,Se[ze]=e.current,e.current=t}var Ne=Ee(null),_e=Ee(null),je=Ee(null),Me=Ee(null);function Le(e,t){switch(Pe(je,t),Pe(_e,e),Pe(Ne,null),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)&&(t=t.namespaceURI)?_d(t):0;break;default:if(t=(e=8===e?t.parentNode:t).tagName,e=e.namespaceURI)t=jd(e=_d(e),t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Ce(Ne),Pe(Ne,t)}function Te(){Ce(Ne),Ce(_e),Ce(je)}function Ae(e){null!==e.memoizedState&&Pe(Me,e);var t=Ne.current,n=jd(t,e.type);t!==n&&(Pe(_e,e),Pe(Ne,n))}function Oe(e){_e.current===e&&(Ce(Ne),Ce(_e)),Me.current===e&&(Ce(Me),hf._currentValue=xe)}var De=Object.prototype.hasOwnProperty,Fe=D.unstable_scheduleCallback,Re=D.unstable_cancelCallback,Ie=D.unstable_shouldYield,Be=D.unstable_requestPaint,Ue=D.unstable_now,Ve=D.unstable_getCurrentPriorityLevel,He=D.unstable_ImmediatePriority,$e=D.unstable_UserBlockingPriority,Qe=D.unstable_NormalPriority,We=D.unstable_LowPriority,qe=D.unstable_IdlePriority,Ke=D.log,Ye=D.unstable_setDisableYieldValue,Ge=null,Xe=null;function Je(e){if("function"==typeof Ke&&Ye(e),Xe&&"function"==typeof Xe.setStrictMode)try{Xe.setStrictMode(Ge,e)}catch(t){}}var Ze=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(et(e)/tt|0)|0},et=Math.log,tt=Math.LN2,nt=128,rt=4194304;function ot(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function at(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=e.warmLanes;e=0!==e.finishedLanes;var l=134217727&n;return 0!==l?0!==(n=l&~o)?r=ot(n):0!==(a&=l)?r=ot(a):e||0!==(i=l&~i)&&(r=ot(i)):0!==(l=n&~o)?r=ot(l):0!==a?r=ot(a):e||0!==(i=n&~i)&&(r=ot(i)),0===r?0:0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(i=t&-t)||32===o&&4194176&i)?t:r}function it(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function lt(e,t){switch(e){case 1:case 2:case 4:case 8:return t+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function st(){var e=nt;return!(4194176&(nt<<=1))&&(nt=128),e}function ut(){var e=rt;return!(62914560&(rt<<=1))&&(rt=4194304),e}function ct(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function dt(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ft(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-Ze(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194218&n}function pt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ze(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function mt(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function gt(){var e=ke.p;return 0!==e?e:void 0===(e=window.event)?32:Mf(e.type)}var ht=Math.random().toString(36).slice(2),vt="__reactFiber$"+ht,bt="__reactProps$"+ht,yt="__reactContainer$"+ht,wt="__reactEvents$"+ht,kt="__reactListeners$"+ht,xt="__reactHandles$"+ht,St="__reactResources$"+ht,zt="__reactMarker$"+ht;function Et(e){delete e[vt],delete e[bt],delete e[wt],delete e[kt],delete e[xt]}function Ct(e){var t=e[vt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[yt]||n[vt]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=Ud(e);null!==e;){if(n=e[vt])return n;e=Ud(e)}return t}n=(e=n).parentNode}return null}function Pt(e){if(e=e[vt]||e[yt]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Nt(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(I(33))}function _t(e){var t=e[St];return t||(t=e[St]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function jt(e){e[zt]=!0}var Mt=new Set,Lt={};function Tt(e,t){At(e,t),At(e+"Capture",t)}function At(e,t){for(Lt[e]=t,e=0;e<t.length;e++)Mt.add(t[e])}var Ot=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Dt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ft={},Rt={};function It(e,t,n){if(o=t,De.call(Rt,o)||!De.call(Ft,o)&&(Dt.test(o)?Rt[o]=!0:(Ft[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function Bt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function Ut(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function Vt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function Ht(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function $t(e){e._valueTracker||(e._valueTracker=function(e){var t=Ht(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Qt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ht(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Wt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var qt=/[\n"\\]/g;function Kt(e){return e.replace(qt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Yt(e,t,n,r,o,a,i,l){e.name="",null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+Vt(t)):e.value!==""+Vt(t)&&(e.value=""+Vt(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?Xt(e,i,Vt(t)):null!=n?Xt(e,i,Vt(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=a&&(e.defaultChecked=!!a),null!=o&&(e.checked=o&&"function"!=typeof o&&"symbol"!=typeof o),null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l?e.name=""+Vt(l):e.removeAttribute("name")}function Gt(e,t,n,r,o,a,i,l){if(null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&"boolean"!=typeof a&&(e.type=a),null!=t||null!=n){if(("submit"===a||"reset"===a)&&null==t)return;n=null!=n?""+Vt(n):"",t=null!=t?""+Vt(t):n,l||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:o)&&"symbol"!=typeof r&&!!r,e.checked=l?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i&&(e.name=i)}function Xt(e,t,n){"number"===t&&Wt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Jt(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Vt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function Zt(e,t,n){null==t||((t=""+Vt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+Vt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function en(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(I(92));if(we(r)){if(1<r.length)throw Error(I(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=Vt(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function tn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var nn=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function rn(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||nn.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function on(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(I(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&n[o]!==r&&rn(e,o,r)}else for(var a in t)t.hasOwnProperty(a)&&rn(e,a,t[a])}function an(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ln=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),sn=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function un(e){return sn.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var cn=null;function dn(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var fn=null,pn=null;function mn(e){var t=Pt(e);if(t&&(e=t.stateNode)){var n=e[bt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Yt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Kt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=r[bt]||null;if(!o)throw Error(I(90));Yt(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&Qt(r)}break e;case"textarea":Zt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&Jt(e,!!n.multiple,t,!1)}}}var gn=!1;function hn(e,t,n){if(gn)return e(t,n);gn=!0;try{return e(t)}finally{if(gn=!1,(null!==fn||null!==pn)&&(vc(),fn&&(t=fn,e=pn,pn=fn=null,mn(t),e)))for(t=0;t<e.length;t++)mn(e[t])}}function vn(e,t){var n=e.stateNode;if(null===n)return null;var r=n[bt]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(I(231,t,typeof n));return n}var bn=!1;if(Ot)try{var yn={};Object.defineProperty(yn,"passive",{get:function(){bn=!0}}),window.addEventListener("test",yn,yn),window.removeEventListener("test",yn,yn)}catch(Op){bn=!1}var wn=null,kn=null,xn=null;function Sn(){if(xn)return xn;var e,t,n=kn,r=n.length,o="value"in wn?wn.value:wn.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return xn=o.slice(e,1<t?1-t:void 0)}function zn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function En(){return!0}function Cn(){return!1}function Pn(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?En:Cn,this.isPropagationStopped=Cn,this}return ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=En)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=En)},persist:function(){},isPersistent:En}),t}var Nn,_n,jn,Mn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ln=Pn(Mn),Tn=ce({},Mn,{view:0,detail:0}),An=Pn(Tn),On=ce({},Tn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Wn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==jn&&(jn&&"mousemove"===e.type?(Nn=e.screenX-jn.screenX,_n=e.screenY-jn.screenY):_n=Nn=0,jn=e),Nn)},movementY:function(e){return"movementY"in e?e.movementY:_n}}),Dn=Pn(On),Fn=Pn(ce({},On,{dataTransfer:0})),Rn=Pn(ce({},Tn,{relatedTarget:0})),In=Pn(ce({},Mn,{animationName:0,elapsedTime:0,pseudoElement:0})),Bn=Pn(ce({},Mn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),Un=Pn(ce({},Mn,{data:0})),Vn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Hn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$n={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=$n[e])&&!!t[e]}function Wn(){return Qn}var qn=Pn(ce({},Tn,{key:function(e){if(e.key){var t=Vn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=zn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Hn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Wn,charCode:function(e){return"keypress"===e.type?zn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?zn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),Kn=Pn(ce({},On,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Yn=Pn(ce({},Tn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Wn})),Gn=Pn(ce({},Mn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Xn=Pn(ce({},On,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),Jn=Pn(ce({},Mn,{newState:0,oldState:0})),Zn=[9,13,27,32],er=Ot&&"CompositionEvent"in window,tr=null;Ot&&"documentMode"in document&&(tr=document.documentMode);var nr=Ot&&"TextEvent"in window&&!tr,rr=Ot&&(!er||tr&&8<tr&&11>=tr),or=String.fromCharCode(32),ar=!1;function ir(e,t){switch(e){case"keyup":return-1!==Zn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function lr(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var sr=!1,ur={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!ur[e.type]:"textarea"===t}function dr(e,t,n,r){fn?pn?pn.push(r):pn=[r]:fn=r,0<(t=gd(t,"onChange")).length&&(n=new Ln("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var fr=null,pr=null;function mr(e){ld(e,0)}function gr(e){if(Qt(Nt(e)))return e}function hr(e,t){if("change"===e)return t}var vr=!1;if(Ot){var br;if(Ot){var yr="oninput"in document;if(!yr){var wr=document.createElement("div");wr.setAttribute("oninput","return;"),yr="function"==typeof wr.oninput}br=yr}else br=!1;vr=br&&(!document.documentMode||9<document.documentMode)}function kr(){fr&&(fr.detachEvent("onpropertychange",xr),pr=fr=null)}function xr(e){if("value"===e.propertyName&&gr(pr)){var t=[];dr(t,pr,e,dn(e)),hn(mr,t)}}function Sr(e,t,n){"focusin"===e?(kr(),pr=n,(fr=t).attachEvent("onpropertychange",xr)):"focusout"===e&&kr()}function zr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return gr(pr)}function Er(e,t){if("click"===e)return gr(t)}function Cr(e,t){if("input"===e||"change"===e)return gr(t)}var Pr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Nr(e,t){if(Pr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!De.call(t,o)||!Pr(e[o],t[o]))return!1}return!0}function _r(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function jr(e,t){var n,r=_r(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=_r(r)}}function Mr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Mr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Lr(e){for(var t=Wt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Wt((e=t.contentWindow).document)}return t}function Tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function Ar(e,t){var n=Lr(t);t=e.focusedElem;var r=e.selectionRange;if(n!==t&&t&&t.ownerDocument&&Mr(t.ownerDocument.documentElement,t)){if(null!==r&&Tr(t))if(e=r.start,void 0===(n=r.end)&&(n=e),"selectionStart"in t)t.selectionStart=e,t.selectionEnd=Math.min(n,t.value.length);else if((n=(e=t.ownerDocument||document)&&e.defaultView||window).getSelection){n=n.getSelection();var o=t.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!n.extend&&a>r&&(o=r,r=a,a=o),o=jr(t,a);var i=jr(t,r);o&&i&&(1!==n.rangeCount||n.anchorNode!==o.node||n.anchorOffset!==o.offset||n.focusNode!==i.node||n.focusOffset!==i.offset)&&((e=e.createRange()).setStart(o.node,o.offset),n.removeAllRanges(),a>r?(n.addRange(e),n.extend(i.node,i.offset)):(e.setEnd(i.node,i.offset),n.addRange(e)))}for(e=[],n=t;n=n.parentNode;)1===n.nodeType&&e.push({element:n,left:n.scrollLeft,top:n.scrollTop});for("function"==typeof t.focus&&t.focus(),t=0;t<e.length;t++)(n=e[t]).element.scrollLeft=n.left,n.element.scrollTop=n.top}}var Or=Ot&&"documentMode"in document&&11>=document.documentMode,Dr=null,Fr=null,Rr=null,Ir=!1;function Br(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;Ir||null==Dr||Dr!==Wt(r)||(r="selectionStart"in(r=Dr)&&Tr(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},Rr&&Nr(Rr,r)||(Rr=r,0<(r=gd(Fr,"onSelect")).length&&(t=new Ln("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Dr)))}function Ur(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Vr={animationend:Ur("Animation","AnimationEnd"),animationiteration:Ur("Animation","AnimationIteration"),animationstart:Ur("Animation","AnimationStart"),transitionrun:Ur("Transition","TransitionRun"),transitionstart:Ur("Transition","TransitionStart"),transitioncancel:Ur("Transition","TransitionCancel"),transitionend:Ur("Transition","TransitionEnd")},Hr={},$r={};function Qr(e){if(Hr[e])return Hr[e];if(!Vr[e])return e;var t,n=Vr[e];for(t in n)if(n.hasOwnProperty(t)&&t in $r)return Hr[e]=n[t];return e}Ot&&($r=document.createElement("div").style,"AnimationEvent"in window||(delete Vr.animationend.animation,delete Vr.animationiteration.animation,delete Vr.animationstart.animation),"TransitionEvent"in window||delete Vr.transitionend.transition);var Wr=Qr("animationend"),qr=Qr("animationiteration"),Kr=Qr("animationstart"),Yr=Qr("transitionrun"),Gr=Qr("transitionstart"),Xr=Qr("transitioncancel"),Jr=Qr("transitionend"),Zr=new Map,eo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function to(e,t){Zr.set(e,t),Tt(t,[e])}var no=[],ro=0,oo=0;function ao(){for(var e=ro,t=oo=ro=0;t<e;){var n=no[t];no[t++]=null;var r=no[t];no[t++]=null;var o=no[t];no[t++]=null;var a=no[t];if(no[t++]=null,null!==r&&null!==o){var i=r.pending;null===i?o.next=o:(o.next=i.next,i.next=o),r.pending=o}0!==a&&uo(n,o,a)}}function io(e,t,n,r){no[ro++]=e,no[ro++]=t,no[ro++]=n,no[ro++]=r,oo|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function lo(e,t,n,r){return io(e,t,n,r),co(e)}function so(e,t){return io(e,null,null,t),co(e)}function uo(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(o=!0)),e=a,a=a.return;o&&null!==t&&3===e.tag&&(a=e.stateNode,o=31-Ze(n),null===(e=(a=a.hiddenUpdates)[o])?a[o]=[t]:e.push(t),t.lane=536870912|n)}function co(e){if(50<lc)throw lc=0,sc=null,Error(I(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var fo={},po=new WeakMap;function mo(e,t){if("object"==typeof e&&null!==e){var n=po.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ge(t)},po.set(e,t),t)}return{value:e,source:t,stack:ge(t)}}var go=[],ho=0,vo=null,bo=0,yo=[],wo=0,ko=null,xo=1,So="";function zo(e,t){go[ho++]=bo,go[ho++]=vo,vo=e,bo=t}function Eo(e,t,n){yo[wo++]=xo,yo[wo++]=So,yo[wo++]=ko,ko=e;var r=xo;e=So;var o=32-Ze(r)-1;r&=~(1<<o),n+=1;var a=32-Ze(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,xo=1<<32-Ze(t)+o|n<<o|r,So=a+e}else xo=1<<a|n<<o|r,So=e}function Co(e){null!==e.return&&(zo(e,1),Eo(e,1,0))}function Po(e){for(;e===vo;)vo=go[--ho],go[ho]=null,bo=go[--ho],go[ho]=null;for(;e===ko;)ko=yo[--wo],yo[wo]=null,So=yo[--wo],yo[wo]=null,xo=yo[--wo],yo[wo]=null}var No=null,_o=null,jo=!1,Mo=null,Lo=!1,To=Error(I(519));function Ao(e){throw Io(mo(Error(I(418,"")),e)),To}function Oo(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[vt]=e,t[bt]=r,n){case"dialog":sd("cancel",t),sd("close",t);break;case"iframe":case"object":case"embed":sd("load",t);break;case"video":case"audio":for(n=0;n<ad.length;n++)sd(ad[n],t);break;case"source":sd("error",t);break;case"img":case"image":case"link":sd("error",t),sd("load",t);break;case"details":sd("toggle",t);break;case"input":sd("invalid",t),Gt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),$t(t);break;case"select":sd("invalid",t);break;case"textarea":sd("invalid",t),en(t,r.value,r.defaultValue,r.children),$t(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||kd(t.textContent,n)?(null!=r.popover&&(sd("beforetoggle",t),sd("toggle",t)),null!=r.onScroll&&sd("scroll",t),null!=r.onScrollEnd&&sd("scrollend",t),null!=r.onClick&&(t.onclick=xd),t=!0):t=!1,t||Ao(e)}function Do(e){for(No=e.return;No;)switch(No.tag){case 3:case 27:return void(Lo=!0);case 5:case 13:return void(Lo=!1);default:No=No.return}}function Fo(e){if(e!==No)return!1;if(!jo)return Do(e),jo=!0,!1;var t,n=!1;if((t=3!==e.tag&&27!==e.tag)&&((t=5===e.tag)&&(t=!("form"!==(t=e.type)&&"button"!==t)||Md(e.type,e.memoizedProps)),t=!t),t&&(n=!0),n&&_o&&Ao(e),Do(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(I(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){_o=Bd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}_o=null}}else _o=No?Bd(e.stateNode.nextSibling):null;return!0}function Ro(){_o=No=null,jo=!1}function Io(e){null===Mo?Mo=[e]:Mo.push(e)}var Bo=Error(I(460)),Uo=Error(I(474)),Vo={then:function(){}};function Ho(e){return"fulfilled"===(e=e.status)||"rejected"===e}function $o(){}function Qo(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then($o,$o),t=n),t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===Bo)throw Error(I(483));throw e;default:if("string"==typeof t.status)t.then($o,$o);else{if(null!==(e=Au)&&100<e.shellSuspendCounter)throw Error(I(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===Bo)throw Error(I(483));throw e}throw Wo=t,Bo}}var Wo=null;function qo(){if(null===Wo)throw Error(I(459));var e=Wo;return Wo=null,e}var Ko=null,Yo=0;function Go(e){var t=Yo;return Yo+=1,null===Ko&&(Ko=[]),Qo(Ko,e,t)}function Xo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Jo(e,t){if(t.$$typeof===U)throw Error(I(525));throw e=Object.prototype.toString.call(t),Error(I(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Zo(e){return(0,e._init)(e._payload)}function ea(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(e,t){return(e=hu(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=33554434,n):r:(t.flags|=33554434,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=33554434),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=ku(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function s(e,t,n,r){var a=n.type;return a===$?c(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"==typeof a&&null!==a&&a.$$typeof===ee&&Zo(a)===t.type)?(Xo(t=o(t,n.props),n),t.return=e,t):(Xo(t=bu(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=xu(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function c(e,t,n,r,a){return null===t||7!==t.tag?((t=yu(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=ku(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case V:return Xo(n=bu(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case H:return(t=xu(t,e.mode,n)).return=e,t;case ee:return d(e,t=(0,t._init)(t._payload),n)}if(we(t)||oe(t))return(t=yu(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return d(e,Go(t),n);if(t.$$typeof===Y)return d(e,as(e,t),n);Jo(e,t)}return null}function f(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==o?null:l(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case V:return n.key===o?s(e,t,n,r):null;case H:return n.key===o?u(e,t,n,r):null;case ee:return f(e,t,n=(o=n._init)(n._payload),r)}if(we(n)||oe(n))return null!==o?null:c(e,t,n,r,null);if("function"==typeof n.then)return f(e,t,Go(n),r);if(n.$$typeof===Y)return f(e,t,as(e,n),r);Jo(e,n)}return null}function p(e,t,n,r,o){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return l(t,e=e.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case V:return s(t,e=e.get(null===r.key?n:r.key)||null,r,o);case H:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case ee:return p(e,t,n,r=(0,r._init)(r._payload),o)}if(we(r)||oe(r))return c(t,e=e.get(n)||null,r,o,null);if("function"==typeof r.then)return p(e,t,n,Go(r),o);if(r.$$typeof===Y)return p(e,t,n,as(t,r),o);Jo(t,r)}return null}function m(l,s,u,c){if("object"==typeof u&&null!==u&&u.type===$&&null===u.key&&(u=u.props.children),"object"==typeof u&&null!==u){switch(u.$$typeof){case V:e:{for(var g=u.key;null!==s;){if(s.key===g){if((g=u.type)===$){if(7===s.tag){n(l,s.sibling),(c=o(s,u.props.children)).return=l,l=c;break e}}else if(s.elementType===g||"object"==typeof g&&null!==g&&g.$$typeof===ee&&Zo(g)===s.type){n(l,s.sibling),Xo(c=o(s,u.props),u),c.return=l,l=c;break e}n(l,s);break}t(l,s),s=s.sibling}u.type===$?((c=yu(u.props.children,l.mode,c,u.key)).return=l,l=c):(Xo(c=bu(u.type,u.key,u.props,null,l.mode,c),u),c.return=l,l=c)}return i(l);case H:e:{for(g=u.key;null!==s;){if(s.key===g){if(4===s.tag&&s.stateNode.containerInfo===u.containerInfo&&s.stateNode.implementation===u.implementation){n(l,s.sibling),(c=o(s,u.children||[])).return=l,l=c;break e}n(l,s);break}t(l,s),s=s.sibling}(c=xu(u,l.mode,c)).return=l,l=c}return i(l);case ee:return m(l,s,u=(g=u._init)(u._payload),c)}if(we(u))return function(o,i,l,s){for(var u=null,c=null,m=i,g=i=0,h=null;null!==m&&g<l.length;g++){m.index>g?(h=m,m=null):h=m.sibling;var v=f(o,m,l[g],s);if(null===v){null===m&&(m=h);break}e&&m&&null===v.alternate&&t(o,m),i=a(v,i,g),null===c?u=v:c.sibling=v,c=v,m=h}if(g===l.length)return n(o,m),jo&&zo(o,g),u;if(null===m){for(;g<l.length;g++)null!==(m=d(o,l[g],s))&&(i=a(m,i,g),null===c?u=m:c.sibling=m,c=m);return jo&&zo(o,g),u}for(m=r(m);g<l.length;g++)null!==(h=p(m,o,g,l[g],s))&&(e&&null!==h.alternate&&m.delete(null===h.key?g:h.key),i=a(h,i,g),null===c?u=h:c.sibling=h,c=h);return e&&m.forEach(function(e){return t(o,e)}),jo&&zo(o,g),u}(l,s,u,c);if(oe(u)){if("function"!=typeof(g=oe(u)))throw Error(I(150));return function(o,i,l,s){if(null==l)throw Error(I(151));for(var u=null,c=null,m=i,g=i=0,h=null,v=l.next();null!==m&&!v.done;g++,v=l.next()){m.index>g?(h=m,m=null):h=m.sibling;var b=f(o,m,v.value,s);if(null===b){null===m&&(m=h);break}e&&m&&null===b.alternate&&t(o,m),i=a(b,i,g),null===c?u=b:c.sibling=b,c=b,m=h}if(v.done)return n(o,m),jo&&zo(o,g),u;if(null===m){for(;!v.done;g++,v=l.next())null!==(v=d(o,v.value,s))&&(i=a(v,i,g),null===c?u=v:c.sibling=v,c=v);return jo&&zo(o,g),u}for(m=r(m);!v.done;g++,v=l.next())null!==(v=p(m,o,g,v.value,s))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),i=a(v,i,g),null===c?u=v:c.sibling=v,c=v);return e&&m.forEach(function(e){return t(o,e)}),jo&&zo(o,g),u}(l,s,u=g.call(u),c)}if("function"==typeof u.then)return m(l,s,Go(u),c);if(u.$$typeof===Y)return m(l,s,as(l,u),c);Jo(l,u)}return"string"==typeof u&&""!==u||"number"==typeof u||"bigint"==typeof u?(u=""+u,null!==s&&6===s.tag?(n(l,s.sibling),(c=o(s,u)).return=l,l=c):(n(l,s),(c=ku(u,l.mode,c)).return=l,l=c),i(l)):n(l,s)}return function(e,t,n,r){try{Yo=0;var o=m(e,t,n,r);return Ko=null,o}catch(i){if(i===Bo)throw i;var a=mu(29,i,null,e.mode);return a.lanes=r,a.return=e,a}}}var ta=ea(!0),na=ea(!1),ra=Ee(null),oa=Ee(0);function aa(e,t){Pe(oa,e=Vu),Pe(ra,t),Vu=e|t.baseLanes}function ia(){Pe(oa,Vu),Pe(ra,ra.current)}function la(){Vu=oa.current,Ce(ra),Ce(oa)}var sa=Ee(null),ua=null;function ca(e){var t=e.alternate;Pe(ma,1&ma.current),Pe(sa,e),null===ua&&(null===t||null!==ra.current||null!==t.memoizedState)&&(ua=e)}function da(e){if(22===e.tag){if(Pe(ma,ma.current),Pe(sa,e),null===ua){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ua=e)}}else fa()}function fa(){Pe(ma,ma.current),Pe(sa,sa.current)}function pa(e){Ce(sa),ua===e&&(ua=null),Ce(ma)}var ma=Ee(0);function ga(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ha="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},va=D.unstable_scheduleCallback,ba=D.unstable_NormalPriority,ya={$$typeof:Y,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function wa(){return{controller:new ha,data:new Map,refCount:0}}function ka(e){e.refCount--,0===e.refCount&&va(ba,function(){e.controller.abort()})}var xa=null,Sa=0,za=0,Ea=null;function Ca(){if(0===--Sa&&null!==xa){null!==Ea&&(Ea.status="fulfilled");var e=xa;xa=null,za=0,Ea=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Pa=ue.S;ue.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===xa){var n=xa=[];Sa=0,za=ed(),Ea={status:"pending",value:void 0,then:function(e){n.push(e)}}}Sa++,t.then(Ca,Ca)}(0,t),null!==Pa&&Pa(e,t)};var Na=Ee(null);function _a(){var e=Na.current;return null!==e?e:Au.pooledCache}function ja(e,t){Pe(Na,null===t?Na.current:t.pool)}function Ma(){var e=_a();return null===e?null:{parent:ya._currentValue,pool:e}}var La=0,Ta=null,Aa=null,Oa=null,Da=!1,Fa=!1,Ra=!1,Ia=0,Ba=0,Ua=null,Va=0;function Ha(){throw Error(I(321))}function $a(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Pr(e[n],t[n]))return!1;return!0}function Qa(e,t,n,r,o,a){return La=a,Ta=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ue.H=null===e||null===e.memoizedState?al:il,Ra=!1,a=n(r,o),Ra=!1,Fa&&(a=qa(t,n,r,o)),Wa(e),a}function Wa(e){ue.H=ol;var t=null!==Aa&&null!==Aa.next;if(La=0,Oa=Aa=Ta=null,Da=!1,Ba=0,Ua=null,t)throw Error(I(300));null===e||Sl||null!==(e=e.dependencies)&&ns(e)&&(Sl=!0)}function qa(e,t,n,r){Ta=e;var o=0;do{if(Fa&&(Ua=null),Ba=0,Fa=!1,25<=o)throw Error(I(301));if(o+=1,Oa=Aa=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}ue.H=ll,a=t(n,r)}while(Fa);return a}function Ka(){var e=ue.H,t=e.useState()[0];return t="function"==typeof t.then?ei(t):t,e=e.useState()[0],(null!==Aa?Aa.memoizedState:null)!==e&&(Ta.flags|=1024),t}function Ya(){var e=0!==Ia;return Ia=0,e}function Ga(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Xa(e){if(Da){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Da=!1}La=0,Oa=Aa=Ta=null,Fa=!1,Ba=Ia=0,Ua=null}function Ja(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Oa?Ta.memoizedState=Oa=e:Oa=Oa.next=e,Oa}function Za(){if(null===Aa){var e=Ta.alternate;e=null!==e?e.memoizedState:null}else e=Aa.next;var t=null===Oa?Ta.memoizedState:Oa.next;if(null!==t)Oa=t,Aa=e;else{if(null===e){if(null===Ta.alternate)throw Error(I(467));throw Error(I(310))}e={memoizedState:(Aa=e).memoizedState,baseState:Aa.baseState,baseQueue:Aa.baseQueue,queue:Aa.queue,next:null},null===Oa?Ta.memoizedState=Oa=e:Oa=Oa.next=e}return Oa}function ei(e){var t=Ba;return Ba+=1,null===Ua&&(Ua=[]),e=Qo(Ua,e,t),t=Ta,null===(null===Oa?t.memoizedState:Oa.next)&&(t=t.alternate,ue.H=null===t||null===t.memoizedState?al:il),e}function ti(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return ei(e);if(e.$$typeof===Y)return os(e)}throw Error(I(438,String(e)))}function ni(e){var t=null,n=Ta.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=Ta.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},Ta.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=ne;return t.index++,n}function ri(e,t){return"function"==typeof t?t(e):t}function oi(e){return ai(Za(),Aa,e)}function ai(e,t,n){var r=e.queue;if(null===r)throw Error(I(311));r.lastRenderedReducer=n;var o=e.baseQueue,a=r.pending;if(null!==a){if(null!==o){var i=o.next;o.next=a.next,a.next=i}t.baseQueue=o=a,r.pending=null}if(a=e.baseState,null===o)e.memoizedState=a;else{var l=i=null,s=null,u=t=o.next,c=!1;do{var d=-536870913&u.lane;if(d!==u.lane?(Du&d)===d:(La&d)===d){var f=u.revertLane;if(0===f)null!==s&&(s=s.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),d===za&&(c=!0);else{if((La&f)===f){u=u.next,f===za&&(c=!0);continue}d={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===s?(l=s=d,i=a):s=s.next=d,Ta.lanes|=f,$u|=f}d=u.action,Ra&&n(a,d),a=u.hasEagerState?u.eagerState:n(a,d)}else f={lane:d,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===s?(l=s=f,i=a):s=s.next=f,Ta.lanes|=d,$u|=d;u=u.next}while(null!==u&&u!==t);if(null===s?i=a:s.next=l,!Pr(a,e.memoizedState)&&(Sl=!0,c&&null!==(n=Ea)))throw n;e.memoizedState=a,e.baseState=i,e.baseQueue=s,r.lastRenderedState=a}return null===o&&(r.lanes=0),[e.memoizedState,r.dispatch]}function ii(e){var t=Za(),n=t.queue;if(null===n)throw Error(I(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var i=o=o.next;do{a=e(a,i.action),i=i.next}while(i!==o);Pr(a,t.memoizedState)||(Sl=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function li(e,t,n){var r=Ta,o=Za(),a=jo;if(a){if(void 0===n)throw Error(I(407));n=n()}else n=t();var i=!Pr((Aa||o).memoizedState,n);if(i&&(o.memoizedState=n,Sl=!0),o=o.queue,Li(ci.bind(null,r,o,e),[e]),o.getSnapshot!==t||i||null!==Oa&&1&Oa.memoizedState.tag){if(r.flags|=2048,Pi(9,ui.bind(null,r,o,n,t),{destroy:void 0},null),null===Au)throw Error(I(349));a||60&La||si(r,t,n)}return n}function si(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=Ta.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},Ta.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function ui(e,t,n,r){t.value=n,t.getSnapshot=r,di(t)&&fi(e)}function ci(e,t,n){return n(function(){di(t)&&fi(e)})}function di(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Pr(e,n)}catch(r){return!0}}function fi(e){var t=so(e,2);null!==t&&dc(t,0,2)}function pi(e){var t=Ja();if("function"==typeof e){var n=e;if(e=n(),Ra){Je(!0);try{n()}finally{Je(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ri,lastRenderedState:e},t}function mi(e,t,n,r){return e.baseState=n,ai(e,Aa,"function"==typeof r?r:ri)}function gi(e,t,n,r,o){if(tl(e))throw Error(I(485));if(null!==(e=t.action)){var a={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==ue.T?n(!0):a.isTransition=!1,r(a),null===(n=t.pending)?(a.next=t.pending=a,hi(t,a)):(a.next=n.next,t.pending=n.next=a)}}function hi(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var a=ue.T,i={};ue.T=i;try{var l=n(o,r),s=ue.S;null!==s&&s(i,l),vi(e,t,l)}catch(u){yi(e,t,u)}finally{ue.T=a}}else try{vi(e,t,a=n(o,r))}catch(c){yi(e,t,c)}}function vi(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then(function(n){bi(e,t,n)},function(n){return yi(e,t,n)}):bi(e,t,n)}function bi(e,t,n){t.status="fulfilled",t.value=n,wi(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,hi(e,n)))}function yi(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,wi(t),t=t.next}while(t!==r)}e.action=null}function wi(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function ki(e,t){return t}function xi(e,t){if(jo){var n=Au.formState;if(null!==n){e:{var r=Ta;if(jo){if(_o){t:{for(var o=_o,a=Lo;8!==o.nodeType;){if(!a){o=null;break t}if(null===(o=Bd(o.nextSibling))){o=null;break t}}o="F!"===(a=o.data)||"F"===a?o:null}if(o){_o=Bd(o.nextSibling),r="F!"===o.data;break e}}Ao(r)}r=!1}r&&(t=n[0])}}return(n=Ja()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ki,lastRenderedState:t},n.queue=r,n=Ji.bind(null,Ta,r),r.dispatch=n,r=pi(!1),a=el.bind(null,Ta,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=Ja()).queue=o,n=gi.bind(null,Ta,o,a,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function Si(e){return zi(Za(),Aa,e)}function zi(e,t,n){t=ai(e,t,ki)[0],e=oi(ri)[0],t="object"==typeof t&&null!==t&&"function"==typeof t.then?ei(t):t;var r=Za(),o=r.queue,a=o.dispatch;return n!==r.memoizedState&&(Ta.flags|=2048,Pi(9,Ei.bind(null,o,n),{destroy:void 0},null)),[t,a,e]}function Ei(e,t){e.action=t}function Ci(e){var t=Za(),n=Aa;if(null!==n)return zi(t,n,e);Za(),t=t.memoizedState;var r=(n=Za()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function Pi(e,t,n,r){return e={tag:e,create:t,inst:n,deps:r,next:null},null===(t=Ta.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},Ta.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ni(){return Za().memoizedState}function _i(e,t,n,r){var o=Ja();Ta.flags|=e,o.memoizedState=Pi(1|t,n,{destroy:void 0},void 0===r?null:r)}function ji(e,t,n,r){var o=Za();r=void 0===r?null:r;var a=o.memoizedState.inst;null!==Aa&&null!==r&&$a(r,Aa.memoizedState.deps)?o.memoizedState=Pi(t,n,a,r):(Ta.flags|=e,o.memoizedState=Pi(1|t,n,a,r))}function Mi(e,t){_i(8390656,8,e,t)}function Li(e,t){ji(2048,8,e,t)}function Ti(e,t){return ji(4,2,e,t)}function Ai(e,t){return ji(4,4,e,t)}function Oi(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Di(e,t,n){n=null!=n?n.concat([e]):null,ji(4,4,Oi.bind(null,t,e),n)}function Fi(){}function Ri(e,t){var n=Za();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&$a(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ii(e,t){var n=Za();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&$a(t,r[1]))return r[0];if(r=e(),Ra){Je(!0);try{e()}finally{Je(!1)}}return n.memoizedState=[r,t],r}function Bi(e,t,n){return void 0===n||1073741824&La?e.memoizedState=t:(e.memoizedState=n,e=cc(),Ta.lanes|=e,$u|=e,n)}function Ui(e,t,n,r){return Pr(n,t)?n:null!==ra.current?(e=Bi(e,n,r),Pr(e,t)||(Sl=!0),e):42&La?(e=cc(),Ta.lanes|=e,$u|=e,t):(Sl=!0,e.memoizedState=n)}function Vi(e,t,n,r,o){var a=ke.p;ke.p=0!==a&&8>a?a:8;var i,l,s,u=ue.T,c={};ue.T=c,el(e,!1,t,n);try{var d=o(),f=ue.S;if(null!==f&&f(c,d),null!==d&&"object"==typeof d&&"function"==typeof d.then){var p=(i=r,l=[],s={status:"pending",value:null,reason:null,then:function(e){l.push(e)}},d.then(function(){s.status="fulfilled",s.value=i;for(var e=0;e<l.length;e++)(0,l[e])(i)},function(e){for(s.status="rejected",s.reason=e,e=0;e<l.length;e++)(0,l[e])(void 0)}),s);Zi(e,t,p,uc())}else Zi(e,t,r,uc())}catch(m){Zi(e,t,{then:function(){},status:"rejected",reason:m},uc())}finally{ke.p=a,ue.T=u}}function Hi(){}function $i(e,t,n,r){if(5!==e.tag)throw Error(I(476));var o=Qi(e).queue;Vi(e,o,t,xe,null===n?Hi:function(){return Wi(e),n(r)})}function Qi(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:xe,baseState:xe,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ri,lastRenderedState:xe},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ri,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Wi(e){Zi(e,Qi(e).next.queue,{},uc())}function qi(){return os(hf)}function Ki(){return Za().memoizedState}function Yi(){return Za().memoizedState}function Gi(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=uc(),r=ds(t,e=cs(n),n);return null!==r&&(dc(r,0,n),fs(r,t,n)),t={cache:wa()},void(e.payload=t)}t=t.return}}function Xi(e,t,n){var r=uc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},tl(e)?nl(t,n):null!==(n=lo(e,t,n,r))&&(dc(n,0,r),rl(n,t,r))}function Ji(e,t,n){Zi(e,t,n,uc())}function Zi(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(tl(e))nl(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,Pr(l,i))return io(e,t,o,0),null===Au&&ao(),!1}catch(s){}if(null!==(n=lo(e,t,o,r)))return dc(n,0,r),rl(n,t,r),!0}return!1}function el(e,t,n,r){if(r={lane:2,revertLane:ed(),action:r,hasEagerState:!1,eagerState:null,next:null},tl(e)){if(t)throw Error(I(479))}else null!==(t=lo(e,n,r,2))&&dc(t,0,2)}function tl(e){var t=e.alternate;return e===Ta||null!==t&&t===Ta}function nl(e,t){Fa=Da=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function rl(e,t,n){if(4194176&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,pt(e,n)}}var ol={readContext:os,use:ti,useCallback:Ha,useContext:Ha,useEffect:Ha,useImperativeHandle:Ha,useLayoutEffect:Ha,useInsertionEffect:Ha,useMemo:Ha,useReducer:Ha,useRef:Ha,useState:Ha,useDebugValue:Ha,useDeferredValue:Ha,useTransition:Ha,useSyncExternalStore:Ha,useId:Ha};ol.useCacheRefresh=Ha,ol.useMemoCache=Ha,ol.useHostTransitionStatus=Ha,ol.useFormState=Ha,ol.useActionState=Ha,ol.useOptimistic=Ha;var al={readContext:os,use:ti,useCallback:function(e,t){return Ja().memoizedState=[e,void 0===t?null:t],e},useContext:os,useEffect:Mi,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,_i(4194308,4,Oi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _i(4194308,4,e,t)},useInsertionEffect:function(e,t){_i(4,2,e,t)},useMemo:function(e,t){var n=Ja();t=void 0===t?null:t;var r=e();if(Ra){Je(!0);try{e()}finally{Je(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ja();if(void 0!==n){var o=n(t);if(Ra){Je(!0);try{n(t)}finally{Je(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=Xi.bind(null,Ta,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ja().memoizedState=e},useState:function(e){var t=(e=pi(e)).queue,n=Ji.bind(null,Ta,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Fi,useDeferredValue:function(e,t){return Bi(Ja(),e,t)},useTransition:function(){var e=pi(!1);return e=Vi.bind(null,Ta,e.queue,!0,!1),Ja().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=Ta,o=Ja();if(jo){if(void 0===n)throw Error(I(407));n=n()}else{if(n=t(),null===Au)throw Error(I(349));60&Du||si(r,t,n)}o.memoizedState=n;var a={value:n,getSnapshot:t};return o.queue=a,Mi(ci.bind(null,r,a,e),[e]),r.flags|=2048,Pi(9,ui.bind(null,r,a,n,t),{destroy:void 0},null),n},useId:function(){var e=Ja(),t=Au.identifierPrefix;if(jo){var n=So;t=":"+t+"R"+(n=(xo&~(1<<32-Ze(xo)-1)).toString(32)+n),0<(n=Ia++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=Va++).toString(32)+":";return e.memoizedState=t},useCacheRefresh:function(){return Ja().memoizedState=Gi.bind(null,Ta)}};al.useMemoCache=ni,al.useHostTransitionStatus=qi,al.useFormState=xi,al.useActionState=xi,al.useOptimistic=function(e){var t=Ja();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=el.bind(null,Ta,!0,n),n.dispatch=t,[e,t]};var il={readContext:os,use:ti,useCallback:Ri,useContext:os,useEffect:Li,useImperativeHandle:Di,useInsertionEffect:Ti,useLayoutEffect:Ai,useMemo:Ii,useReducer:oi,useRef:Ni,useState:function(){return oi(ri)},useDebugValue:Fi,useDeferredValue:function(e,t){return Ui(Za(),Aa.memoizedState,e,t)},useTransition:function(){var e=oi(ri)[0],t=Za().memoizedState;return["boolean"==typeof e?e:ei(e),t]},useSyncExternalStore:li,useId:Ki};il.useCacheRefresh=Yi,il.useMemoCache=ni,il.useHostTransitionStatus=qi,il.useFormState=Si,il.useActionState=Si,il.useOptimistic=function(e,t){return mi(Za(),0,e,t)};var ll={readContext:os,use:ti,useCallback:Ri,useContext:os,useEffect:Li,useImperativeHandle:Di,useInsertionEffect:Ti,useLayoutEffect:Ai,useMemo:Ii,useReducer:ii,useRef:Ni,useState:function(){return ii(ri)},useDebugValue:Fi,useDeferredValue:function(e,t){var n=Za();return null===Aa?Bi(n,e,t):Ui(n,Aa.memoizedState,e,t)},useTransition:function(){var e=ii(ri)[0],t=Za().memoizedState;return["boolean"==typeof e?e:ei(e),t]},useSyncExternalStore:li,useId:Ki};function sl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:ce({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}ll.useCacheRefresh=Yi,ll.useMemoCache=ni,ll.useHostTransitionStatus=qi,ll.useFormState=Ci,ll.useActionState=Ci,ll.useOptimistic=function(e,t){var n=Za();return null!==Aa?mi(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])};var ul={isMounted:function(e){return!!(e=e._reactInternals)&&he(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=uc(),o=cs(r);o.payload=t,null!=n&&(o.callback=n),null!==(t=ds(e,o,r))&&(dc(t,0,r),fs(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=uc(),o=cs(r);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=ds(e,o,r))&&(dc(t,0,r),fs(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=uc(),r=cs(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=ds(e,r,n))&&(dc(t,0,n),fs(t,e,n))}};function cl(e,t,n,r,o,a,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!(t.prototype&&t.prototype.isPureReactComponent&&Nr(n,r)&&Nr(o,a))}function dl(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ul.enqueueReplaceState(t,t.state,null)}function fl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=ce({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var pl="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function ml(e){pl(e)}function gl(e){}function hl(e){pl(e)}function vl(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function bl(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function yl(e,t,n){return(n=cs(n)).tag=3,n.payload={element:null},n.callback=function(){vl(e,t)},n}function wl(e){return(e=cs(e)).tag=3,e}function kl(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"==typeof o){var a=r.value;e.payload=function(){return o(a)},e.callback=function(){bl(t,n,r)}}var i=n.stateNode;null!==i&&"function"==typeof i.componentDidCatch&&(e.callback=function(){bl(t,n,r),"function"!=typeof o&&(null===tc?tc=new Set([this]):tc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var xl=Error(I(461)),Sl=!1;function zl(e,t,n,r){t.child=null===e?na(t,null,n,r):ta(t,e.child,n,r)}function El(e,t,n,r,o){n=n.render;var a=t.ref;if("ref"in r){var i={};for(var l in r)"ref"!==l&&(i[l]=r[l])}else i=r;return rs(t),r=Qa(e,t,n,i,a,o),l=Ya(),null===e||Sl?(jo&&l&&Co(t),t.flags|=1,zl(e,t,r,o),t.child):(Ga(e,t,o),Ql(e,t,o))}function Cl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!=typeof a||gu(a)||void 0!==a.defaultProps||null!==n.compare?((e=bu(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Pl(e,t,a,r,o))}if(a=e.child,!Wl(e,o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:Nr)(i,r)&&e.ref===t.ref)return Ql(e,t,o)}return t.flags|=1,(e=hu(a,r)).ref=t.ref,e.return=t,t.child=e}function Pl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(Nr(a,r)&&e.ref===t.ref){if(Sl=!1,t.pendingProps=r=a,!Wl(e,o))return t.lanes=e.lanes,Ql(e,t,o);131072&e.flags&&(Sl=!0)}}return Ml(e,t,n,r,o)}function Nl(e,t,n){var r=t.pendingProps,o=r.children,a=!!(2&t.stateNode._pendingVisibility),i=null!==e?e.memoizedState:null;if(jl(e,t),"hidden"===r.mode||a){if(128&t.flags){if(r=null!==i?i.baseLanes|n:n,null!==e){for(o=t.child=e.child,a=0;null!==o;)a=a|o.lanes|o.childLanes,o=o.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return _l(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,_l(e,t,null!==i?i.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&ja(0,null!==i?i.cachePool:null),null!==i?aa(t,i):ia(),da(t)}else null!==i?(ja(0,i.cachePool),aa(t,i),fa(),t.memoizedState=null):(null!==e&&ja(0,null),ia(),fa());return zl(e,t,o,n),t.child}function _l(e,t,n,r){var o=_a();return o=null===o?null:{parent:ya._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&ja(0,null),ia(),da(t),null!==e&&ts(e,t,r,!0),null}function jl(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=2097664);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(I(284));null!==e&&e.ref===n||(t.flags|=2097664)}}function Ml(e,t,n,r,o){return rs(t),n=Qa(e,t,n,r,void 0,o),r=Ya(),null===e||Sl?(jo&&r&&Co(t),t.flags|=1,zl(e,t,n,o),t.child):(Ga(e,t,o),Ql(e,t,o))}function Ll(e,t,n,r,o,a){return rs(t),t.updateQueue=null,n=qa(t,r,n,o),Wa(e),r=Ya(),null===e||Sl?(jo&&r&&Co(t),t.flags|=1,zl(e,t,n,a),t.child):(Ga(e,t,a),Ql(e,t,a))}function Tl(e,t,n,r,o){if(rs(t),null===t.stateNode){var a=fo,i=n.contextType;"object"==typeof i&&null!==i&&(a=os(i)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=ul,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},ss(t),i=n.contextType,a.context="object"==typeof i&&null!==i?os(i):fo,a.state=t.memoizedState,"function"==typeof(i=n.getDerivedStateFromProps)&&(sl(t,n,i,r),a.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(i=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),i!==a.state&&ul.enqueueReplaceState(a,a.state,null),hs(t,r,a,o),gs(),a.state=t.memoizedState),"function"==typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var l=t.memoizedProps,s=fl(n,l);a.props=s;var u=a.context,c=n.contextType;i=fo,"object"==typeof c&&null!==c&&(i=os(c));var d=n.getDerivedStateFromProps;c="function"==typeof d||"function"==typeof a.getSnapshotBeforeUpdate,l=t.pendingProps!==l,c||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(l||u!==i)&&dl(t,a,r,i),ls=!1;var f=t.memoizedState;a.state=f,hs(t,r,a,o),gs(),u=t.memoizedState,l||f!==u||ls?("function"==typeof d&&(sl(t,n,d,r),u=t.memoizedState),(s=ls||cl(t,n,s,r,f,u,i))?(c||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4194308)):("function"==typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),a.props=r,a.state=u,a.context=i,r=s):("function"==typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,us(e,t),c=fl(n,i=t.memoizedProps),a.props=c,d=t.pendingProps,f=a.context,u=n.contextType,s=fo,"object"==typeof u&&null!==u&&(s=os(u)),(u="function"==typeof(l=n.getDerivedStateFromProps)||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(i!==d||f!==s)&&dl(t,a,r,s),ls=!1,f=t.memoizedState,a.state=f,hs(t,r,a,o),gs();var p=t.memoizedState;i!==d||f!==p||ls||null!==e&&null!==e.dependencies&&ns(e.dependencies)?("function"==typeof l&&(sl(t,n,l,r),p=t.memoizedState),(c=ls||cl(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&ns(e.dependencies))?(u||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,s),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,s)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof a.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=s,r=c):("function"!=typeof a.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,jl(e,t),r=!!(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=ta(t,e.child,null,o),t.child=ta(t,null,n,o)):zl(e,t,n,o),t.memoizedState=a.state,e=t.child):e=Ql(e,t,o),e}function Al(e,t,n,r){return Ro(),t.flags|=256,zl(e,t,n,r),t.child}var Ol={dehydrated:null,treeContext:null,retryLane:0};function Dl(e){return{baseLanes:e,cachePool:Ma()}}function Fl(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=qu),e}function Rl(e,t,n){var r,o=t.pendingProps,a=!1,i=!!(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&!!(2&ma.current)),r&&(a=!0,t.flags&=-129),r=!!(32&t.flags),t.flags&=-33,null===e){if(jo){if(a?ca(t):fa(),jo){var l,s=_o;if(l=s){e:{for(l=s,s=Lo;8!==l.nodeType;){if(!s){s=null;break e}if(null===(l=Bd(l.nextSibling))){s=null;break e}}s=l}null!==s?(t.memoizedState={dehydrated:s,treeContext:null!==ko?{id:xo,overflow:So}:null,retryLane:536870912},(l=mu(18,null,null,0)).stateNode=s,l.return=t,t.child=l,No=t,_o=null,l=!0):l=!1}l||Ao(t)}if(null!==(s=t.memoizedState)&&null!==(s=s.dehydrated))return"$!"===s.data?t.lanes=16:t.lanes=536870912,null;pa(t)}return s=o.children,o=o.fallback,a?(fa(),s=Bl({mode:"hidden",children:s},a=t.mode),o=yu(o,a,n,null),s.return=t,o.return=t,s.sibling=o,t.child=s,(a=t.child).memoizedState=Dl(n),a.childLanes=Fl(e,r,n),t.memoizedState=Ol,o):(ca(t),Il(t,s))}if(null!==(l=e.memoizedState)&&null!==(s=l.dehydrated)){if(i)256&t.flags?(ca(t),t.flags&=-257,t=Ul(e,t,n)):null!==t.memoizedState?(fa(),t.child=e.child,t.flags|=128,t=null):(fa(),a=o.fallback,s=t.mode,o=Bl({mode:"visible",children:o.children},s),(a=yu(a,s,n,null)).flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,ta(t,e.child,null,n),(o=t.child).memoizedState=Dl(n),o.childLanes=Fl(e,r,n),t.memoizedState=Ol,t=a);else if(ca(t),"$!"===s.data){if(r=s.nextSibling&&s.nextSibling.dataset)var u=r.dgst;r=u,(o=Error(I(419))).stack="",o.digest=r,Io({value:o,source:null,stack:null}),t=Ul(e,t,n)}else if(Sl||ts(e,t,n,!1),r=0!==(n&e.childLanes),Sl||r){if(null!==(r=Au)){if(42&(o=n&-n))o=1;else switch(o){case 2:o=1;break;case 8:o=4;break;case 32:o=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:o=64;break;case 268435456:o=134217728;break;default:o=0}if(0!==(o=0!==(o&(r.suspendedLanes|n))?0:o)&&o!==l.retryLane)throw l.retryLane=o,so(e,o),dc(r,0,o),xl}"$?"===s.data||Sc(),t=Ul(e,t,n)}else"$?"===s.data?(t.flags|=128,t.child=e.child,t=Bc.bind(null,e),s._reactRetry=t,t=null):(e=l.treeContext,_o=Bd(s.nextSibling),No=t,jo=!0,Mo=null,Lo=!1,null!==e&&(yo[wo++]=xo,yo[wo++]=So,yo[wo++]=ko,xo=e.id,So=e.overflow,ko=t),(t=Il(t,o.children)).flags|=4096);return t}return a?(fa(),a=o.fallback,s=t.mode,u=(l=e.child).sibling,(o=hu(l,{mode:"hidden",children:o.children})).subtreeFlags=31457280&l.subtreeFlags,null!==u?a=hu(u,a):(a=yu(a,s,n,null)).flags|=2,a.return=t,o.return=t,o.sibling=a,t.child=o,o=a,a=t.child,null===(s=e.child.memoizedState)?s=Dl(n):(null!==(l=s.cachePool)?(u=ya._currentValue,l=l.parent!==u?{parent:u,pool:u}:l):l=Ma(),s={baseLanes:s.baseLanes|n,cachePool:l}),a.memoizedState=s,a.childLanes=Fl(e,r,n),t.memoizedState=Ol,o):(ca(t),e=(n=e.child).sibling,(n=hu(n,{mode:"visible",children:o.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Il(e,t){return(t=Bl({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Bl(e,t){return wu(e,t,0,null)}function Ul(e,t,n){return ta(t,e.child,null,n),(e=Il(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Zl(e.return,t,n)}function Hl(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function $l(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(zl(e,t,r.children,n),2&(r=ma.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vl(e,n,t);else if(19===e.tag)Vl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(Pe(ma,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ga(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Hl(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ga(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Hl(t,!0,n,null,a);break;case"together":Hl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ql(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),$u|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(ts(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(I(153));if(null!==t.child){for(n=hu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=hu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Wl(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!ns(e))}function ql(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Sl=!0;else{if(!(Wl(e,n)||128&t.flags))return Sl=!1,function(e,t,n){switch(t.tag){case 3:Le(t,t.stateNode.containerInfo),Xl(0,ya,e.memoizedState.cache),Ro();break;case 27:case 5:Ae(t);break;case 4:Le(t,t.stateNode.containerInfo);break;case 10:Xl(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(ca(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Rl(e,t,n):(ca(t),null!==(e=Ql(e,t,n))?e.sibling:null);ca(t);break;case 19:var o=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(ts(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return $l(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Pe(ma,ma.current),r)break;return null;case 22:case 23:return t.lanes=0,Nl(e,t,n);case 24:Xl(0,ya,e.memoizedState.cache)}return Ql(e,t,n)}(e,t,n);Sl=!!(131072&e.flags)}else Sl=!1,jo&&1048576&t.flags&&Eo(t,bo,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,"function"!=typeof r){if(null!=r){if((o=r.$$typeof)===G){t.tag=11,t=El(null,t,r,e,n);break e}if(o===Z){t.tag=14,t=Cl(null,t,r,e,n);break e}}throw t=ie(r)||r,Error(I(306,t,""))}gu(r)?(e=fl(r,e),t.tag=1,t=Tl(null,t,r,e,n)):(t.tag=0,t=Ml(null,t,r,e,n))}return t;case 0:return Ml(e,t,t.type,t.pendingProps,n);case 1:return Tl(e,t,r=t.type,o=fl(r,t.pendingProps),n);case 3:e:{if(Le(t,t.stateNode.containerInfo),null===e)throw Error(I(387));var a=t.pendingProps;r=(o=t.memoizedState).element,us(e,t),hs(t,a,null,n);var i=t.memoizedState;if(a=i.cache,Xl(0,ya,a),a!==o.cache&&es(t,[ya],n,!0),gs(),a=i.element,o.isDehydrated){if(o={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Al(e,t,a,n);break e}if(a!==r){Io(r=mo(Error(I(424)),t)),t=Al(e,t,a,n);break e}for(_o=Bd(t.stateNode.containerInfo.firstChild),No=t,jo=!0,Mo=null,Lo=!0,n=na(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(Ro(),a===r){t=Ql(e,t,n);break e}zl(e,t,a,n)}t=t.child}return t;case 26:return jl(e,t),null===e?(n=Yd(t.type,null,t.pendingProps,null))?t.memoizedState=n:jo||(n=t.type,e=t.pendingProps,(r=Nd(je.current).createElement(n))[vt]=t,r[bt]=e,Ed(r,n,e),jt(r),t.stateNode=r):t.memoizedState=Yd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ae(t),null===e&&jo&&(r=t.stateNode=Vd(t.type,t.pendingProps,je.current),No=t,Lo=!0,_o=Bd(r.firstChild)),r=t.pendingProps.children,null!==e||jo?zl(e,t,r,n):t.child=ta(t,null,r,n),jl(e,t),t.child;case 5:return null===e&&jo&&((o=r=_o)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[zt])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==o.rel||e.getAttribute("href")!==(null==o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===a)return e}if(null===(e=Bd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,Lo))?(t.stateNode=r,No=t,_o=Bd(r.firstChild),Lo=!1,o=!0):o=!1),o||Ao(t)),Ae(t),o=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,r=a.children,Md(o,a)?r=null:null!==i&&Md(o,i)&&(t.flags|=32),null!==t.memoizedState&&(o=Qa(e,t,Ka,null,null,n),hf._currentValue=o),jl(e,t),zl(e,t,r,n),t.child;case 6:return null===e&&jo&&((e=n=_o)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=Bd(e.nextSibling)))return null}return e}(n,t.pendingProps,Lo))?(t.stateNode=n,No=t,_o=null,e=!0):e=!1),e||Ao(t)),null;case 13:return Rl(e,t,n);case 4:return Le(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ta(t,null,r,n):zl(e,t,r,n),t.child;case 11:return El(e,t,t.type,t.pendingProps,n);case 7:return zl(e,t,t.pendingProps,n),t.child;case 8:case 12:return zl(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,Xl(0,t.type,r.value),zl(e,t,r.children,n),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,rs(t),r=r(o=os(o)),t.flags|=1,zl(e,t,r,n),t.child;case 14:return Cl(e,t,t.type,t.pendingProps,n);case 15:return Pl(e,t,t.type,t.pendingProps,n);case 19:return $l(e,t,n);case 22:return Nl(e,t,n);case 24:return rs(t),r=os(ya),null===e?(null===(o=_a())&&(o=Au,a=wa(),o.pooledCache=a,a.refCount++,null!==a&&(o.pooledCacheLanes|=n),o=a),t.memoizedState={parent:r,cache:o},ss(t),Xl(0,ya,o)):(0!==(e.lanes&n)&&(us(e,t),hs(t,null,null,n),gs()),o=e.memoizedState,a=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=o),Xl(0,ya,r)):(r=a.cache,Xl(0,ya,r),r!==o.cache&&es(t,[ya],n,!0))),zl(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(I(156,t.tag))}var Kl=Ee(null),Yl=null,Gl=null;function Xl(e,t,n){Pe(Kl,t._currentValue),t._currentValue=n}function Jl(e){e._currentValue=Kl.current,Ce(Kl)}function Zl(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function es(e,t,n,r){var o=e.child;for(null!==o&&(o.return=e);null!==o;){var a=o.dependencies;if(null!==a){var i=o.child;a=a.firstContext;e:for(;null!==a;){var l=a;a=o;for(var s=0;s<t.length;s++)if(l.context===t[s]){a.lanes|=n,null!==(l=a.alternate)&&(l.lanes|=n),Zl(a.return,n,e),r||(i=null);break e}a=l.next}}else if(18===o.tag){if(null===(i=o.return))throw Error(I(341));i.lanes|=n,null!==(a=i.alternate)&&(a.lanes|=n),Zl(i,n,e),i=null}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===e){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}}function ts(e,t,n,r){e=null;for(var o=t,a=!1;null!==o;){if(!a)if(524288&o.flags)a=!0;else if(262144&o.flags)break;if(10===o.tag){var i=o.alternate;if(null===i)throw Error(I(387));if(null!==(i=i.memoizedProps)){var l=o.type;Pr(o.pendingProps.value,i.value)||(null!==e?e.push(l):e=[l])}}else if(o===Me.current){if(null===(i=o.alternate))throw Error(I(387));i.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(null!==e?e.push(hf):e=[hf])}o=o.return}null!==e&&es(t,e,n,r),t.flags|=262144}function ns(e){for(e=e.firstContext;null!==e;){if(!Pr(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function rs(e){Yl=e,Gl=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function os(e){return is(Yl,e)}function as(e,t){return null===Yl&&rs(e),is(e,t)}function is(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===Gl){if(null===e)throw Error(I(308));Gl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Gl=Gl.next=t;return n}var ls=!1;function ss(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function us(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function cs(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ds(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&Tu){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=co(e),uo(e,null,n),t}return io(e,r,t,n),co(e)}function fs(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194176&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,pt(e,n)}}function ps(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ms=!1;function gs(){if(ms&&null!==Ea)throw Ea}function hs(e,t,n,r){ms=!1;var o=e.updateQueue;ls=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,u=s.next;s.next=null,null===i?a=u:i.next=u,i=s;var c=e.alternate;null!==c&&(l=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s)}if(null!==a){var d=o.baseState;for(i=0,c=u=s=null,l=a;;){var f=-536870913&l.lane,p=f!==l.lane;if(p?(Du&f)===f:(r&f)===f){0!==f&&f===za&&(ms=!0),null!==c&&(c=c.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var m=e,g=l;f=t;var h=n;switch(g.tag){case 1:if("function"==typeof(m=g.payload)){d=m.call(h,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(f="function"==typeof(m=g.payload)?m.call(h,d,f):m))break e;d=ce({},d,f);break e;case 2:ls=!0}}null!==(f=l.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=o.callbacks)?o.callbacks=[f]:p.push(f))}else p={lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=f;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(p=l).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}null===c&&(s=d),o.baseState=s,o.firstBaseUpdate=u,o.lastBaseUpdate=c,null===a&&(o.shared.lanes=0),$u|=i,e.lanes=i,e.memoizedState=d}}function vs(e,t){if("function"!=typeof e)throw Error(I(191,e));e.call(t)}function bs(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)vs(n[e],t)}function ys(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var a=n.create,i=n.inst;r=a(),i.destroy=r}n=n.next}while(n!==o)}}catch(l){Dc(t,t.return,l)}}function ws(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var a=o.next;r=a;do{if((r.tag&e)===e){var i=r.inst,l=i.destroy;if(void 0!==l){i.destroy=void 0,o=t;var s=n;try{l()}catch(u){Dc(o,s,u)}}}r=r.next}while(r!==a)}}catch(u){Dc(t,t.return,u)}}function ks(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{bs(t,n)}catch(r){Dc(e,e.return,r)}}}function xs(e,t,n){n.props=fl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){Dc(e,t,r)}}function Ss(e,t){try{var n=e.ref;if(null!==n){var r=e.stateNode;switch(e.tag){case 26:case 27:case 5:var o=r;break;default:o=r}"function"==typeof n?e.refCleanup=n(o):n.current=o}}catch(a){Dc(e,t,a)}}function zs(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(o){Dc(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(a){Dc(e,t,a)}else n.current=null}function Es(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){Dc(e,e.return,o)}}function Cs(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,a=null,i=null,l=null,s=null,u=null,c=null;for(p in n){var d=n[p];if(n.hasOwnProperty(p)&&null!=d)switch(p){case"checked":case"value":break;case"defaultValue":s=d;default:r.hasOwnProperty(p)||Sd(e,t,p,null,r,d)}}for(var f in r){var p=r[f];if(d=n[f],r.hasOwnProperty(f)&&(null!=p||null!=d))switch(f){case"type":a=p;break;case"name":o=p;break;case"checked":u=p;break;case"defaultChecked":c=p;break;case"value":i=p;break;case"defaultValue":l=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(I(137,t));break;default:p!==d&&Sd(e,t,f,p,r,d)}}return void Yt(e,i,l,s,u,c,a,o);case"select":for(a in p=i=l=f=null,n)if(s=n[a],n.hasOwnProperty(a)&&null!=s)switch(a){case"value":break;case"multiple":p=s;default:r.hasOwnProperty(a)||Sd(e,t,a,null,r,s)}for(o in r)if(a=r[o],s=n[o],r.hasOwnProperty(o)&&(null!=a||null!=s))switch(o){case"value":f=a;break;case"defaultValue":l=a;break;case"multiple":i=a;default:a!==s&&Sd(e,t,o,a,r,s)}return t=l,n=i,r=p,void(null!=f?Jt(e,!!n,f,!1):!!r!=!!n&&(null!=t?Jt(e,!!n,t,!0):Jt(e,!!n,n?[]:"",!1)));case"textarea":for(l in p=f=null,n)if(o=n[l],n.hasOwnProperty(l)&&null!=o&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Sd(e,t,l,null,r,o)}for(i in r)if(o=r[i],a=n[i],r.hasOwnProperty(i)&&(null!=o||null!=a))switch(i){case"value":f=o;break;case"defaultValue":p=o;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(I(91));break;default:o!==a&&Sd(e,t,i,o,r,a)}return void Zt(e,f,p);case"option":for(var m in n)f=n[m],n.hasOwnProperty(m)&&null!=f&&!r.hasOwnProperty(m)&&("selected"===m?e.selected=!1:Sd(e,t,m,null,r,f));for(s in r)f=r[s],p=n[s],!r.hasOwnProperty(s)||f===p||null==f&&null==p||("selected"===s?e.selected=f&&"function"!=typeof f&&"symbol"!=typeof f:Sd(e,t,s,f,r,p));return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)f=n[g],n.hasOwnProperty(g)&&null!=f&&!r.hasOwnProperty(g)&&Sd(e,t,g,null,r,f);for(u in r)if(f=r[u],p=n[u],r.hasOwnProperty(u)&&f!==p&&(null!=f||null!=p))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(I(137,t));break;default:Sd(e,t,u,f,r,p)}return;default:if(an(t)){for(var h in n)f=n[h],n.hasOwnProperty(h)&&void 0!==f&&!r.hasOwnProperty(h)&&zd(e,t,h,void 0,r,f);for(c in r)f=r[c],p=n[c],!r.hasOwnProperty(c)||f===p||void 0===f&&void 0===p||zd(e,t,c,f,r,p);return}}for(var v in n)f=n[v],n.hasOwnProperty(v)&&null!=f&&!r.hasOwnProperty(v)&&Sd(e,t,v,null,r,f);for(d in r)f=r[d],p=n[d],!r.hasOwnProperty(d)||f===p||null==f&&null==p||Sd(e,t,d,f,r,p)}(r,e.type,n,t),r[bt]=t}catch(o){Dc(e,e.return,o)}}function Ps(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag||4===e.tag}function Ns(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||Ps(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&27!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function _s(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=xd));else if(4!==r&&27!==r&&null!==(e=e.child))for(_s(e,t,n),e=e.sibling;null!==e;)_s(e,t,n),e=e.sibling}function js(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&27!==r&&null!==(e=e.child))for(js(e,t,n),e=e.sibling;null!==e;)js(e,t,n),e=e.sibling}var Ms=!1,Ls=!1,Ts=!1,As="function"==typeof WeakSet?WeakSet:Set,Os=null,Ds=!1;function Fs(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Gs(e,n),4&r&&ys(5,n);break;case 1:if(Gs(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(l){Dc(n,n.return,l)}else{var o=fl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){Dc(n,n.return,s)}}64&r&&ks(n),512&r&&Ss(n,n.return);break;case 3:if(Gs(e,n),64&r&&null!==(r=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:e=n.child.stateNode}try{bs(r,e)}catch(l){Dc(n,n.return,l)}}break;case 26:Gs(e,n),512&r&&Ss(n,n.return);break;case 27:case 5:Gs(e,n),null===t&&4&r&&Es(n),512&r&&Ss(n,n.return);break;case 12:default:Gs(e,n);break;case 13:Gs(e,n),4&r&&Hs(e,n);break;case 22:if(!(o=null!==n.memoizedState||Ms)){t=null!==t&&null!==t.memoizedState||Ls;var a=Ms,i=Ls;Ms=o,(Ls=t)&&!i?Js(e,n,!!(8772&n.subtreeFlags)):Gs(e,n),Ms=a,Ls=i}512&r&&("manual"===n.memoizedProps.mode?Ss(n,n.return):zs(n,n.return))}}function Rs(e){var t=e.alternate;null!==t&&(e.alternate=null,Rs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&Et(t),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Is=null,Bs=!1;function Us(e,t,n){for(n=n.child;null!==n;)Vs(e,t,n),n=n.sibling}function Vs(e,t,n){if(Xe&&"function"==typeof Xe.onCommitFiberUnmount)try{Xe.onCommitFiberUnmount(Ge,n)}catch(i){}switch(n.tag){case 26:Ls||zs(n,t),Us(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:Ls||zs(n,t);var r=Is,o=Bs;for(Is=n.stateNode,Us(e,t,n),t=(n=n.stateNode).attributes;t.length;)n.removeAttributeNode(t[0]);Et(n),Is=r,Bs=o;break;case 5:Ls||zs(n,t);case 6:o=Is;var a=Bs;if(Is=null,Us(e,t,n),Bs=a,null!==(Is=o))if(Bs)try{e=Is,r=n.stateNode,8===e.nodeType?e.parentNode.removeChild(r):e.removeChild(r)}catch(l){Dc(n,t,l)}else try{Is.removeChild(n.stateNode)}catch(l){Dc(n,t,l)}break;case 18:null!==Is&&(Bs?(t=Is,n=n.stateNode,8===t.nodeType?Rd(t.parentNode,n):1===t.nodeType&&Rd(t,n),Yf(t)):Rd(Is,n.stateNode));break;case 4:r=Is,o=Bs,Is=n.stateNode.containerInfo,Bs=!0,Us(e,t,n),Is=r,Bs=o;break;case 0:case 11:case 14:case 15:Ls||ws(2,n,t),Ls||ws(4,n,t),Us(e,t,n);break;case 1:Ls||(zs(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&xs(n,t,r)),Us(e,t,n);break;case 21:Us(e,t,n);break;case 22:Ls||zs(n,t),Ls=(r=Ls)||null!==n.memoizedState,Us(e,t,n),Ls=r;break;default:Us(e,t,n)}}function Hs(e,t){if(null===t.memoizedState&&null!==(e=t.alternate)&&null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))try{Yf(e)}catch(n){Dc(t,t.return,n)}}function $s(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new As),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new As),t;default:throw Error(I(435,e.tag))}}(e);t.forEach(function(t){var r=Uc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Qs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r],a=e,i=t,l=i;e:for(;null!==l;){switch(l.tag){case 27:case 5:Is=l.stateNode,Bs=!1;break e;case 3:case 4:Is=l.stateNode.containerInfo,Bs=!0;break e}l=l.return}if(null===Is)throw Error(I(160));Vs(a,i,o),Is=null,Bs=!1,null!==(a=o.alternate)&&(a.return=null),o.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)qs(t,e),t=t.sibling}var Ws=null;function qs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Qs(t,e),Ks(e),4&r&&(ws(3,e,e.return),ys(3,e),ws(5,e,e.return));break;case 1:Qs(t,e),Ks(e),512&r&&(Ls||null===n||zs(n,n.return)),64&r&&Ms&&null!==(e=e.updateQueue)&&null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r));break;case 26:var o=Ws;if(Qs(t,e),Ks(e),512&r&&(Ls||null===n||zs(n,n.return)),4&r){var a=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":(!(a=o.getElementsByTagName("title")[0])||a[zt]||a[vt]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=o.createElement(r),o.head.insertBefore(a,o.querySelector("head > title"))),Ed(a,r,n),a[vt]=e,jt(a),r=a;break e;case"link":var i=lf("link","href",o).get(r+(n.href||""));if(i)for(var l=0;l<i.length;l++)if((a=i[l]).getAttribute("href")===(null==n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(l,1);break t}Ed(a=o.createElement(r),r,n),o.head.appendChild(a);break;case"meta":if(i=lf("meta","content",o).get(r+(n.content||"")))for(l=0;l<i.length;l++)if((a=i[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(l,1);break t}Ed(a=o.createElement(r),r,n),o.head.appendChild(a);break;default:throw Error(I(468,r))}a[vt]=e,jt(a),r=a}e.stateNode=r}else sf(o,e.type,e.stateNode);else e.stateNode=tf(o,r,e.memoizedProps);else a!==r?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===r?sf(o,e.type,e.stateNode):tf(o,r,e.memoizedProps)):null===r&&null!==e.stateNode&&Cs(e,e.memoizedProps,n.memoizedProps)}break;case 27:if(4&r&&null===e.alternate){o=e.stateNode,a=e.memoizedProps;try{for(var s=o.firstChild;s;){var u=s.nextSibling,c=s.nodeName;s[zt]||"HEAD"===c||"BODY"===c||"SCRIPT"===c||"STYLE"===c||"LINK"===c&&"stylesheet"===s.rel.toLowerCase()||o.removeChild(s),s=u}for(var d=e.type,f=o.attributes;f.length;)o.removeAttributeNode(f[0]);Ed(o,d,a),o[vt]=e,o[bt]=a}catch(m){Dc(e,e.return,m)}}case 5:if(Qs(t,e),Ks(e),512&r&&(Ls||null===n||zs(n,n.return)),32&e.flags){o=e.stateNode;try{tn(o,"")}catch(m){Dc(e,e.return,m)}}4&r&&null!=e.stateNode&&Cs(e,o=e.memoizedProps,null!==n?n.memoizedProps:o),1024&r&&(Ts=!0);break;case 6:if(Qs(t,e),Ks(e),4&r){if(null===e.stateNode)throw Error(I(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(m){Dc(e,e.return,m)}}break;case 3:if(af=null,o=Ws,Ws=Qd(t.containerInfo),Qs(t,e),Ws=o,Ks(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Yf(t.containerInfo)}catch(m){Dc(e,e.return,m)}Ts&&(Ts=!1,Ys(e));break;case 4:r=Ws,Ws=Qd(e.stateNode.containerInfo),Qs(t,e),Ks(e),Ws=r;break;case 12:Qs(t,e),Ks(e);break;case 13:Qs(t,e),Ks(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(Ju=Ue()),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,$s(e,r));break;case 22:if(512&r&&(Ls||null===n||zs(n,n.return)),s=null!==e.memoizedState,u=null!==n&&null!==n.memoizedState,Ms=(c=Ms)||s,Ls=(d=Ls)||u,Qs(t,e),Ls=d,Ms=c,Ks(e),(t=e.stateNode)._current=e,t._visibility&=-3,t._visibility|=2&t._pendingVisibility,8192&r&&(t._visibility=s?-2&t._visibility:1|t._visibility,s&&(t=Ms||Ls,null===n||u||t||Xs(e)),null===e.memoizedProps||"manual"!==e.memoizedProps.mode))e:for(n=null,t=e;;){if(5===t.tag||26===t.tag||27===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,s)"function"==typeof(a=o.style).setProperty?a.setProperty("display","none","important"):a.display="none";else{i=u.stateNode;var p=null!=(l=u.memoizedProps.style)&&l.hasOwnProperty("display")?l.display:null;i.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(m){Dc(u,u.return,m)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=s?"":u.memoizedProps}catch(m){Dc(u,u.return,m)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&null!==(r=e.updateQueue)&&null!==(n=r.retryQueue)&&(r.retryQueue=null,$s(e,n));break;case 19:Qs(t,e),Ks(e),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,$s(e,r));break;case 21:break;default:Qs(t,e),Ks(e)}}function Ks(e){var t=e.flags;if(2&t){try{if(27!==e.tag){e:{for(var n=e.return;null!==n;){if(Ps(n)){var r=n;break e}n=n.return}throw Error(I(160))}switch(r.tag){case 27:var o=r.stateNode;js(e,Ns(e),o);break;case 5:var a=r.stateNode;32&r.flags&&(tn(a,""),r.flags&=-33),js(e,Ns(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;_s(e,Ns(e),i);break;default:throw Error(I(161))}}}catch(l){Dc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ys(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Ys(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Gs(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Fs(e,t.alternate,t),t=t.sibling}function Xs(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ws(4,t,t.return),Xs(t);break;case 1:zs(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&xs(t,t.return,n),Xs(t);break;case 26:case 27:case 5:zs(t,t.return),Xs(t);break;case 22:zs(t,t.return),null===t.memoizedState&&Xs(t);break;default:Xs(t)}e=e.sibling}}function Js(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,a=t,i=a.flags;switch(a.tag){case 0:case 11:case 15:Js(o,a,n),ys(4,a);break;case 1:if(Js(o,a,n),"function"==typeof(o=(r=a).stateNode).componentDidMount)try{o.componentDidMount()}catch(u){Dc(r,r.return,u)}if(null!==(o=(r=a).updateQueue)){var l=r.stateNode;try{var s=o.shared.hiddenCallbacks;if(null!==s)for(o.shared.hiddenCallbacks=null,o=0;o<s.length;o++)vs(s[o],l)}catch(u){Dc(r,r.return,u)}}n&&64&i&&ks(a),Ss(a,a.return);break;case 26:case 27:case 5:Js(o,a,n),n&&null===r&&4&i&&Es(a),Ss(a,a.return);break;case 12:default:Js(o,a,n);break;case 13:Js(o,a,n),n&&4&i&&Hs(o,a);break;case 22:null===a.memoizedState&&Js(o,a,n),Ss(a,a.return)}t=t.sibling}}function Zs(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&ka(n))}function eu(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&ka(e))}function tu(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)nu(e,t,n,r),t=t.sibling}function nu(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:tu(e,t,n,r),2048&o&&ys(9,t);break;case 3:tu(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&ka(e)));break;case 12:if(2048&o){tu(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,i=a.id,l=a.onPostCommit;"function"==typeof l&&l(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){Dc(t,t.return,s)}}else tu(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,null!==t.memoizedState?4&a._visibility?tu(e,t,n,r):ou(e,t):4&a._visibility?tu(e,t,n,r):(a._visibility|=4,ru(e,t,n,r,!!(10256&t.subtreeFlags))),2048&o&&Zs(t.alternate,t);break;case 24:tu(e,t,n,r),2048&o&&eu(t.alternate,t);break;default:tu(e,t,n,r)}}function ru(e,t,n,r,o){for(o=o&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,i=t,l=n,s=r,u=i.flags;switch(i.tag){case 0:case 11:case 15:ru(a,i,l,s,o),ys(8,i);break;case 23:break;case 22:var c=i.stateNode;null!==i.memoizedState?4&c._visibility?ru(a,i,l,s,o):ou(a,i):(c._visibility|=4,ru(a,i,l,s,o)),o&&2048&u&&Zs(i.alternate,i);break;case 24:ru(a,i,l,s,o),o&&2048&u&&eu(i.alternate,i);break;default:ru(a,i,l,s,o)}t=t.sibling}}function ou(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:ou(n,r),2048&o&&Zs(r.alternate,r);break;case 24:ou(n,r),2048&o&&eu(r.alternate,r);break;default:ou(n,r)}t=t.sibling}}var au=8192;function iu(e){if(e.subtreeFlags&au)for(e=e.child;null!==e;)lu(e),e=e.sibling}function lu(e){switch(e.tag){case 26:iu(e),e.flags&au&&null!==e.memoizedState&&function(e,t,n){if(null===cf)throw Error(I(475));var r=cf;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var o=Gd(n.href),a=e.querySelector(Xd(o));if(a)return null!==(e=a._p)&&"object"==typeof e&&"function"==typeof e.then&&(r.count++,r=ff.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=a,void jt(a);a=e.ownerDocument||e,n=Jd(n),(o=Hd.get(o))&&rf(n,o),jt(a=a.createElement("link"));var i=a;i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),Ed(a,"link",n),t.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(r.count++,t=ff.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ws,e.memoizedState,e.memoizedProps);break;case 5:default:iu(e);break;case 3:case 4:var t=Ws;Ws=Qd(e.stateNode.containerInfo),iu(e),Ws=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=au,au=16777216,iu(e),au=t):iu(e))}}function su(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function uu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Os=r,fu(r,e)}su(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)cu(e),e=e.sibling}function cu(e){switch(e.tag){case 0:case 11:case 15:uu(e),2048&e.flags&&ws(9,e,e.return);break;case 3:case 12:default:uu(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&4&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-5,du(e)):uu(e)}}function du(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Os=r,fu(r,e)}su(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:ws(8,t,t.return),du(t);break;case 22:4&(n=t.stateNode)._visibility&&(n._visibility&=-5,du(t));break;default:du(t)}e=e.sibling}}function fu(e,t){for(;null!==Os;){var n=Os;switch(n.tag){case 0:case 11:case 15:ws(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:ka(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Os=r;else e:for(n=e;null!==Os;){var o=(r=Os).sibling,a=r.return;if(Rs(r),r===n){Os=null;break e}if(null!==o){o.return=a,Os=o;break e}Os=a}}}function pu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function mu(e,t,n,r){return new pu(e,t,n,r)}function gu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function hu(e,t){var n=e.alternate;return null===n?((n=mu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=31457280&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function vu(e,t){e.flags&=31457282;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function bu(e,t,n,r,o,a){var i=0;if(r=e,"function"==typeof e)gu(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,Ne.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case $:return yu(n.children,o,a,t);case Q:i=8,o|=24;break;case W:return(e=mu(12,n,t,2|o)).elementType=W,e.lanes=a,e;case X:return(e=mu(13,n,t,o)).elementType=X,e.lanes=a,e;case J:return(e=mu(19,n,t,o)).elementType=J,e.lanes=a,e;case te:return wu(n,o,a,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case q:case Y:i=10;break e;case K:i=9;break e;case G:i=11;break e;case Z:i=14;break e;case ee:i=16,r=null;break e}i=29,n=Error(I(130,null===e?"null":typeof e,"")),r=null}return(t=mu(i,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function yu(e,t,n,r){return(e=mu(7,e,r,t)).lanes=n,e}function wu(e,t,n,r){(e=mu(22,e,r,t)).elementType=te,e.lanes=n;var o={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var e=o._current;if(null===e)throw Error(I(456));if(!(2&o._pendingVisibility)){var t=so(e,2);null!==t&&(o._pendingVisibility|=2,dc(t,0,2))}},attach:function(){var e=o._current;if(null===e)throw Error(I(456));if(2&o._pendingVisibility){var t=so(e,2);null!==t&&(o._pendingVisibility&=-3,dc(t,0,2))}}};return e.stateNode=o,e}function ku(e,t,n){return(e=mu(6,e,null,t)).lanes=n,e}function xu(e,t,n){return(t=mu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Su(e){e.flags|=4}function zu(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!uf(t)){if(null!==(t=sa.current)&&((4194176&Du)===Du?null!==ua:(62914560&Du)!==Du&&!(536870912&Du)||t!==ua))throw Wo=Vo,Uo;e.flags|=8192}}function Eu(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?ut():536870912,e.lanes|=t,Ku|=t)}function Cu(e,t){if(!jo)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Pu(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=31457280&o.subtreeFlags,r|=31457280&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Nu(e,t,n){var r=t.pendingProps;switch(Po(t),t.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return Pu(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),Jl(ya),Te(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(Fo(t)?Su(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==Mo&&(pc(Mo),Mo=null))),Pu(t),null;case 26:return n=t.memoizedState,null===e?(Su(t),null!==n?(Pu(t),zu(t,n)):(Pu(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Su(t),Pu(t),zu(t,n)):(Pu(t),t.flags&=-16777217):(e.memoizedProps!==r&&Su(t),Pu(t),t.flags&=-16777217),null;case 27:Oe(t),n=je.current;var o=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Su(t);else{if(!r){if(null===t.stateNode)throw Error(I(166));return Pu(t),null}e=Ne.current,Fo(t)?Oo(t):(e=Vd(o,r,n),t.stateNode=e,Su(t))}return Pu(t),null;case 5:if(Oe(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Su(t);else{if(!r){if(null===t.stateNode)throw Error(I(166));return Pu(t),null}if(e=Ne.current,Fo(t))Oo(t);else{switch(o=Nd(je.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof r.is?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"==typeof r.is?o.createElement(n,{is:r.is}):o.createElement(n)}}e[vt]=t,e[bt]=r;e:for(o=t.child;null!==o;){if(5===o.tag||6===o.tag)e.appendChild(o.stateNode);else if(4!==o.tag&&27!==o.tag&&null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;null===o.sibling;){if(null===o.return||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(Ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Su(t)}}return Pu(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Su(t);else{if("string"!=typeof r&&null===t.stateNode)throw Error(I(166));if(e=je.current,Fo(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(o=No))switch(o.tag){case 27:case 5:r=o.memoizedProps}e[vt]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||kd(e.nodeValue,n)))||Ao(t)}else(e=Nd(e).createTextNode(r))[vt]=t,t.stateNode=e}return Pu(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(o=Fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(I(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(I(317));o[vt]=t}else Ro(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Pu(t),o=!1}else null!==Mo&&(pc(Mo),Mo=null),o=!0;if(!o)return 256&t.flags?(pa(t),t):(pa(t),null)}if(pa(t),128&t.flags)return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){o=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(o=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==o&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Eu(t,t.updateQueue),Pu(t),null;case 4:return Te(),null===e&&dd(t.stateNode.containerInfo),Pu(t),null;case 10:return Jl(t.type),Pu(t),null;case 19:if(Ce(ma),null===(o=t.memoizedState))return Pu(t),null;if(r=!!(128&t.flags),null===(a=o.rendering))if(r)Cu(o,!1);else{if(0!==Hu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(a=ga(e))){for(t.flags|=128,Cu(o,!1),e=a.updateQueue,t.updateQueue=e,Eu(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)vu(n,e),n=n.sibling;return Pe(ma,1&ma.current|2),t.child}e=e.sibling}null!==o.tail&&Ue()>Zu&&(t.flags|=128,r=!0,Cu(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ga(a))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,Eu(t,e),Cu(o,!0),null===o.tail&&"hidden"===o.tailMode&&!a.alternate&&!jo)return Pu(t),null}else 2*Ue()-o.renderingStartTime>Zu&&536870912!==n&&(t.flags|=128,r=!0,Cu(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=o.last)?e.sibling=a:t.child=a,o.last=a)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ue(),t.sibling=null,e=ma.current,Pe(ma,r?1&e|2:1&e),t):(Pu(t),null);case 22:case 23:return pa(t),la(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?!!(536870912&n)&&!(128&t.flags)&&(Pu(t),6&t.subtreeFlags&&(t.flags|=8192)):Pu(t),null!==(n=t.updateQueue)&&Eu(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&Ce(Na),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Jl(ya),Pu(t),null;case 25:return null}throw Error(I(156,t.tag))}function _u(e,t){switch(Po(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Jl(ya),Te(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Oe(t),null;case 13:if(pa(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(I(340));Ro()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ce(ma),null;case 4:return Te(),null;case 10:return Jl(t.type),null;case 22:case 23:return pa(t),la(),null!==e&&Ce(Na),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return Jl(ya),null;default:return null}}function ju(e,t){switch(Po(t),t.tag){case 3:Jl(ya),Te();break;case 26:case 27:case 5:Oe(t);break;case 4:Te();break;case 13:pa(t);break;case 19:Ce(ma);break;case 10:Jl(t.type);break;case 22:case 23:pa(t),la(),null!==e&&Ce(Na);break;case 24:Jl(ya)}}var Mu={getCacheForType:function(e){var t=os(ya),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},Lu="function"==typeof WeakMap?WeakMap:Map,Tu=0,Au=null,Ou=null,Du=0,Fu=0,Ru=null,Iu=!1,Bu=!1,Uu=!1,Vu=0,Hu=0,$u=0,Qu=0,Wu=0,qu=0,Ku=0,Yu=null,Gu=null,Xu=!1,Ju=0,Zu=1/0,ec=null,tc=null,nc=!1,rc=null,oc=0,ac=0,ic=null,lc=0,sc=null;function uc(){return 2&Tu&&0!==Du?Du&-Du:null!==ue.T?0!==za?za:ed():gt()}function cc(){0===qu&&(qu=536870912&Du&&!jo?536870912:st());var e=sa.current;return null!==e&&(e.flags|=32),qu}function dc(e,t,n){(e===Au&&2===Fu||null!==e.cancelPendingCommit)&&(yc(e,0),hc(e,Du,qu,!1)),dt(e,n),2&Tu&&e===Au||(e===Au&&(!(2&Tu)&&(Qu|=n),4===Hu&&hc(e,Du,qu,!1)),Kc(e))}function fc(e,t,n){if(6&Tu)throw Error(I(327));for(var r=!n&&!(60&t)&&0===(t&e.expiredLanes)||it(e,t),o=r?function(e,t){var n=Tu;Tu|=2;var r=kc(),o=xc();Au!==e||Du!==t?(ec=null,Zu=Ue()+500,yc(e,t)):Bu=it(e,t);e:for(;;)try{if(0!==Fu&&null!==Ou){t=Ou;var a=Ru;t:switch(Fu){case 1:Fu=0,Ru=null,_c(e,t,a,1);break;case 2:if(Ho(a)){Fu=0,Ru=null,Nc(t);break}t=function(){2===Fu&&Au===e&&(Fu=7),Kc(e)},a.then(t,t);break e;case 3:Fu=7;break e;case 4:Fu=5;break e;case 7:Ho(a)?(Fu=0,Ru=null,Nc(t)):(Fu=0,Ru=null,_c(e,t,a,7));break;case 5:var i=null;switch(Ou.tag){case 26:i=Ou.memoizedState;case 5:case 27:var l=Ou;if(!i||uf(i)){Fu=0,Ru=null;var s=l.sibling;if(null!==s)Ou=s;else{var u=l.return;null!==u?(Ou=u,jc(u)):Ou=null}break t}}Fu=0,Ru=null,_c(e,t,a,5);break;case 6:Fu=0,Ru=null,_c(e,t,a,6);break;case 8:bc(),Hu=6;break e;default:throw Error(I(462))}}Cc();break}catch(c){wc(e,c)}return Gl=Yl=null,ue.H=r,ue.A=o,Tu=n,null!==Ou?0:(Au=null,Du=0,ao(),Hu)}(e,t):zc(e,t,!0),a=r;;){if(0===o){Bu&&!r&&hc(e,t,0,!1);break}if(6===o)hc(e,t,0,!Iu);else{if(n=e.current.alternate,a&&!gc(n)){o=zc(e,t,!1),a=!1;continue}if(2===o){if(a=t,e.errorRecoveryDisabledLanes&a)var i=0;else i=0!=(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var l=e;o=Yu;var s=l.current.memoizedState.isDehydrated;if(s&&(yc(l,i).flags|=256),2!==(i=zc(l,i,!1))){if(Uu&&!s){l.errorRecoveryDisabledLanes|=a,Qu|=a,o=4;break e}a=Gu,Gu=o,null!==a&&pc(a)}o=i}if(a=!1,2!==o)continue}}if(1===o){yc(e,0),hc(e,t,0,!0);break}e:{switch(r=e,o){case 0:case 1:throw Error(I(345));case 4:if((4194176&t)===t){hc(r,t,qu,!Iu);break e}break;case 2:Gu=null;break;case 3:case 5:break;default:throw Error(I(329))}if(r.finishedWork=n,r.finishedLanes=t,(62914560&t)===t&&10<(a=Ju+300-Ue())){if(hc(r,t,qu,!Iu),0!==at(r,0))break e;r.timeoutHandle=Td(mc.bind(null,r,n,Gu,ec,Xu,t,qu,Qu,Ku,Iu,2,-0,0),a)}else mc(r,n,Gu,ec,Xu,t,qu,Qu,Ku,Iu,0,-0,0)}}break}Kc(e)}function pc(e){null===Gu?Gu=e:Gu.push.apply(Gu,e)}function mc(e,t,n,r,o,a,i,l,s,u,c,d,f){var p=t.subtreeFlags;if((8192&p||!(16785408&~p))&&(cf={stylesheets:null,count:0,unsuspend:df},lu(t),null!==(t=function(){if(null===cf)throw Error(I(475));var e=cf;return e.stylesheets&&0===e.count&&mf(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&mf(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=t(Lc.bind(null,e,n,r,o,i,l,s,1,d,f)),void hc(e,a,i,!u);Lc(e,n,r,o,i,l,s)}function gc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&null!==(n=t.updateQueue)&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!Pr(a(),o))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function hc(e,t,n,r){t&=~Wu,t&=~Qu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var a=31-Ze(o),i=1<<a;r[a]=-1,o&=~i}0!==n&&ft(e,n,t)}function vc(){return!!(6&Tu)||(Yc(0),!1)}function bc(){if(null!==Ou){if(0===Fu)var e=Ou.return;else Gl=Yl=null,Xa(e=Ou),Ko=null,Yo=0,e=Ou;for(;null!==e;)ju(e.alternate,e),e=e.return;Ou=null}}function yc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,Ad(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),bc(),Au=e,Ou=n=hu(e.current,null),Du=t,Fu=0,Ru=null,Iu=!1,Bu=it(e,t),Uu=!1,Ku=qu=Wu=Qu=$u=Hu=0,Gu=Yu=null,Xu=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-Ze(r),a=1<<o;t|=e[o],r&=~a}return Vu=t,ao(),n}function wc(e,t){Ta=null,ue.H=ol,t===Bo?(t=qo(),Fu=3):t===Uo?(t=qo(),Fu=4):Fu=t===xl?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,Ru=t,null===Ou&&(Hu=1,vl(e,mo(t,e.current)))}function kc(){var e=ue.H;return ue.H=ol,null===e?ol:e}function xc(){var e=ue.A;return ue.A=Mu,e}function Sc(){Hu=4,Iu||(4194176&Du)!==Du&&null!==sa.current||(Bu=!0),!(134217727&$u)&&!(134217727&Qu)||null===Au||hc(Au,Du,qu,!1)}function zc(e,t,n){var r=Tu;Tu|=2;var o=kc(),a=xc();Au===e&&Du===t||(ec=null,yc(e,t)),t=!1;var i=Hu;e:for(;;)try{if(0!==Fu&&null!==Ou){var l=Ou,s=Ru;switch(Fu){case 8:bc(),i=6;break e;case 3:case 2:case 6:null===sa.current&&(t=!0);var u=Fu;if(Fu=0,Ru=null,_c(e,l,s,u),n&&Bu){i=0;break e}break;default:u=Fu,Fu=0,Ru=null,_c(e,l,s,u)}}Ec(),i=Hu;break}catch(c){wc(e,c)}return t&&e.shellSuspendCounter++,Gl=Yl=null,Tu=r,ue.H=o,ue.A=a,null===Ou&&(Au=null,Du=0,ao()),i}function Ec(){for(;null!==Ou;)Pc(Ou)}function Cc(){for(;null!==Ou&&!Ie();)Pc(Ou)}function Pc(e){var t=ql(e.alternate,e,Vu);e.memoizedProps=e.pendingProps,null===t?jc(e):Ou=t}function Nc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ll(n,t,t.pendingProps,t.type,void 0,Du);break;case 11:t=Ll(n,t,t.pendingProps,t.type.render,t.ref,Du);break;case 5:Xa(t);default:ju(n,t),t=ql(n,t=Ou=vu(t,Vu),Vu)}e.memoizedProps=e.pendingProps,null===t?jc(e):Ou=t}function _c(e,t,n,r){Gl=Yl=null,Xa(t),Ko=null,Yo=0;var o=t.return;try{if(function(e,t,n,r,o){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(t=n.alternate)&&ts(t,n,o,!0),null!==(n=sa.current)){switch(n.tag){case 13:return null===ua?Sc():null===n.alternate&&0===Hu&&(Hu=3),n.flags&=-257,n.flags|=65536,n.lanes=o,r===Vo?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),Fc(e,r,o)),!1;case 22:return n.flags|=65536,r===Vo?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),Fc(e,r,o)),!1}throw Error(I(435,n.tag))}return Fc(e,r,o),Sc(),!1}if(jo)return null!==(t=sa.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==To&&Io(mo(e=Error(I(422),{cause:r}),n))):(r!==To&&Io(mo(t=Error(I(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,o&=-o,e.lanes|=o,r=mo(r,n),ps(e,o=yl(e.stateNode,r,o)),4!==Hu&&(Hu=2)),!1;var a=Error(I(520),{cause:r});if(a=mo(a,n),null===Yu?Yu=[a]:Yu.push(a),4!==Hu&&(Hu=2),null===t)return!0;r=mo(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,ps(n,e=yl(n.stateNode,r,e)),!1;case 1:if(t=n.type,a=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===a||"function"!=typeof a.componentDidCatch||null!==tc&&tc.has(a))))return n.flags|=65536,o&=-o,n.lanes|=o,kl(o=wl(o),e,n,r),ps(n,o),!1}n=n.return}while(null!==n);return!1}(e,o,t,n,Du))return Hu=1,vl(e,mo(n,e.current)),void(Ou=null)}catch(a){if(null!==o)throw Ou=o,a;return Hu=1,vl(e,mo(n,e.current)),void(Ou=null)}32768&t.flags?(jo||1===r?e=!0:Bu||536870912&Du?e=!1:(Iu=e=!0,(2===r||3===r||6===r)&&null!==(r=sa.current)&&13===r.tag&&(r.flags|=16384)),Mc(t,e)):jc(t)}function jc(e){var t=e;do{if(32768&t.flags)return void Mc(t,Iu);e=t.return;var n=Nu(t.alternate,t,Vu);if(null!==n)return void(Ou=n);if(null!==(t=t.sibling))return void(Ou=t);Ou=t=e}while(null!==t);0===Hu&&(Hu=5)}function Mc(e,t){do{var n=_u(e.alternate,e);if(null!==n)return n.flags&=32767,void(Ou=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(Ou=e);Ou=e=n}while(null!==e);Hu=6,Ou=null}function Lc(e,t,n,r,o,a,i,l,s,u){var c=ue.T,d=ke.p;try{ke.p=2,ue.T=null,function(e,t,n,r,o,a,i,l){do{Ac()}while(null!==rc);if(6&Tu)throw Error(I(327));var s=e.finishedWork;if(r=e.finishedLanes,null===s)return null;if(e.finishedWork=null,e.finishedLanes=0,s===e.current)throw Error(I(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var u=s.lanes|s.childLanes;if(function(e,t,n,r,o,a){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var l=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=i&~n;0<n;){var c=31-Ze(n),d=1<<c;l[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&ft(e,r,0),0!==a&&0===o&&0!==e.tag&&(e.suspendedLanes|=a&~(i&~t))}(e,r,u|=oo,a,i,l),e===Au&&(Ou=Au=null,Du=0),!(10256&s.subtreeFlags)&&!(10256&s.flags)||nc||(nc=!0,ac=u,ic=n,Fe(Qe,function(){return Ac(),null})),n=!!(15990&s.flags),15990&s.subtreeFlags||n?(n=ue.T,ue.T=null,a=ke.p,ke.p=2,i=Tu,Tu|=4,function(e,t){if(e=e.containerInfo,Cd=zf,Tr(e=Lr(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var i=0,l=-1,s=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var p;d!==n||0!==o&&3!==d.nodeType||(l=i+o),d!==a||0!==r&&3!==d.nodeType||(s=i+r),3===d.nodeType&&(i+=d.nodeValue.length),null!==(p=d.firstChild);)f=d,d=p;for(;;){if(d===e)break t;if(f===n&&++u===o&&(l=i),f===a&&++c===r&&(s=i),null!==(p=d.nextSibling))break;f=(d=f).parentNode}d=p}n=-1===l||-1===s?null:{start:l,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(Pd={focusedElem:e,selectionRange:n},zf=!1,Os=t;null!==Os;)if(e=(t=Os).child,1028&t.subtreeFlags&&null!==e)e.return=t,Os=e;else for(;null!==Os;){switch(a=(t=Os).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==a){e=void 0,n=t,o=a.memoizedProps,a=a.memoizedState,r=n.stateNode;try{var m=fl(n.type,o,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(h){Dc(n,n.return,h)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))Id(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Id(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(I(163))}if(null!==(e=t.sibling)){e.return=t.return,Os=e;break}Os=t.return}m=Ds,Ds=!1}(e,s),qs(s,e),Ar(Pd,e.containerInfo),zf=!!Cd,Pd=Cd=null,e.current=s,Fs(e,s.alternate,s),Be(),Tu=i,ke.p=a,ue.T=n):e.current=s,nc?(nc=!1,rc=e,oc=r):Tc(e,u),0===(u=e.pendingLanes)&&(tc=null),function(e){if(Xe&&"function"==typeof Xe.onCommitFiberRoot)try{Xe.onCommitFiberRoot(Ge,e,void 0,!(128&~e.current.flags))}catch(t){}}(s.stateNode),Kc(e),null!==t)for(o=e.onRecoverableError,s=0;s<t.length;s++)u=t[s],o(u.value,{componentStack:u.stack});!!(3&oc)&&Ac(),u=e.pendingLanes,4194218&r&&42&u?e===sc?lc++:(lc=0,sc=e):lc=0,Yc(0)}(e,t,n,r,d,o,a,i)}finally{ue.T=c,ke.p=d}}function Tc(e,t){0===(e.pooledCacheLanes&=t)&&null!=(t=e.pooledCache)&&(e.pooledCache=null,ka(t))}function Ac(){if(null!==rc){var e=rc,t=ac;ac=0;var n=mt(oc),r=ue.T,o=ke.p;try{if(ke.p=32>n?32:n,ue.T=null,null===rc)var a=!1;else{n=ic,ic=null;var i=rc,l=oc;if(rc=null,oc=0,6&Tu)throw Error(I(331));var s=Tu;if(Tu|=4,cu(i.current),nu(i,i.current,l,n),Tu=s,Yc(0),Xe&&"function"==typeof Xe.onPostCommitFiberRoot)try{Xe.onPostCommitFiberRoot(Ge,i)}catch(u){}a=!0}return a}finally{ke.p=o,ue.T=r,Tc(e,t)}}return!1}function Oc(e,t,n){t=mo(n,t),null!==(e=ds(e,t=yl(e.stateNode,t,2),2))&&(dt(e,2),Kc(e))}function Dc(e,t,n){if(3===e.tag)Oc(e,e,n);else for(;null!==t;){if(3===t.tag){Oc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===tc||!tc.has(r))){e=mo(n,e),null!==(r=ds(t,n=wl(2),2))&&(kl(n,r,t,e),dt(r,2),Kc(r));break}}t=t.return}}function Fc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new Lu;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(Uu=!0,o.add(n),e=Rc.bind(null,e,t,n),t.then(e,e))}function Rc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Au===e&&(Du&n)===n&&(4===Hu||3===Hu&&(62914560&Du)===Du&&300>Ue()-Ju?!(2&Tu)&&yc(e,0):Wu|=n,Ku===Du&&(Ku=0)),Kc(e)}function Ic(e,t){0===t&&(t=ut()),null!==(e=so(e,t))&&(dt(e,t),Kc(e))}function Bc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ic(e,n)}function Uc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(I(314))}null!==r&&r.delete(t),Ic(e,n)}var Vc=null,Hc=null,$c=!1,Qc=!1,Wc=!1,qc=0;function Kc(e){var t;e!==Hc&&null===e.next&&(null===Hc?Vc=Hc=e:Hc=Hc.next=e),Qc=!0,$c||($c=!0,t=Gc,Dd(function(){6&Tu?Fe(He,t):t()}))}function Yc(e,t){if(!Wc&&Qc){Wc=!0;do{for(var n=!1,r=Vc;null!==r;){if(0!==e){var o=r.pendingLanes;if(0===o)var a=0;else{var i=r.suspendedLanes,l=r.pingedLanes;a=(1<<31-Ze(42|e)+1)-1,a=201326677&(a&=o&~(i&~l))?201326677&a|1:a?2|a:0}0!==a&&(n=!0,Zc(r,a))}else a=Du,!(3&(a=at(r,r===Au?a:0)))||it(r,a)||(n=!0,Zc(r,a));r=r.next}}while(n);Wc=!1}}function Gc(){Qc=$c=!1;var e,t=0;0!==qc&&(((e=window.event)&&"popstate"===e.type?e!==Ld&&(Ld=e,!0):(Ld=null,!1))&&(t=qc),qc=0);for(var n=Ue(),r=null,o=Vc;null!==o;){var a=o.next,i=Xc(o,n);0===i?(o.next=null,null===r?Vc=a:r.next=a,null===a&&(Hc=r)):(r=o,(0!==t||3&i)&&(Qc=!0)),o=a}Yc(t)}function Xc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var i=31-Ze(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=lt(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}if(n=Du,n=at(e,e===(t=Au)?n:0),r=e.callbackNode,0===n||e===t&&2===Fu||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Re(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||it(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&Re(r),mt(n)){case 2:case 8:n=$e;break;case 32:default:n=Qe;break;case 268435456:n=qe}return r=Jc.bind(null,e),n=Fe(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&Re(r),e.callbackPriority=2,e.callbackNode=null,2}function Jc(e,t){var n=e.callbackNode;if(Ac()&&e.callbackNode!==n)return null;var r=Du;return 0===(r=at(e,e===Au?r:0))?null:(fc(e,r,t),Xc(e,Ue()),null!=e.callbackNode&&e.callbackNode===n?Jc.bind(null,e):null)}function Zc(e,t){if(Ac())return null;fc(e,t,!0)}function ed(){return 0===qc&&(qc=st()),qc}function td(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:un(""+e)}function nd(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var rd=0;rd<eo.length;rd++){var od=eo[rd];to(od.toLowerCase(),"on"+(od[0].toUpperCase()+od.slice(1)))}to(Wr,"onAnimationEnd"),to(qr,"onAnimationIteration"),to(Kr,"onAnimationStart"),to("dblclick","onDoubleClick"),to("focusin","onFocus"),to("focusout","onBlur"),to(Yr,"onTransitionRun"),to(Gr,"onTransitionStart"),to(Xr,"onTransitionCancel"),to(Jr,"onTransitionEnd"),At("onMouseEnter",["mouseout","mouseover"]),At("onMouseLeave",["mouseout","mouseover"]),At("onPointerEnter",["pointerout","pointerover"]),At("onPointerLeave",["pointerout","pointerover"]),Tt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Tt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Tt("onBeforeInput",["compositionend","keypress","textInput","paste"]),Tt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Tt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Tt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ad="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),id=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ad));function ld(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;a=l,o.currentTarget=u;try{a(o)}catch(c){pl(c)}o.currentTarget=null,a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,u=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;a=l,o.currentTarget=u;try{a(o)}catch(c){pl(c)}o.currentTarget=null,a=s}}}}function sd(e,t){var n=t[wt];void 0===n&&(n=t[wt]=new Set);var r=e+"__bubble";n.has(r)||(fd(t,e,2,!1),n.add(r))}function ud(e,t,n){var r=0;t&&(r|=4),fd(n,e,r,t)}var cd="_reactListening"+Math.random().toString(36).slice(2);function dd(e){if(!e[cd]){e[cd]=!0,Mt.forEach(function(t){"selectionchange"!==t&&(id.has(t)||ud(t,!1,e),ud(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[cd]||(t[cd]=!0,ud("selectionchange",!1,t))}}function fd(e,t,n,r){switch(Mf(t)){case 2:var o=Ef;break;case 8:o=Cf;break;default:o=Pf}n=o.bind(null,t,n,e),o=void 0,!bn||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function pd(e,t,n,r,o){var a=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=Ct(l)))return;if(5===(s=i.tag)||6===s||26===s||27===s){r=a=i;continue e}l=l.parentNode}}r=r.return}hn(function(){var r=a,o=dn(n),i=[];e:{var l=Zr.get(e);if(void 0!==l){var s=Ln,u=e;switch(e){case"keypress":if(0===zn(n))break e;case"keydown":case"keyup":s=qn;break;case"focusin":u="focus",s=Rn;break;case"focusout":u="blur",s=Rn;break;case"beforeblur":case"afterblur":s=Rn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=Dn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=Fn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Yn;break;case Wr:case qr:case Kr:s=In;break;case Jr:s=Gn;break;case"scroll":case"scrollend":s=An;break;case"wheel":s=Xn;break;case"copy":case"cut":case"paste":s=Bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Kn;break;case"toggle":case"beforetoggle":s=Jn}var c=!!(4&t),d=!c&&("scroll"===e||"scrollend"===e),f=c?null!==l?l+"Capture":null:l;c=[];for(var p,m=r;null!==m;){var g=m;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===f||null!=(g=vn(m,f))&&c.push(md(m,g,p)),d)break;m=m.return}0<c.length&&(l=new s(l,u,null,n,o),i.push({event:l,listeners:c}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===cn||!(u=n.relatedTarget||n.fromElement)||!Ct(u)&&!u[yt])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?Ct(u):null)&&(d=he(u),c=u.tag,u!==d||5!==c&&27!==c&&6!==c)&&(u=null)):(s=null,u=r),s!==u)){if(c=Dn,g="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(c=Kn,g="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?l:Nt(s),p=null==u?l:Nt(u),(l=new c(g,m+"leave",s,n,o)).target=d,l.relatedTarget=p,g=null,Ct(o)===r&&((c=new c(f,m+"enter",u,n,o)).target=p,c.relatedTarget=d,g=c),d=g,s&&u)e:{for(f=u,m=0,p=c=s;p;p=hd(p))m++;for(p=0,g=f;g;g=hd(g))p++;for(;0<m-p;)c=hd(c),m--;for(;0<p-m;)f=hd(f),p--;for(;m--;){if(c===f||null!==f&&c===f.alternate)break e;c=hd(c),f=hd(f)}c=null}else c=null;null!==s&&vd(i,l,s,c,!1),null!==u&&null!==d&&vd(i,d,u,c,!0)}if("select"===(s=(l=r?Nt(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var h=hr;else if(cr(l))if(vr)h=Cr;else{h=zr;var v=Sr}else!(s=l.nodeName)||"input"!==s.toLowerCase()||"checkbox"!==l.type&&"radio"!==l.type?r&&an(r.elementType)&&(h=hr):h=Er;switch(h&&(h=h(e,r))?dr(i,h,n,o):(v&&v(e,l,r),"focusout"===e&&r&&"number"===l.type&&null!=r.memoizedProps.value&&Xt(l,"number",l.value)),v=r?Nt(r):window,e){case"focusin":(cr(v)||"true"===v.contentEditable)&&(Dr=v,Fr=r,Rr=null);break;case"focusout":Rr=Fr=Dr=null;break;case"mousedown":Ir=!0;break;case"contextmenu":case"mouseup":case"dragend":Ir=!1,Br(i,n,o);break;case"selectionchange":if(Or)break;case"keydown":case"keyup":Br(i,n,o)}var b;if(er)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else sr?ir(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(rr&&"ko"!==n.locale&&(sr||"onCompositionStart"!==y?"onCompositionEnd"===y&&sr&&(b=Sn()):(kn="value"in(wn=o)?wn.value:wn.textContent,sr=!0)),0<(v=gd(r,y)).length&&(y=new Un(y,e,null,n,o),i.push({event:y,listeners:v}),(b||null!==(b=lr(n)))&&(y.data=b))),(b=nr?function(e,t){switch(e){case"compositionend":return lr(t);case"keypress":return 32!==t.which?null:(ar=!0,or);case"textInput":return(e=t.data)===or&&ar?null:e;default:return null}}(e,n):function(e,t){if(sr)return"compositionend"===e||!er&&ir(e,t)?(e=Sn(),xn=kn=wn=null,sr=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return rr&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(y=gd(r,"onBeforeInput")).length&&(v=new Un("onBeforeInput","beforeinput",null,n,o),i.push({event:v,listeners:y}),v.data=b),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var a=td((o[bt]||null).action),i=r.submitter;i&&null!==(t=(t=i[bt]||null)?td(t.formAction):i.getAttribute("formAction"))&&(a=t,i=null);var l=new Ln("action","action",null,r,o);e.push({event:l,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==qc){var e=i?nd(o,i):new FormData(o);$i(n,{pending:!0,data:e,method:o.method,action:a},null,e)}}else"function"==typeof a&&(l.preventDefault(),e=i?nd(o,i):new FormData(o),$i(n,{pending:!0,data:e,method:o.method,action:a},a,e))},currentTarget:o}]})}}(i,e,r,n,o)}ld(i,t)})}function md(e,t,n){return{instance:e,listener:t,currentTarget:n}}function gd(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5!==(o=o.tag)&&26!==o&&27!==o||null===a||(null!=(o=vn(e,n))&&r.unshift(md(e,o,a)),null!=(o=vn(e,t))&&r.push(md(e,o,a))),e=e.return}return r}function hd(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function vd(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(l=l.tag,null!==s&&s===r)break;5!==l&&26!==l&&27!==l||null===u||(s=u,o?null!=(u=vn(n,a))&&i.unshift(md(n,u,s)):o||null!=(u=vn(n,a))&&i.push(md(n,u,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var bd=/\r\n?/g,yd=/\u0000|\uFFFD/g;function wd(e){return("string"==typeof e?e:""+e).replace(bd,"\n").replace(yd,"")}function kd(e,t){return t=wd(t),wd(e)===t}function xd(){}function Sd(e,t,n,r,o,a){switch(n){case"children":"string"==typeof r?"body"===t||"textarea"===t&&""===r||tn(e,r):("number"==typeof r||"bigint"==typeof r)&&"body"!==t&&tn(e,""+r);break;case"className":Bt(e,"class",r);break;case"tabIndex":Bt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":Bt(e,n,r);break;case"style":on(e,r,a);break;case"data":if("object"!==t){Bt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"==typeof r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=un(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"==typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof a&&("formAction"===n?("input"!==t&&Sd(e,t,"name",o.name,o,null),Sd(e,t,"formEncType",o.formEncType,o,null),Sd(e,t,"formMethod",o.formMethod,o,null),Sd(e,t,"formTarget",o.formTarget,o,null)):(Sd(e,t,"encType",o.encType,o,null),Sd(e,t,"method",o.method,o,null),Sd(e,t,"target",o.target,o,null))),null==r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=un(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=xd);break;case"onScroll":null!=r&&sd("scroll",e);break;case"onScrollEnd":null!=r&&sd("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(I(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(I(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"muted":e.muted=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"==typeof r||"boolean"==typeof r||"symbol"==typeof r){e.removeAttribute("xlink:href");break}n=un(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!=typeof r&&"symbol"!=typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"==typeof r||"symbol"==typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":sd("beforetoggle",e),sd("toggle",e),It(e,"popover",r);break;case"xlinkActuate":Ut(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":Ut(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":Ut(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":Ut(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":Ut(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":Ut(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":Ut(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":Ut(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":Ut(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":It(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&It(e,n=ln.get(n)||n,r)}}function zd(e,t,n,r,o,a){switch(n){case"style":on(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(I(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(I(60));e.innerHTML=n}}break;case"children":"string"==typeof r?tn(e,r):("number"==typeof r||"bigint"==typeof r)&&tn(e,""+r);break;case"onScroll":null!=r&&sd("scroll",e);break;case"onScrollEnd":null!=r&&sd("scrollend",e);break;case"onClick":null!=r&&(e.onclick=xd);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Lt.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),"function"==typeof(a=null!=(a=e[bt]||null)?a[n]:null)&&e.removeEventListener(t,a,o),"function"!=typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):It(e,n,r):("function"!=typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,o)))}}function Ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":sd("error",e),sd("load",e);var r,o=!1,a=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":o=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(I(137,t));default:Sd(e,t,r,i,n,null)}}return a&&Sd(e,t,"srcSet",n.srcSet,n,null),void(o&&Sd(e,t,"src",n.src,n,null));case"input":sd("invalid",e);var l=r=i=a=null,s=null,u=null;for(o in n)if(n.hasOwnProperty(o)){var c=n[o];if(null!=c)switch(o){case"name":a=c;break;case"type":i=c;break;case"checked":s=c;break;case"defaultChecked":u=c;break;case"value":r=c;break;case"defaultValue":l=c;break;case"children":case"dangerouslySetInnerHTML":if(null!=c)throw Error(I(137,t));break;default:Sd(e,t,o,c,n,null)}}return Gt(e,r,l,s,u,i,a,!1),void $t(e);case"select":for(a in sd("invalid",e),o=i=r=null,n)if(n.hasOwnProperty(a)&&null!=(l=n[a]))switch(a){case"value":r=l;break;case"defaultValue":i=l;break;case"multiple":o=l;default:Sd(e,t,a,l,n,null)}return t=r,n=i,e.multiple=!!o,void(null!=t?Jt(e,!!o,t,!1):null!=n&&Jt(e,!!o,n,!0));case"textarea":for(i in sd("invalid",e),r=a=o=null,n)if(n.hasOwnProperty(i)&&null!=(l=n[i]))switch(i){case"value":o=l;break;case"defaultValue":a=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(I(91));break;default:Sd(e,t,i,l,n,null)}return en(e,o,a,r),void $t(e);case"option":for(s in n)n.hasOwnProperty(s)&&null!=(o=n[s])&&("selected"===s?e.selected=o&&"function"!=typeof o&&"symbol"!=typeof o:Sd(e,t,s,o,n,null));return;case"dialog":sd("cancel",e),sd("close",e);break;case"iframe":case"object":sd("load",e);break;case"video":case"audio":for(o=0;o<ad.length;o++)sd(ad[o],e);break;case"image":sd("error",e),sd("load",e);break;case"details":sd("toggle",e);break;case"embed":case"source":case"link":sd("error",e),sd("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(o=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(I(137,t));default:Sd(e,t,u,o,n,null)}return;default:if(an(t)){for(c in n)n.hasOwnProperty(c)&&void 0!==(o=n[c])&&zd(e,t,c,o,n,void 0);return}}for(l in n)n.hasOwnProperty(l)&&null!=(o=n[l])&&Sd(e,t,l,o,n,null)}var Cd=null,Pd=null;function Nd(e){return 9===e.nodeType?e:e.ownerDocument}function _d(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function jd(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function Md(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var Ld=null,Td="function"==typeof setTimeout?setTimeout:void 0,Ad="function"==typeof clearTimeout?clearTimeout:void 0,Od="function"==typeof Promise?Promise:void 0,Dd="function"==typeof queueMicrotask?queueMicrotask:void 0!==Od?function(e){return Od.resolve(null).then(e).catch(Fd)}:Td;function Fd(e){setTimeout(function(){throw e})}function Rd(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Yf(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Yf(t)}function Id(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Id(n),Et(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function Bd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}function Ud(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function Vd(e,t,n){switch(t=Nd(n),e){case"html":if(!(e=t.documentElement))throw Error(I(452));return e;case"head":if(!(e=t.head))throw Error(I(453));return e;case"body":if(!(e=t.body))throw Error(I(454));return e;default:throw Error(I(451))}}var Hd=new Map,$d=new Set;function Qd(e){return"function"==typeof e.getRootNode?e.getRootNode():e.ownerDocument}var Wd=ke.d;ke.d={f:function(){var e=Wd.f(),t=vc();return e||t},r:function(e){var t=Pt(e);null!==t&&5===t.tag&&"form"===t.type?Wi(t):Wd.r(e)},D:function(e){Wd.D(e),Kd("dns-prefetch",e,null)},C:function(e,t){Wd.C(e,t),Kd("preconnect",e,t)},L:function(e,t,n){Wd.L(e,t,n);var r=qd;if(r&&e&&t){var o='link[rel="preload"][as="'+Kt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+Kt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(o+='[imagesizes="'+Kt(n.imageSizes)+'"]')):o+='[href="'+Kt(e)+'"]';var a=o;switch(t){case"style":a=Gd(e);break;case"script":a=Zd(e)}Hd.has(a)||(e=ce({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Hd.set(a,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(Xd(a))||"script"===t&&r.querySelector(ef(a))||(Ed(t=r.createElement("link"),"link",e),jt(t),r.head.appendChild(t)))}},m:function(e,t){Wd.m(e,t);var n=qd;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+Kt(r)+'"][href="'+Kt(e)+'"]',a=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=Zd(e)}if(!Hd.has(a)&&(e=ce({rel:"modulepreload",href:e},t),Hd.set(a,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(ef(a)))return}Ed(r=n.createElement("link"),"link",e),jt(r),n.head.appendChild(r)}}},X:function(e,t){Wd.X(e,t);var n=qd;if(n&&e){var r=_t(n).hoistableScripts,o=Zd(e),a=r.get(o);a||((a=n.querySelector(ef(o)))||(e=ce({src:e,async:!0},t),(t=Hd.get(o))&&of(e,t),jt(a=n.createElement("script")),Ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}},S:function(e,t,n){Wd.S(e,t,n);var r=qd;if(r&&e){var o=_t(r).hoistableStyles,a=Gd(e);t=t||"default";var i=o.get(a);if(!i){var l={loading:0,preload:null};if(i=r.querySelector(Xd(a)))l.loading=5;else{e=ce({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Hd.get(a))&&rf(e,n);var s=i=r.createElement("link");jt(s),Ed(s,"link",e),s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),s.addEventListener("load",function(){l.loading|=1}),s.addEventListener("error",function(){l.loading|=2}),l.loading|=4,nf(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:l},o.set(a,i)}}},M:function(e,t){Wd.M(e,t);var n=qd;if(n&&e){var r=_t(n).hoistableScripts,o=Zd(e),a=r.get(o);a||((a=n.querySelector(ef(o)))||(e=ce({src:e,async:!0,type:"module"},t),(t=Hd.get(o))&&of(e,t),jt(a=n.createElement("script")),Ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}}};var qd="undefined"==typeof document?null:document;function Kd(e,t,n){var r=qd;if(r&&"string"==typeof t&&t){var o=Kt(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"==typeof n&&(o+='[crossorigin="'+n+'"]'),$d.has(o)||($d.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(Ed(t=r.createElement("link"),"link",e),jt(t),r.head.appendChild(t)))}}function Yd(e,t,n,r){var o,a,i,l,s=(s=je.current)?Qd(s):null;if(!s)throw Error(I(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Gd(n.href),(r=(n=_t(s).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Gd(n.href);var u=_t(s).hoistableStyles,c=u.get(e);if(c||(s=s.ownerDocument||s,c={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,c),(u=s.querySelector(Xd(e)))&&!u._p&&(c.instance=u,c.state.loading=5),Hd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Hd.set(e,n),u||(o=s,a=e,i=n,l=c.state,o.querySelector('link[rel="preload"][as="style"]['+a+"]")?l.loading=1:(a=o.createElement("link"),l.preload=a,a.addEventListener("load",function(){return l.loading|=1}),a.addEventListener("error",function(){return l.loading|=2}),Ed(a,"link",i),jt(a),o.head.appendChild(a))))),t&&null===r)throw Error(I(528,""));return c}if(t&&null!==r)throw Error(I(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Zd(n),(r=(n=_t(s).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(I(444,e))}}function Gd(e){return'href="'+Kt(e)+'"'}function Xd(e){return'link[rel="stylesheet"]['+e+"]"}function Jd(e){return ce({},e,{"data-precedence":e.precedence,precedence:null})}function Zd(e){return'[src="'+Kt(e)+'"]'}function ef(e){return"script[async]"+e}function tf(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+Kt(n.href)+'"]');if(r)return t.instance=r,jt(r),r;var o=ce({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return jt(r=(e.ownerDocument||e).createElement("style")),Ed(r,"style",o),nf(r,n.precedence,e),t.instance=r;case"stylesheet":o=Gd(n.href);var a=e.querySelector(Xd(o));if(a)return t.state.loading|=4,t.instance=a,jt(a),a;r=Jd(n),(o=Hd.get(o))&&rf(r,o),jt(a=(e.ownerDocument||e).createElement("link"));var i=a;return i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),Ed(a,"link",r),t.state.loading|=4,nf(a,n.precedence,e),t.instance=a;case"script":return a=Zd(n.src),(o=e.querySelector(ef(a)))?(t.instance=o,jt(o),o):(r=n,(o=Hd.get(a))&&of(r=ce({},n),o),jt(o=(e=e.ownerDocument||e).createElement("script")),Ed(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(I(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,nf(r,n.precedence,e));return t.instance}function nf(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,a=o,i=0;i<r.length;i++){var l=r[i];if(l.dataset.precedence===t)a=l;else if(a!==o)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function rf(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function of(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var af=null;function lf(e,t,n){if(null===af){var r=new Map,o=af=new Map;o.set(n,r)}else(r=(o=af).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var a=n[o];if(!(a[zt]||a[vt]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var i=a.getAttribute(t)||"";i=e+i;var l=r.get(i);l?l.push(a):r.set(i,[a])}}return r}function sf(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function uf(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var cf=null;function df(){}function ff(){if(this.count--,0===this.count)if(this.stylesheets)mf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var pf=null;function mf(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,pf=new Map,t.forEach(gf,e),pf=null,ff.call(e))}function gf(e,t){if(!(4&t.state.loading)){var n=pf.get(e);if(n)var r=n.get(null);else{n=new Map,pf.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<o.length;a++){var i=o[a];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(o=t.instance).getAttribute("data-precedence"),(a=n.get(i)||r)===r&&n.set(null,o),n.set(i,o),this.count++,r=ff.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),a?a.parentNode.insertBefore(o,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var hf={$$typeof:Y,Provider:null,Consumer:null,_currentValue:xe,_currentValue2:xe,_threadCount:0};function vf(e,t,n,r,o,a,i,l){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ct(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ct(0),this.hiddenUpdates=ct(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=a,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function bf(e,t,n,r,o,a,i,l,s,u,c,d){return e=new vf(e,t,n,i,l,s,u,d),t=1,!0===a&&(t|=24),a=mu(3,null,null,t),e.current=a,a.stateNode=e,(t=wa()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},ss(a),e}function yf(e){return e?e=fo:fo}function wf(e,t,n,r,o,a){o=yf(o),null===r.context?r.context=o:r.pendingContext=o,(r=cs(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=ds(e,r,t))&&(dc(n,0,t),fs(n,e,t))}function kf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function xf(e,t){kf(e,t),(e=e.alternate)&&kf(e,t)}function Sf(e){if(13===e.tag){var t=so(e,67108864);null!==t&&dc(t,0,67108864),xf(e,67108864)}}var zf=!0;function Ef(e,t,n,r){var o=ue.T;ue.T=null;var a=ke.p;try{ke.p=2,Pf(e,t,n,r)}finally{ke.p=a,ue.T=o}}function Cf(e,t,n,r){var o=ue.T;ue.T=null;var a=ke.p;try{ke.p=8,Pf(e,t,n,r)}finally{ke.p=a,ue.T=o}}function Pf(e,t,n,r){if(zf){var o=Nf(r);if(null===o)pd(e,t,r,_f,n),Bf(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Tf=Uf(Tf,e,t,n,r,o),!0;case"dragenter":return Af=Uf(Af,e,t,n,r,o),!0;case"mouseover":return Of=Uf(Of,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Df.set(a,Uf(Df.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,Ff.set(a,Uf(Ff.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Bf(e,r),4&t&&-1<If.indexOf(e)){for(;null!==o;){var a=Pt(o);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var i=ot(a.pendingLanes);if(0!==i){var l=a;for(l.pendingLanes|=2,l.entangledLanes|=2;i;){var s=1<<31-Ze(i);l.entanglements[1]|=s,i&=~s}Kc(a),!(6&Tu)&&(Zu=Ue()+500,Yc(0))}}break;case 13:null!==(l=so(a,2))&&dc(l,0,2),vc(),xf(a,2)}if(null===(a=Nf(r))&&pd(e,t,r,_f,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else pd(e,t,r,null,n)}}function Nf(e){return jf(e=dn(e))}var _f=null;function jf(e){if(_f=null,null!==(e=Ct(e))){var t=he(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return _f=e,null}function Mf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ve()){case He:return 2;case $e:return 8;case Qe:case We:return 32;case qe:return 268435456;default:return 32}default:return 32}}var Lf=!1,Tf=null,Af=null,Of=null,Df=new Map,Ff=new Map,Rf=[],If="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Bf(e,t){switch(e){case"focusin":case"focusout":Tf=null;break;case"dragenter":case"dragleave":Af=null;break;case"mouseover":case"mouseout":Of=null;break;case"pointerover":case"pointerout":Df.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ff.delete(t.pointerId)}}function Uf(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&null!==(t=Pt(t))&&Sf(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Vf(e){var t=Ct(e.target);if(null!==t){var n=he(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=ve(n)))return e.blockedOn=t,void function(e,t){var n=ke.p;try{return ke.p=e,t()}finally{ke.p=n}}(e.priority,function(){if(13===n.tag){var e=uc(),t=so(n,e);null!==t&&dc(t,0,e),xf(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Hf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Nf(e.nativeEvent);if(null!==n)return null!==(t=Pt(n))&&Sf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);cn=r,n.target.dispatchEvent(r),cn=null,t.shift()}return!0}function $f(e,t,n){Hf(e)&&n.delete(t)}function Qf(){Lf=!1,null!==Tf&&Hf(Tf)&&(Tf=null),null!==Af&&Hf(Af)&&(Af=null),null!==Of&&Hf(Of)&&(Of=null),Df.forEach($f),Ff.forEach($f)}function Wf(e,t){e.blockedOn===t&&(e.blockedOn=null,Lf||(Lf=!0,D.unstable_scheduleCallback(D.unstable_NormalPriority,Qf)))}var qf=null;function Kf(e){qf!==e&&(qf=e,D.unstable_scheduleCallback(D.unstable_NormalPriority,function(){qf===e&&(qf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],o=e[t+2];if("function"!=typeof r){if(null===jf(r||n))continue;break}var a=Pt(n);null!==a&&(e.splice(t,3),t-=3,$i(a,{pending:!0,data:o,method:n.method,action:r},r,o))}}))}function Yf(e){function t(t){return Wf(t,e)}null!==Tf&&Wf(Tf,e),null!==Af&&Wf(Af,e),null!==Of&&Wf(Of,e),Df.forEach(t),Ff.forEach(t);for(var n=0;n<Rf.length;n++){var r=Rf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<Rf.length&&null===(n=Rf[0]).blockedOn;)Vf(n),null===n.blockedOn&&Rf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],a=n[r+1],i=o[bt]||null;if("function"==typeof a)i||Kf(n);else if(i){var l=null;if(a&&a.hasAttribute("formAction")){if(o=a,i=a[bt]||null)l=i.formAction;else if(null!==jf(o))continue}else l=i.action;"function"==typeof l?n[r+1]=l:(n.splice(r,3),r-=3),Kf(n)}}}function Gf(e){this._internalRoot=e}function Xf(e){this._internalRoot=e}Xf.prototype.render=Gf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(I(409));wf(t.current,uc(),e,t,null,null)},Xf.prototype.unmount=Gf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;0===e.tag&&Ac(),wf(e.current,2,null,e,null,null),vc(),t[yt]=null}},Xf.prototype.unstable_scheduleHydration=function(e){if(e){var t=gt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rf.length&&0!==t&&t<Rf[n].priority;n++);Rf.splice(n,0,e),0===n&&Vf(e)}};var Jf=F.version;if("19.0.0"!==Jf)throw Error(I(527,Jf,"19.0.0"));ke.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(I(188));throw e=Object.keys(e).join(","),Error(I(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=he(e)))throw Error(I(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return be(o),e;if(a===r)return be(o),t;a=a.sibling}throw Error(I(188))}if(n.return!==r.return)n=o,r=a;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=a;break}if(l===r){i=!0,r=o,n=a;break}l=l.sibling}if(!i){for(l=a.child;l;){if(l===n){i=!0,n=a,r=o;break}if(l===r){i=!0,r=a,n=o;break}l=l.sibling}if(!i)throw Error(I(189))}}if(n.alternate!==r)throw Error(I(190))}if(3!==n.tag)throw Error(I(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?ye(e):null)?null:e.stateNode};var Zf={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:ue,findFiberByHostInstance:Ct,reconcilerVersion:"19.0.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ep=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ep.isDisabled&&ep.supportsFiber)try{Ge=ep.inject(Zf),Xe=ep}catch(Dp){}}T.createRoot=function(e,t){if(!B(e))throw Error(I(299));var n=!1,r="",o=ml,a=gl,i=hl;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(o=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(i=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=bf(e,1,!1,null,0,n,r,o,a,i,0,null),e[yt]=t.current,dd(8===e.nodeType?e.parentNode:e),new Gf(t)},T.hydrateRoot=function(e,t,n){if(!B(e))throw Error(I(299));var r=!1,o="",a=ml,i=gl,l=hl,s=null;return null!=n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(i=n.onCaughtError),void 0!==n.onRecoverableError&&(l=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(s=n.formState)),(t=bf(e,1,!0,t,0,r,o,a,i,l,0,s)).context=yf(null),n=t.current,(o=cs(r=uc())).callback=null,ds(n,o,r),t.current.lanes=r,dt(t,r),Kc(t),e[yt]=t.current,dd(e),new Xf(t)},T.version="19.0.0",function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(Dp){}}(),L.exports=T;var tp=L.exports;e("u","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M256 112v288M400 256H112' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("B","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><rect x='32' y='128' width='448' height='320' rx='48' ry='48' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M144 128V96a32 32 0 0132-32h160a32 32 0 0132 32v32M480 240H32M320 240v24a8 8 0 01-8 8H200a8 8 0 01-8-8v-24' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("C","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M176 416v64M80 32h192a32 32 0 0132 32v412a4 4 0 01-4 4H48h0V64a32 32 0 0132-32zM320 192h112a32 32 0 0132 32v256h0-160 0V208a16 16 0 0116-16z' class='ionicon-fill-none ionicon-stroke-width'/><path d='M98.08 431.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM98.08 351.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM98.08 271.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM98.08 191.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM98.08 111.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM178.08 351.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM178.08 271.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM178.08 191.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM178.08 111.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM258.08 431.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM258.08 351.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM258.08 271.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79z'/><ellipse cx='256' cy='176' rx='15.95' ry='16.03' transform='rotate(-45 255.99 175.996)'/><path d='M258.08 111.87a16 16 0 1113.79-13.79 16 16 0 01-13.79 13.79zM400 400a16 16 0 1016 16 16 16 0 00-16-16zM400 320a16 16 0 1016 16 16 16 0 00-16-16zM400 240a16 16 0 1016 16 16 16 0 00-16-16zM336 400a16 16 0 1016 16 16 16 0 00-16-16zM336 320a16 16 0 1016 16 16 16 0 00-16-16zM336 240a16 16 0 1016 16 16 16 0 00-16-16z'/></svg>"),e("c","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' class='ionicon'><rect stroke-linejoin='round' x='48' y='80' width='416' height='384' rx='48' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='296' cy='232' r='24'/><circle cx='376' cy='232' r='24'/><circle cx='296' cy='312' r='24'/><circle cx='376' cy='312' r='24'/><circle cx='136' cy='312' r='24'/><circle cx='216' cy='312' r='24'/><circle cx='136' cy='392' r='24'/><circle cx='216' cy='392' r='24'/><circle cx='296' cy='392' r='24'/><path stroke-linejoin='round' stroke-linecap='round' d='M128 48v32M384 48v32' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linejoin='round' d='M464 160H48' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("h","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M350.54 148.68l-26.62-42.06C318.31 100.08 310.62 96 302 96h-92c-8.62 0-16.31 4.08-21.92 10.62l-26.62 42.06C155.85 155.23 148.62 160 140 160H80a32 32 0 00-32 32v192a32 32 0 0032 32h352a32 32 0 0032-32V192a32 32 0 00-32-32h-59c-8.65 0-16.85-4.77-22.46-11.32z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='272' r='80' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M124 158v-22h-24v22' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("j","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M352 176L217.6 336 160 272' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("m","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>"),e("k","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 328l144-144 144 144' class='ionicon-fill-none'/></svg>"),e("a","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M336 64h32a48 48 0 0148 48v320a48 48 0 01-48 48H144a48 48 0 01-48-48V112a48 48 0 0148-48h32' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='176' y='32' width='160' height='64' rx='26.13' ry='26.13' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("f","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M320 320L192 192M192 320l128-128' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("g","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M368 368L144 144M368 144L144 368' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("e","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M320 336h76c55 0 100-21.21 100-75.6s-53-73.47-96-75.6C391.11 99.74 329 48 256 48c-69 0-113.44 45.79-128 91.2-60 5.7-112 35.88-112 98.4S70 336 136 336h56M192 400.1l64 63.9 64-63.9M256 224v224.03' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("d","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M416 221.25V416a48 48 0 01-48 48H144a48 48 0 01-48-48V96a48 48 0 0148-48h98.75a32 32 0 0122.62 9.37l141.26 141.26a32 32 0 019.37 22.62z' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 56v120a32 32 0 0032 32h120M176 288h160M176 368h160' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>");const np=e("q","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 00-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 000-17.47C428.89 172.28 347.8 112 255.66 112z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='256' r='80' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),rp=(e("n","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M32 144h448M112 256h288M208 368h96' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("z","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 192v-72a40 40 0 0140-40h75.89a40 40 0 0122.19 6.72l27.84 18.56a40 40 0 0022.19 6.72H408a40 40 0 0140 40v40' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M479.9 226.55L463.68 392a40 40 0 01-39.93 40H88.25a40 40 0 01-39.93-40L32.1 226.55A32 32 0 0164 192h384.1a32 32 0 0131.8 34.55z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("A","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' class='ionicon'><rect x='96' y='32' width='320' height='448' rx='48' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M208 80h96' class='ionicon-fill-none ionicon-stroke-width'/><path d='M333.48 284.51A39.65 39.65 0 00304 272c-11.6 0-22.09 4.41-29.54 12.43s-11.2 19.12-10.34 31C265.83 338.91 283.72 358 304 358s38.14-19.09 39.87-42.55c.88-11.78-2.82-22.77-10.39-30.94zM371.69 448H236.31a12.05 12.05 0 01-9.31-4.17 13 13 0 01-2.76-10.92c3.25-17.56 13.38-32.31 29.3-42.66C267.68 381.06 285.6 376 304 376s36.32 5.06 50.46 14.25c15.92 10.35 26.05 25.1 29.3 42.66a13 13 0 01-2.76 10.92 12.05 12.05 0 01-9.31 4.17z'/></svg>"),e("y","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M432 112V96a48.14 48.14 0 00-48-48H64a48.14 48.14 0 00-48 48v256a48.14 48.14 0 0048 48h16' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='128' width='400' height='336' rx='45.99' ry='45.99' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><ellipse cx='372.92' cy='219.64' rx='30.77' ry='30.55' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M342.15 372.17L255 285.78a30.93 30.93 0 00-42.18-1.21L96 387.64M265.23 464l118.59-117.73a31 31 0 0141.46-1.87L496 402.91' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("s","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M220 220h32v116' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M208 340h88' class='ionicon-fill-none ionicon-stroke-width'/><path d='M248 130a26 26 0 1026 26 26 26 0 00-26-26z'/></svg>"),e("D","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M218.1 167.17c0 13 0 25.6 4.1 37.4-43.1 50.6-156.9 184.3-167.5 194.5a20.17 20.17 0 00-6.7 15c0 8.5 5.2 16.7 9.6 21.3 6.6 6.9 34.8 33 40 28 15.4-15 18.5-19 24.8-25.2 9.5-9.3-1-28.3 2.3-36s6.8-9.2 12.5-10.4 15.8 2.9 23.7 3c8.3.1 12.8-3.4 19-9.2 5-4.6 8.6-8.9 8.7-15.6.2-9-12.8-20.9-3.1-30.4s23.7 6.2 34 5 22.8-15.5 24.1-21.6-11.7-21.8-9.7-30.7c.7-3 6.8-10 11.4-11s25 6.9 29.6 5.9c5.6-1.2 12.1-7.1 17.4-10.4 15.5 6.7 29.6 9.4 47.7 9.4 68.5 0 124-53.4 124-119.2S408.5 48 340 48s-121.9 53.37-121.9 119.17zM400 144a32 32 0 11-32-32 32 32 0 0132 32z' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("i","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48c-79.5 0-144 61.39-144 137 0 87 96 224.87 131.25 272.49a15.77 15.77 0 0025.5 0C304 409.89 400 272.07 400 185c0-75.61-64.5-137-144-137z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='192' r='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M336 208v-95a80 80 0 00-160 0v95' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='208' width='320' height='272' rx='48' ry='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),op="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M192 176v-40a40 40 0 0140-40h160a40 40 0 0140 40v240a40 40 0 01-40 40H240c-22.09 0-48-17.91-48-40v-40' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M288 336l80-80-80-80M80 256h272' class='ionicon-fill-none ionicon-stroke-width'/></svg>",ap=(e("l","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M304 336v40a40 40 0 01-40 40H104a40 40 0 01-40-40V136a40 40 0 0140-40h152c22.09 0 48 17.91 48 40v40M368 336l80-80-80-80M176 256h256' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("p","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M402 168c-2.93 40.67-33.1 72-66 72s-63.12-31.32-66-72c-3-42.31 26.37-72 66-72s69 30.46 66 72z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M336 304c-65.17 0-127.84 32.37-143.54 95.41-2.08 8.34 3.15 16.59 11.72 16.59h263.65c8.57 0 13.77-8.25 11.72-16.59C463.85 335.36 401.18 304 336 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 185.94c-2.34 32.48-26.72 58.06-53 58.06s-50.7-25.57-53-58.06C91.61 152.15 115.34 128 147 128s55.39 24.77 53 57.94z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M206 306c-18.05-8.27-37.93-11.45-59-11.45-52 0-102.1 25.85-114.65 76.2-1.65 6.66 2.53 13.25 9.37 13.25H154' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("b","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M258.9 48C141.92 46.42 46.42 141.92 48 258.9c1.56 112.19 92.91 203.54 205.1 205.1 117 1.6 212.48-93.9 210.88-210.88C462.44 140.91 371.09 49.56 258.9 48zm126.42 327.25a4 4 0 01-6.14-.32 124.27 124.27 0 00-32.35-29.59C321.37 329 289.11 320 256 320s-65.37 9-90.83 25.34a124.24 124.24 0 00-32.35 29.58 4 4 0 01-6.14.32A175.32 175.32 0 0180 259c-1.63-97.31 78.22-178.76 175.57-179S432 158.81 432 256a175.32 175.32 0 01-46.68 119.25z'/><path d='M256 144c-19.72 0-37.55 7.39-50.22 20.82s-19 32-17.57 51.93C191.11 256 221.52 288 256 288s64.83-32 67.79-71.24c1.48-19.74-4.8-38.14-17.68-51.82C293.39 151.44 275.59 144 256 144z'/></svg>"));e("o","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M344 144c-3.92 52.87-44 96-88 96s-84.15-43.12-88-96c-4-55 35-96 88-96s92 42 88 96z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 304c-87 0-175.3 48-191.64 138.6C62.39 453.52 68.57 464 80 464h352c11.44 0 17.62-10.48 15.65-21.4C431.3 352 343 304 256 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("v","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><rect x='336' y='336' width='80' height='80' rx='8' ry='8'/><rect x='272' y='272' width='64' height='64' rx='8' ry='8'/><rect x='416' y='416' width='64' height='64' rx='8' ry='8'/><rect x='432' y='272' width='48' height='48' rx='8' ry='8'/><rect x='272' y='432' width='48' height='48' rx='8' ry='8'/><rect x='336' y='96' width='80' height='80' rx='8' ry='8'/><rect x='288' y='48' width='176' height='176' rx='16' ry='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='96' width='80' height='80' rx='8' ry='8'/><rect x='48' y='48' width='176' height='176' rx='16' ry='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='336' width='80' height='80' rx='8' ry='8'/><rect x='48' y='288' width='176' height='176' rx='16' ry='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("r","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M320 146s24.36-12-64-12a160 160 0 10160 160' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 58l80 80-80 80' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("t","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 128v144h96' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("x","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M80 112h352' class='ionicon-stroke-width'/><path d='M192 112V72h0a23.93 23.93 0 0124-24h80a23.93 23.93 0 0124 24h0v40M256 176v224M184 176l8 224M328 176l-8 224' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>"),e("w","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M85.57 446.25h340.86a32 32 0 0028.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0028.17 47.17z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 195.39l5.74 122 5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 5.95z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 397.25a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>");var ip,lp=new Uint8Array(16);function sp(){if(!ip&&!(ip="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ip(lp)}const up=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var cp=[],dp=0;dp<256;++dp)cp.push((dp+256).toString(16).substr(1));function fp(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(cp[e[t+0]]+cp[e[t+1]]+cp[e[t+2]]+cp[e[t+3]]+"-"+cp[e[t+4]]+cp[e[t+5]]+"-"+cp[e[t+6]]+cp[e[t+7]]+"-"+cp[e[t+8]]+cp[e[t+9]]+"-"+cp[e[t+10]]+cp[e[t+11]]+cp[e[t+12]]+cp[e[t+13]]+cp[e[t+14]]+cp[e[t+15]]).toLowerCase();if(!function(e){return"string"==typeof e&&up.test(e)}(n))throw TypeError("Stringified UUID is invalid");return n}function pp(e,t,n){var r=(e=e||{}).random||(e.rng||sp)();return r[6]=15&r[6]|64,r[8]=63&r[8]|128,fp(r)}const mp=()=>{const[e,t]=n.useState(""),[r,y]=n.useState(""),[w,k]=n.useState(!1),[x,S]=n.useState(!1),[z,E]=n.useState(""),[P,N]=n.useState(!1),_=C();return o.jsx(a,{children:o.jsxs(i,{className:"ion-padding login-bg",fullscreen:!0,children:[o.jsxs("div",{className:"login-hero",children:[o.jsx("div",{className:"login-blob login-blob-1"}),o.jsx("div",{className:"login-blob login-blob-2"}),o.jsxs("div",{className:"login-brand",children:[o.jsx("img",{src:"/logo192.png",alt:"Logo",className:"login-logo"}),o.jsxs("div",{children:[o.jsx("h1",{className:"login-title",children:"Absensi Karyawan"}),o.jsx("p",{className:"login-desc",children:"Masuk untuk melanjutkan aktivitas Anda"})]})]})]}),o.jsx("div",{className:"login-container",children:o.jsxs(l,{className:"login-card glass",children:[o.jsx(s,{children:o.jsxs(u,{className:"login-card-title",children:[o.jsx(c,{icon:op,className:"login-card-title-icon"}),"Masuk Akun"]})}),o.jsx(d,{children:o.jsxs("form",{onSubmit:async t=>{t.preventDefault(),N(!0);try{const t=(()=>{let e=localStorage.getItem("device_id");return e||(e=pp(),localStorage.setItem("device_id",e)),e})(),o=`https://absensiku.trunois.my.id/api/status_user.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(e)}&device_id=${encodeURIComponent(t)}`,a=await fetch(o),i=await a.json();if("success"!==i.status)return E("Gagal cek status akun"),S(!0),void N(!1);{const e=i.data;if(Array.isArray(e)&&e.length>0){const n=e[0];if("blokir"===n.status_akun)return E("Akun Anda diblokir, silakan hubungi admin"),S(!0),void N(!1);if("login"===n.status_login&&(!n.device_id||n.device_id!==t))return E("Akun sudah login di perangkat lain, tidak bisa login lagi"),S(!0),void N(!1)}}const l=`https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(e)}&password=${encodeURIComponent(r)}`,s=await fetch(l),u=await s.json();if("success"===u.status){const r=Array.isArray(u.data)?u.data[0]:u.data;localStorage.setItem("user",JSON.stringify(r));try{const n=`https://absensiku.trunois.my.id/api/status_user.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(e)}`,r=await fetch(n),o=await r.json();let a="POST",i={api_key:"absensiku_api_key_2023",nik:e,status_login:"login",status_akun:"aktif",device_id:t},l="https://absensiku.trunois.my.id/api/status_user.php";"success"===o.status&&Array.isArray(o.data)&&o.data.length>0&&(a="PUT",i.id=o.data[0].id),await fetch(l,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})}catch(n){}_.push("/home")}else E(u.message||"NIK atau password salah"),S(!0)}catch(o){E("Terjadi kesalahan koneksi"),S(!0)}finally{N(!1)}},autoComplete:"off",children:[o.jsxs(f,{lines:"none",className:"login-list",children:[o.jsxs(p,{className:"login-input-item input-with-icon",children:[o.jsx(c,{icon:ap,slot:"start",className:"login-input-icon"}),o.jsxs("div",{className:"stacked-field",children:[o.jsx(m,{position:"stacked",children:"NIK"}),o.jsx(g,{value:e,onIonChange:e=>t(e.detail.value||""),required:!0,inputmode:"numeric",placeholder:"Masukkan NIK"})]})]}),o.jsxs(p,{className:"login-input-item input-with-icon",children:[o.jsx(c,{icon:rp,slot:"start",className:"login-input-icon"}),o.jsxs("div",{className:"stacked-field",children:[o.jsx(m,{position:"stacked",children:"Password"}),o.jsx("div",{className:"password-field",children:o.jsx(g,{type:w?"text":"password",value:r,onIonChange:e=>y(e.detail.value||""),required:!0,placeholder:"Masukkan password"})})]}),o.jsx(h,{slot:"end",fill:"clear",className:"eye-btn",onClick:()=>k(e=>!e),disabled:P,"aria-label":w?"Sembunyikan Password":"Lihat Password",children:o.jsx(c,{icon:w?"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M432 448a15.92 15.92 0 01-11.31-4.69l-352-352a16 16 0 0122.62-22.62l352 352A16 16 0 01432 448zM255.66 384c-41.49 0-81.5-12.28-118.92-36.5-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 00.14-2.94L93.5 161.38a2 2 0 00-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0075.8-12.58 2 2 0 00.77-3.31l-21.58-21.58a4 4 0 00-3.83-1 204.8 204.8 0 01-51.16 6.47zM490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 00-74.89 12.83 2 2 0 00-.75 3.31l21.55 21.55a4 4 0 003.88 1 192.82 192.82 0 0150.21-6.69c40.69 0 80.58 12.43 118.55 37 34.71 22.4 65.74 53.88 89.76 91a.13.13 0 010 .16 310.72 310.72 0 01-64.12 72.73 2 2 0 00-.15 2.95l19.9 19.89a2 2 0 002.7.13 343.49 343.49 0 0068.64-78.48 32.2 32.2 0 00-.1-34.78z'/><path d='M256 160a95.88 95.88 0 00-21.37 2.4 2 2 0 00-1 3.38l112.59 112.56a2 2 0 003.38-1A96 96 0 00256 160zM165.78 233.66a2 2 0 00-3.38 1 96 96 0 00115 115 2 2 0 001-3.38z'/></svg>":np})})]})]}),o.jsxs(h,{expand:"block",type:"submit",className:"ion-margin-top login-btn primary",color:"primary",shape:"round",disabled:P,children:[o.jsx(c,{icon:op,slot:"start"}),P?"Memproses...":"Login"]}),o.jsxs("div",{className:"login-footnote",children:[o.jsx(c,{icon:rp}),o.jsx("span",{children:"Data Anda aman dan terenkripsi."})]})]})})]})}),o.jsx(v,{isOpen:x,onDidDismiss:()=>S(!1),message:z,duration:2e3,color:"danger"}),o.jsx(b,{isOpen:P,message:"Memproses..."})]})})},gp=n.lazy(()=>y(()=>t.import("./Home-legacy-BU4WM0TG.js"),void 0)),hp=n.lazy(()=>y(()=>t.import("./Absensi-legacy-YST22tdR.js"),void 0)),vp=n.lazy(()=>y(()=>t.import("./Histori-legacy-CPx2dheu.js"),void 0)),bp=n.lazy(()=>y(()=>t.import("./IzinDinas-legacy-TrPVspcm.js"),void 0)),yp=n.lazy(()=>y(()=>t.import("./HistoriIzinDinas-legacy-BAGrWSmO.js"),void 0)),wp=n.lazy(()=>y(()=>t.import("./Rapat-legacy-BtxEdIEm.js"),void 0)),kp=n.lazy(()=>y(()=>t.import("./LaporanHarian-legacy-PNL-NwBf.js"),void 0)),xp=n.lazy(()=>y(()=>t.import("./Profile-legacy-nf4vTdFR.js"),void 0)),Sp=n.lazy(()=>y(()=>t.import("./Lembur-legacy-BoekG8MF.js"),void 0)),zp=n.lazy(()=>y(()=>t.import("./BarcodeTest-legacy-CzWbQ6s8.js"),void 0)),Ep=n.lazy(()=>y(()=>t.import("./ganti_password-legacy-Cs6R5mWm.js"),void 0));w();const Cp=({children:e,...t})=>{const n=!!localStorage.getItem("user");return o.jsx(P,{...t,render:({location:t})=>n?e:o.jsx(N,{to:{pathname:"/login",state:{from:t}}})})},Pp=()=>o.jsx(i,{className:"ion-padding",children:o.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"},children:[o.jsx(z,{name:"crescent"}),o.jsx("p",{style:{marginTop:"16px",color:"var(--ion-color-medium)"},children:"Memuat halaman..."})]})}),Np=()=>(n.useEffect(()=>{(async()=>{setTimeout(async()=>{try{const{fetchAndStoreLokasi:e}=await y(()=>t.import("./lokasi-legacy-Bdcnjewv.js"),void 0);await e();const{fetchAndStoreBidang:n}=await y(()=>t.import("./bidang-legacy-BYcpxj0T.js"),void 0);await n();const{fetchAndStoreJamKerja:r}=await y(()=>t.import("./jamKerja-legacy-BJSQBBXU.js"),void 0);await r();const{fetchAndStoreJamKerjaBidang:o}=await y(()=>t.import("./jamKerjaBidang-legacy-D-Z9PwNM.js"),void 0);await o()}catch(e){}},100)})()},[]),n.useEffect(()=>{const e=async(e,t)=>{const n=`violation_sent_${t}`;if(((e,t)=>{try{const n=localStorage.getItem(e);return!!n&&Date.now()-new Date(n).getTime()<t}catch{return!1}})(n,3e5))return;const r=(()=>{try{const e=localStorage.getItem("user"),t=e?JSON.parse(e):{};return Array.isArray(t)?t[0]||{}:t}catch{return{}}})(),o=localStorage.getItem("device_id")||"",a={api_key:"absensiku_api_key_2023",user_id:r?.id||r?.nik||"",nik:r?.nik||"",device_id:o,alasan:e};try{await fetch("https://absensiku.trunois.my.id/api/blokir_device.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),(e=>{try{localStorage.setItem(e,(new Date).toISOString())}catch{}})(n)}catch(Op){}},t=t=>{e("Penggunaan Fake GPS terdeteksi","fake_gps")},n=t=>{e("Pengaturan waktu otomatis dimatikan","auto_time_off")};return window.addEventListener("fakeGpsDetected",t),window.addEventListener("autoTimeDisabled",n),()=>{window.removeEventListener("fakeGpsDetected",t),window.removeEventListener("autoTimeDisabled",n)}},[]),o.jsx(k,{children:o.jsx(x,{children:o.jsxs(S,{children:[o.jsx(Cp,{exact:!0,path:"/home",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(gp,{})})}),o.jsx(Cp,{exact:!0,path:"/absensi",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(hp,{})})}),o.jsx(Cp,{exact:!0,path:"/histori",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(vp,{})})}),o.jsx(Cp,{exact:!0,path:"/izin-dinas",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(bp,{})})}),o.jsx(Cp,{exact:!0,path:"/histori-izin-dinas",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(yp,{})})}),o.jsx(Cp,{exact:!0,path:"/rapat",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(wp,{})})}),o.jsx(Cp,{exact:!0,path:"/laporan-harian",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(kp,{})})}),o.jsx(Cp,{exact:!0,path:"/profile",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(xp,{})})}),o.jsx(Cp,{exact:!0,path:"/lembur",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(Sp,{})})}),o.jsx(Cp,{exact:!0,path:"/barcode-test",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(zp,{})})}),o.jsx(Cp,{exact:!0,path:"/ganti-password",children:o.jsx(n.Suspense,{fallback:o.jsx(Pp,{}),children:o.jsx(Ep,{})})}),o.jsx(P,{exact:!0,path:"/logout",render:()=>(localStorage.removeItem("user"),window.location.replace("/login"),null)}),o.jsx(P,{exact:!0,path:"/login",render:()=>localStorage.getItem("user")?o.jsx(N,{to:"/home"}):o.jsx(mp,{})}),o.jsx(P,{exact:!0,path:"/",children:o.jsx(N,{to:"/login"})})]})})}));let _p=null,jp=!1;const Mp=new Map;function Lp(e){try{return JSON.parse(e)}catch{return e}}const Tp=document.getElementById("root"),Ap=tp.createRoot(Tp);(async function(){if(jp)return;let e=null,n=null;try{const r=await y(()=>t.import("./localforage-cordovasqlitedriver.es6-legacy-3gX-Jw1h.js"),void 0);n=r?.default??r,e=n?._driver||"cordovaSQLiteDriver"}catch{}const r=[];if(e&&r.push(e),r.push(j.IndexedDB,j.LocalStorage),_p=new _({name:"absensipdam_db",driverOrder:r}),n)try{await _p.defineDriver(n)}catch{}await _p.create();try{await _p.forEach((e,t)=>{try{const n="string"==typeof e?e:JSON.stringify(e);Mp.set(t,n),window.localStorage.setItem(t,n)}catch{}})}catch{}try{for(let e=0;e<window.localStorage.length;e++){const t=window.localStorage.key(e);if(!t)continue;const n=window.localStorage.getItem(t);null!=n&&(await _p.set(t,Lp(n)),Mp.set(t,n))}}catch{}try{const e=window.localStorage.setItem.bind(window.localStorage),t=window.localStorage.removeItem.bind(window.localStorage),n=window.localStorage.getItem.bind(window.localStorage),r=window.localStorage.clear.bind(window.localStorage);window.localStorage.setItem=(t,n)=>{e(t,n),_p.set(t,Lp(n)),Mp.set(t,n)},window.localStorage.removeItem=e=>{t(e),_p.remove(e),Mp.delete(e)},window.localStorage.getItem=e=>Mp.has(e)?Mp.get(e):n(e),window.localStorage.clear=()=>{r(),Mp.clear()}}catch{}jp=!0})().catch(()=>{}).finally(()=>{Ap.render(o.jsx(E.StrictMode,{children:o.jsx(Np,{})}))})}}});

System.register(["./ionic-legacy-DbGqp7zN.js","./react-vendor-legacy-wCcNgjsd.js"],function(t,e){"use strict";var n,r,a;return{setters:[t=>{n=t.a6,r=t.a7,a=t.a8},null],execute:function(){
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
t("createSwipeBackGesture",(t,e,c,i,o)=>{const s=t.ownerDocument.defaultView;let l=n(t);const u=t=>l?-t.deltaX:t.deltaX;return r({el:t,gestureName:"goback-swipe",gesturePriority:101,threshold:10,canStart:r=>(l=n(t),(t=>{const{startX:e}=t;return l?e>=s.innerWidth-50:e<=50})(r)&&e()),onStart:c,onMove:t=>{const e=u(t)/s.innerWidth;i(e)},onEnd:t=>{const e=u(t),n=s.innerWidth,r=e/n,c=(t=>l?-t.velocityX:t.velocityX)(t),i=c>=0&&(c>.2||e>n/2),d=(i?1-r:r)*n;let h=0;if(d>5){const t=d/Math.abs(c);h=Math.min(t,540)}o(i,r<=0?.01:a(0,r,.9999),h)}})})}}});

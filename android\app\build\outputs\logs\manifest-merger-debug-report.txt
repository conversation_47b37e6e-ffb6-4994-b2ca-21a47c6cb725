-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:42:9-50:20
	android:grantUriPermissions
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:46:13-47
	android:authorities
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:44:13-64
	android:exported
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:45:13-37
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:43:13-62
manifest
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:2:1-66:12
MERGED from [:capacitor-community-barcode-scanner] E:\absensi\absensipdam\node_modules\@capacitor-community\barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-12:12
MERGED from [:capacitor-app] E:\absensi\absensipdam\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:capacitor-geolocation] E:\absensi\absensipdam\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-keyboard] E:\absensi\absensipdam\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-status-bar] E:\absensi\absensipdam\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] E:\absensi\absensipdam\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] E:\absensi\absensipdam\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ca6269356c469528ff6b9f1685f79bc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6286dbecde1ffd3ab412947624b8daf\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\813a0b5554c95f143c67b004c468397e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\db15cf71e8af3edb1bafe5c13aaffcb3\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\50cdf8e5b5c36378fcc75ae24fa6b2ed\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a33fbba25158287a68bfd669b0fbd8cb\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\01b8d59e2e170cb2f96e45aeaa1ca8cb\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0e4f73ef7e950802d5239bb0ed04eab\transformed\iongeolocation-android-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b0abb3dd2b4909e2a2bab894fcbfc22d\transformed\play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74b645c9d671c25cab4170f63136785d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-4\d8a574f5fe4f9b0a6179e7a98e7093c1\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\76c15aa2019fedc0a75f1d9bb376faf8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\31ba652c41369ff28d8e6dee8a9618db\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-4\c805d8e59cc8d8e960f9a6cd4408257c\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\8281808b23ecea55cd61ff696a408c00\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d59dd8a5ef72d79998ebb8d78dabcc1\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f14493f312c3f4eadb68c6a8cbf4563d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9278a49e96c911591c8ebdd2ea07196\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d01d57775c68b5ec073dd60a9b01fdd6\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f88148ff9e10da45c7d84a8ec821d2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca581ec854fdbe4661818d147b22120e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ee655c809e097c747cc030fc7a0bb0f\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\761fb12f48b9bb01ad3fa7855f8f3de1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed025da6510cc3f736f04d86e69b6ce3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6469b569004b15e753bcd52d2e98b157\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c50eda3906e1c133472018ba22074208\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6d0bb51d45c97b8eee93727f6e334b61\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cac9c049f890210ed72773b469669e6e\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\587286bc10727c834dc1091dc029ffe8\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fa265e6c166e0f773afbff8107bc21ee\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0407a2600ed5319bd765521c7ee5b4b8\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\bad46d921694a4fb08bd732e708b69cb\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\62e06f36fd52dbadce6d14a9ce2536b5\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\transforms-4\37ceeb3cfd0258bb0e4512fd1b507433\transformed\fragment-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-4\a5c753f78b1a7669935dbde916f7ad92\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\816a8e8382650c4a83605b9c6931e859\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-4\e585d0381fb4520d03a610224be796f7\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f116a56c8656799799f53e03e1d03e64\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\720960a286a3ac3a992bf0bacf4abb2e\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b075b47ad44408908813cb6894f1485\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea253cef4e4e6b8b96e7b38ddb512cba\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\038e8f8ce685d9365febdb09d3538d84\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\335f83cc90b4497618fd3f45ad44051f\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b161fd752656a8cbe8b6717196052804\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\408f01d849720485fee7cf9e16b3a08a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\5e8f833320ad98b582f468c6d63edc8d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\224ce41c8c3dc3f86067abd175be6c97\transformed\framework-10.1.1\AndroidManifest.xml:20:1-27:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
	package
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:4:5-51:19
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:4:5-51:19
MERGED from [:capacitor-cordova-android-plugins] E:\absensi\absensipdam\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-9:19
MERGED from [:capacitor-cordova-android-plugins] E:\absensi\absensipdam\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-9:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6286dbecde1ffd3ab412947624b8daf\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6286dbecde1ffd3ab412947624b8daf\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\813a0b5554c95f143c67b004c468397e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\813a0b5554c95f143c67b004c468397e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b0abb3dd2b4909e2a2bab894fcbfc22d\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b0abb3dd2b4909e2a2bab894fcbfc22d\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\62e06f36fd52dbadce6d14a9ce2536b5\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\62e06f36fd52dbadce6d14a9ce2536b5\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b075b47ad44408908813cb6894f1485\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b075b47ad44408908813cb6894f1485\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
	android:extractNativeLibs
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:8:9-54
	android:icon
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:10:9-40
activity#com.absensi.pdam.MainActivity
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:12:9-25:20
	android:label
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:15:13-56
	android:launchMode
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:17:13-44
	android:exported
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:18:13-36
	android:configChanges
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:13:13-140
	android:theme
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:16:13-62
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:14:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:22:17-77
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:22:27-74
receiver#com.absensi.pdam.AlarmReceiver
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:27:9-29:40
	android:exported
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:29:13-37
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:28:13-42
receiver#com.absensi.pdam.BootReceiver
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:31:9-40:20
	android:enabled
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:33:13-35
	android:exported
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:34:13-36
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:32:13-41
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.LOCKED_BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:35:13-39:29
action#android.intent.action.BOOT_COMPLETED
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:36:17-79
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:36:25-76
action#android.intent.action.LOCKED_BOOT_COMPLETED
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:37:17-86
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:37:25-83
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:38:17-84
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:38:25-81
uses-permission#android.permission.INTERNET
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:55:5-67
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:55:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:56:5-68
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:56:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:57:5-81
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:57:22-78
uses-permission#android.permission.CAMERA
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:58:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:58:22-62
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:59:5-79
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:59:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:60:5-81
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:60:22-78
uses-feature#android.hardware.camera
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:63:5-85
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:63:58-82
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:63:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:64:5-95
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:64:68-92
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:64:19-67
uses-feature#android.hardware.camera.flash
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:65:5-91
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:65:64-88
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:65:19-63
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:47:13-49:64
	android:resource
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:49:17-51
	android:name
		ADDED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml:48:17-67
uses-sdk
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
MERGED from [:capacitor-community-barcode-scanner] E:\absensi\absensipdam\node_modules\@capacitor-community\barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:67
MERGED from [:capacitor-community-barcode-scanner] E:\absensi\absensipdam\node_modules\@capacitor-community\barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:67
MERGED from [:capacitor-app] E:\absensi\absensipdam\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] E:\absensi\absensipdam\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] E:\absensi\absensipdam\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] E:\absensi\absensipdam\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] E:\absensi\absensipdam\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] E:\absensi\absensipdam\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] E:\absensi\absensipdam\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] E:\absensi\absensipdam\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] E:\absensi\absensipdam\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] E:\absensi\absensipdam\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] E:\absensi\absensipdam\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] E:\absensi\absensipdam\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ca6269356c469528ff6b9f1685f79bc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ca6269356c469528ff6b9f1685f79bc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6286dbecde1ffd3ab412947624b8daf\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6286dbecde1ffd3ab412947624b8daf\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\813a0b5554c95f143c67b004c468397e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\813a0b5554c95f143c67b004c468397e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\db15cf71e8af3edb1bafe5c13aaffcb3\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\db15cf71e8af3edb1bafe5c13aaffcb3\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\50cdf8e5b5c36378fcc75ae24fa6b2ed\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\50cdf8e5b5c36378fcc75ae24fa6b2ed\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a33fbba25158287a68bfd669b0fbd8cb\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a33fbba25158287a68bfd669b0fbd8cb\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\01b8d59e2e170cb2f96e45aeaa1ca8cb\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\01b8d59e2e170cb2f96e45aeaa1ca8cb\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0e4f73ef7e950802d5239bb0ed04eab\transformed\iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0e4f73ef7e950802d5239bb0ed04eab\transformed\iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b0abb3dd2b4909e2a2bab894fcbfc22d\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b0abb3dd2b4909e2a2bab894fcbfc22d\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74b645c9d671c25cab4170f63136785d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74b645c9d671c25cab4170f63136785d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-4\d8a574f5fe4f9b0a6179e7a98e7093c1\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-4\d8a574f5fe4f9b0a6179e7a98e7093c1\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\76c15aa2019fedc0a75f1d9bb376faf8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\76c15aa2019fedc0a75f1d9bb376faf8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\31ba652c41369ff28d8e6dee8a9618db\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\31ba652c41369ff28d8e6dee8a9618db\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-4\c805d8e59cc8d8e960f9a6cd4408257c\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-4\c805d8e59cc8d8e960f9a6cd4408257c\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\8281808b23ecea55cd61ff696a408c00\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\8281808b23ecea55cd61ff696a408c00\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d59dd8a5ef72d79998ebb8d78dabcc1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d59dd8a5ef72d79998ebb8d78dabcc1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f14493f312c3f4eadb68c6a8cbf4563d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f14493f312c3f4eadb68c6a8cbf4563d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9278a49e96c911591c8ebdd2ea07196\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a9278a49e96c911591c8ebdd2ea07196\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d01d57775c68b5ec073dd60a9b01fdd6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d01d57775c68b5ec073dd60a9b01fdd6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f88148ff9e10da45c7d84a8ec821d2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f88148ff9e10da45c7d84a8ec821d2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca581ec854fdbe4661818d147b22120e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca581ec854fdbe4661818d147b22120e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ee655c809e097c747cc030fc7a0bb0f\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ee655c809e097c747cc030fc7a0bb0f\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\761fb12f48b9bb01ad3fa7855f8f3de1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\761fb12f48b9bb01ad3fa7855f8f3de1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed025da6510cc3f736f04d86e69b6ce3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed025da6510cc3f736f04d86e69b6ce3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6469b569004b15e753bcd52d2e98b157\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6469b569004b15e753bcd52d2e98b157\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c50eda3906e1c133472018ba22074208\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c50eda3906e1c133472018ba22074208\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6d0bb51d45c97b8eee93727f6e334b61\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6d0bb51d45c97b8eee93727f6e334b61\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cac9c049f890210ed72773b469669e6e\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cac9c049f890210ed72773b469669e6e\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\587286bc10727c834dc1091dc029ffe8\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\587286bc10727c834dc1091dc029ffe8\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fa265e6c166e0f773afbff8107bc21ee\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fa265e6c166e0f773afbff8107bc21ee\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0407a2600ed5319bd765521c7ee5b4b8\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0407a2600ed5319bd765521c7ee5b4b8\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\bad46d921694a4fb08bd732e708b69cb\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\bad46d921694a4fb08bd732e708b69cb\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\62e06f36fd52dbadce6d14a9ce2536b5\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\62e06f36fd52dbadce6d14a9ce2536b5\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\transforms-4\37ceeb3cfd0258bb0e4512fd1b507433\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\transforms-4\37ceeb3cfd0258bb0e4512fd1b507433\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-4\a5c753f78b1a7669935dbde916f7ad92\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-4\a5c753f78b1a7669935dbde916f7ad92\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\816a8e8382650c4a83605b9c6931e859\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\816a8e8382650c4a83605b9c6931e859\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-4\e585d0381fb4520d03a610224be796f7\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-4\e585d0381fb4520d03a610224be796f7\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f116a56c8656799799f53e03e1d03e64\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f116a56c8656799799f53e03e1d03e64\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\720960a286a3ac3a992bf0bacf4abb2e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\720960a286a3ac3a992bf0bacf4abb2e\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b075b47ad44408908813cb6894f1485\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b075b47ad44408908813cb6894f1485\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea253cef4e4e6b8b96e7b38ddb512cba\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea253cef4e4e6b8b96e7b38ddb512cba\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\038e8f8ce685d9365febdb09d3538d84\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\038e8f8ce685d9365febdb09d3538d84\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\335f83cc90b4497618fd3f45ad44051f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\335f83cc90b4497618fd3f45ad44051f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b161fd752656a8cbe8b6717196052804\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b161fd752656a8cbe8b6717196052804\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\408f01d849720485fee7cf9e16b3a08a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\408f01d849720485fee7cf9e16b3a08a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\5e8f833320ad98b582f468c6d63edc8d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\5e8f833320ad98b582f468c6d63edc8d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\224ce41c8c3dc3f86067abd175be6c97\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\224ce41c8c3dc3f86067abd175be6c97\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
	tools:overrideLibrary
		ADDED from [:capacitor-community-barcode-scanner] E:\absensi\absensipdam\node_modules\@capacitor-community\barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-64
	android:targetSdkVersion
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\absensi\absensipdam\android\app\src\main\AndroidManifest.xml
queries
ADDED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
	android:name
		ADDED from [:capacitor-camera] E:\absensi\absensipdam\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
uses-permission#android.permission.VIBRATE
ADDED from [:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:capacitor-haptics] E:\absensi\absensipdam\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b51adc917640d95afb5d4798abf9ab6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c227367af2f4b77cf9615db1233119af\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c64b7fb2cbf1834569145bb91f70c00\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.absensi.pdam.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.absensi.pdam.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0193beb95545e88dd0f0a864925327\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\89cba2975059942639abe4e50b315e32\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\77bafeffa40caf3f8cdeb54ad021e60d\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\55d3226a80915a2c46c8498f5ba8b3e5\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4976c7da56d5a2dcc96331e1cd9cbda1\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74

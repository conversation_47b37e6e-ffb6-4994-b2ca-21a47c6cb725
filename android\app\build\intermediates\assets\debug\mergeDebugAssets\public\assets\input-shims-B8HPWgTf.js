import{a9 as e,ag as t,ah as o,ai as n,ad as i,ac as r,aj as a,ak as s,al as d,am as l,an as c}from"./ionic-CJlrxXsE.js";import"./react-vendor-DCX9i6UF.js";
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const u=new WeakMap,v=(e,t,o,n=0,i=!1)=>{u.has(e)!==o&&(o?m(e,t,n,i):f(e,t))},m=(e,t,o,n=!1)=>{const i=t.parentNode,r=t.cloneNode(!1);r.classList.add("cloned-input"),r.tabIndex=-1,n&&(r.disabled=!0),i.appendChild(r),u.set(e,r);const a="rtl"===e.ownerDocument.dir?9999:-9999;e.style.pointerEvents="none",t.style.transform="translate3d(".concat(a,"px,").concat(o,"px,0) scale(0)")},f=(e,t)=>{const o=u.get(e);o&&(u.delete(e),o.remove()),e.style.pointerEvents="",t.style.transform=""},p="input, textarea, [no-blur], [contenteditable]",w=(e,t,o,n)=>{const i=e.top,r=e.bottom,a=t.top,s=a+15,d=Math.min(t.bottom,n-o)-50-r,l=s-i,c=Math.round(d<0?-d:l>0?-l:0),u=Math.min(c,i-a),v=Math.abs(u)/.3;return{scrollAmount:u,scrollDuration:Math.min(400,Math.max(150,v)),scrollPadding:o,inputSafeY:4-(i-s)}},y="$ionPaddingTimer",h=(e,t,o)=>{const n=e[y];n&&clearTimeout(n),t>0?e.style.setProperty("--keyboard-offset","".concat(t,"px")):e[y]=setTimeout(()=>{e.style.setProperty("--keyboard-offset","0px"),o&&o()},120)},b=(e,t,o)=>{e.addEventListener("focusout",()=>{t&&h(t,0,o)},{once:!0})};let E=0;const g="data-ionic-skip-scroll-assist",S=e=>{var t;if(document.activeElement===e)return;const o=e.getAttribute("id"),n=e.closest('label[for="'.concat(o,'"]')),i=null===(t=document.activeElement)||void 0===t?void 0:t.closest('label[for="'.concat(o,'"]'));null!==n&&n===i||(e.setAttribute(g,"true"),e.focus())},L=async(e,t,o,n,i,r,a=!1,s=0,u=!0)=>{if(!o&&!n)return;const m=((e,t,o,n)=>{var i;const r=null!==(i=e.closest("ion-item,[ion-item]"))&&void 0!==i?i:e;return w(r.getBoundingClientRect(),t.getBoundingClientRect(),o,n)})(e,o||n,i,s);if(o&&Math.abs(m.scrollAmount)<4)return S(t),void(r&&null!==o&&(h(o,E),b(t,o,()=>E=0)));if(v(e,t,!0,m.inputSafeY,a),S(t),d(()=>e.click()),r&&o&&(E=m.scrollPadding,h(o,E)),"undefined"!=typeof window){let n;const i=async()=>{void 0!==n&&clearTimeout(n),window.removeEventListener("ionKeyboardDidShow",a),window.removeEventListener("ionKeyboardDidShow",i),o&&await c(o,0,m.scrollAmount,m.scrollDuration),v(e,t,!1,m.inputSafeY),S(t),r&&b(t,o,()=>E=0)},a=()=>{window.removeEventListener("ionKeyboardDidShow",a),window.addEventListener("ionKeyboardDidShow",i)};if(o){const e=await l(o),r=e.scrollHeight-e.clientHeight;if(u&&m.scrollAmount>r-e.scrollTop)return"password"===t.type?(m.scrollAmount+=50,window.addEventListener("ionKeyboardDidShow",a)):window.addEventListener("ionKeyboardDidShow",i),void(n=setTimeout(i,1e3))}i()}},D=async(d,l)=>{if(void 0===e)return;const c="ios"===l,u="android"===l,m=d.getNumber("keyboardHeight",290),f=d.getBoolean("scrollAssist",!0),w=d.getBoolean("hideCaretOnScroll",c),y=d.getBoolean("inputBlurring",!1),h=d.getBoolean("scrollPadding",!0),b=Array.from(e.querySelectorAll("ion-input, ion-textarea")),E=new WeakMap,S=new WeakMap,D=await t.getResizeMode(),A=async e=>{await new Promise(t=>i(e,t));const t=e.shadowRoot||e,d=t.querySelector("input")||t.querySelector("textarea"),l=r(e),c=l?null:e.closest("ion-footer");if(!d)return;if(l&&w&&!E.has(e)){const t=((e,t,i)=>{if(!i||!t)return()=>{};const r=o=>{var n;(n=t)===n.getRootNode().activeElement&&v(e,t,o)},a=()=>v(e,t,!1),s=()=>r(!0),d=()=>r(!1);return o(i,"ionScrollStart",s),o(i,"ionScrollEnd",d),t.addEventListener("blur",a),()=>{n(i,"ionScrollStart",s),n(i,"ionScrollEnd",d),t.removeEventListener("blur",a)}})(e,d,l);E.set(e,t)}if(!("date"===d.type||"datetime-local"===d.type)&&(l||c)&&f&&!S.has(e)){const t=((e,t,o,n,i,r,d,l=!1)=>{const c=r&&(void 0===d||d.mode===a.None);let u=!1;const v=void 0!==s?s.innerHeight:0,m=i=>{!1!==u?L(e,t,o,n,i.detail.keyboardHeight,c,l,v,!1):u=!0},f=()=>{u=!1,null==s||s.removeEventListener("ionKeyboardDidShow",m),e.removeEventListener("focusout",f)},p=async()=>{t.hasAttribute(g)?t.removeAttribute(g):(L(e,t,o,n,i,c,l,v),null==s||s.addEventListener("ionKeyboardDidShow",m),e.addEventListener("focusout",f))};return e.addEventListener("focusin",p),()=>{e.removeEventListener("focusin",p),null==s||s.removeEventListener("ionKeyboardDidShow",m),e.removeEventListener("focusout",f)}})(e,d,l,c,m,h,D,u);S.set(e,t)}};y&&(()=>{let e=!0,t=!1;const n=document,i=()=>{t=!0},r=()=>{e=!0},a=o=>{if(t)return void(t=!1);const i=n.activeElement;if(!i)return;if(i.matches(p))return;const r=o.target;r!==i&&(r.matches(p)||r.closest(p)||(e=!1,setTimeout(()=>{e||i.blur()},50)))};o(n,"ionScrollStart",i),n.addEventListener("focusin",r,!0),n.addEventListener("touchend",a,!1)})();for(const e of b)A(e);e.addEventListener("ionInputDidLoad",e=>{A(e.detail)}),e.addEventListener("ionInputDidUnload",e=>{(e=>{if(w){const t=E.get(e);t&&t(),E.delete(e)}if(f){const t=S.get(e);t&&t(),S.delete(e)}})(e.detail)})};export{D as startInputShims};

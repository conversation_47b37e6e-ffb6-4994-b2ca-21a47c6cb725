System.register(["./networkOptimizer-legacy-Beqb1gSF.js"],function(t,e){"use strict";var i;return{setters:[t=>{i=t.f}],execute:function(){t("fetchAndStoreBidang",async function(){try{const t=JSON.parse(localStorage.getItem("user")||"{}").bidang_id;if(!t)return;const e=await i("https://absensiku.trunois.my.id/api/bidang.php?api_key=absensiku_api_key_2023",{},18e5);if("success"===e.status&&Array.isArray(e.data)){const i=e.data.filter(e=>e.id==t);localStorage.setItem("bidang_list",JSON.stringify(i))}}catch(t){}})}}});

import { fetchAndStoreHariLibur } from './hariLibur';
import { fetchAndStoreJamKerja } from './jamKerja';
import { fetchAndStoreJamKerjaBidang } from './jamKerjaBidang';
import { fetchAndStoreBidang } from './bidang';
import { fetchAndStoreLokasi } from './lokasi';
import { batchRequests } from './networkOptimizer';

/**
 * Memanggil semua fungsi fetchAndStore untuk mendownload dan menyimpan data ke localStorage.
 * Mengembalikan status sukses/gagal untuk masing-masing data.
 * Optimasi: menggunakan batch requests untuk mengurangi beban pada perangkat lambat.
 */
export async function downloadAllDataWithStatus() {
  const results = {
    hariLibur: false,
    jamKerja: false,
    jamKerjaBidang: false,
    bidang: false,
    lokasi: false,
  };

  // Buat array request functions untuk batch processing
  const requests = [
    async () => {
      try {
        await fetchAndStoreHariLibur();
        results.hariLibur = !!localStorage.getItem('hari_libur_list');
        return true;
      } catch {
        results.hariLibur = false;
        return false;
      }
    },
    async () => {
      try {
        await fetchAndStoreJamKerja();
        results.jamKerja = !!localStorage.getItem('jam_kerja_list');
        return true;
      } catch {
        results.jamKerja = false;
        return false;
      }
    },
    async () => {
      try {
        await fetchAndStoreJamKerjaBidang();
        results.jamKerjaBidang = !!localStorage.getItem('jam_kerja_bidang_list');
        return true;
      } catch {
        results.jamKerjaBidang = false;
        return false;
      }
    },
    async () => {
      try {
        await fetchAndStoreBidang();
        results.bidang = !!localStorage.getItem('bidang_list');
        return true;
      } catch {
        results.bidang = false;
        return false;
      }
    },
    async () => {
      try {
        await fetchAndStoreLokasi();
        results.lokasi = !!localStorage.getItem('lokasi_list');
        return true;
      } catch {
        results.lokasi = false;
        return false;
      }
    }
  ];

  // Jalankan requests dalam batch dengan ukuran kecil untuk perangkat lambat
  await batchRequests(requests, 2); // Batch size 2 untuk mengurangi beban

  return results;
}
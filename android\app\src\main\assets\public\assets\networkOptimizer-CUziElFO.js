var e=Object.defineProperty,t=(t,a,s)=>(((t,a,s)=>{a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s})(t,"symbol"!=typeof a?a+"":a,s),s);const a=new class{constructor(){t(this,"cache",new Map),t(this,"pendingRequests",new Map),t(this,"CACHE_DURATION",{SHORT:3e5,MEDIUM:18e5,LONG:864e5})}async fetchWithCache(e,t={},a=this.CACHE_DURATION.MEDIUM){const s=this.generateCacheKey(e,t),n=this.getFromCache(s);if(n)return n;if(this.pendingRequests.has(s))return this.pendingRequests.get(s);const c=this.makeRequest(e,t).then(e=>(this.setCache(s,e,a),e)).finally(()=>{this.pendingRequests.delete(s)});return this.pendingRequests.set(s,c),c}async fetchWithRetry(e,t={},a=3,s=1e3){let n;for(let r=0;r<=a;r++)try{const a=new AbortController,s=setTimeout(()=>a.abort(),1e4),n=await fetch(e,{...t,signal:a.signal});if(clearTimeout(s),!n.ok)throw new Error("HTTP ".concat(n.status,": ").concat(n.statusText));return await n.json()}catch(c){n=c,r<a&&await this.delay(s*Math.pow(2,r))}throw n}async batchRequests(e,t=3){const a=[];for(let s=0;s<e.length;s+=t){const n=e.slice(s,s+t);(await Promise.allSettled(n.map(e=>e()))).forEach((e,t)=>{"fulfilled"===e.status?a[s+t]=e.value:a[s+t]=null}),s+t<e.length&&await this.delay(500)}return a}preloadResources(e){e.forEach(e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)})}clearExpiredCache(){const e=Date.now();for(const[t,a]of this.cache.entries())e>a.expiry&&this.cache.delete(t)}getCacheStats(){return{size:this.cache.size,hitRate:0}}generateCacheKey(e,t){const a=t.method||"GET",s=t.body?JSON.stringify(t.body):"";return"".concat(a,":").concat(e,":").concat(s)}getFromCache(e){const t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),null):t.data:null}setCache(e,t,a){this.cache.set(e,{data:t,timestamp:Date.now(),expiry:Date.now()+a})}async makeRequest(e,t){const a=await fetch(e,t);if(!a.ok)throw new Error("HTTP ".concat(a.status,": ").concat(a.statusText));return a.json()}delay(e){return new Promise(t=>setTimeout(t,e))}},s=(e,t,s)=>a.fetchWithCache(e,t,s),n=(e,t)=>a.batchRequests(e,t);setInterval(()=>{a.clearExpiredCache()},6e5);export{n as b,s as f};

#!/usr/bin/env node

/**
 * Script untuk testing performa aplikasi
 * Menjalankan berbagai test untuk memvalidasi optimasi yang telah diterapkan
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting Performance Testing...\n');

// Test 1: Bundle Size Analysis
console.log('📦 Testing Bundle Size...');
try {
  // Build aplikasi
  console.log('Building application...');
  execSync('npm run build', { stdio: 'inherit' });
  
  // Analisis ukuran bundle
  const distPath = path.join(__dirname, '../dist');
  const stats = getBundleStats(distPath);
  
  console.log('\n📊 Bundle Size Results:');
  console.log(`Total Size: ${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`JS Files: ${stats.jsFiles.length} files, ${(stats.jsSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`CSS Files: ${stats.cssFiles.length} files, ${(stats.cssSize / 1024).toFixed(2)} KB`);
  console.log(`Assets: ${stats.assetFiles.length} files, ${(stats.assetSize / 1024 / 1024).toFixed(2)} MB`);
  
  // Validasi target
  const targetSize = 2 * 1024 * 1024; // 2MB target
  if (stats.totalSize <= targetSize) {
    console.log('✅ Bundle size within target (<2MB)');
  } else {
    console.log('❌ Bundle size exceeds target (>2MB)');
  }
  
} catch (error) {
  console.error('❌ Bundle size test failed:', error.message);
}

// Test 2: Dependency Analysis
console.log('\n📋 Analyzing Dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
  const deps = Object.keys(packageJson.dependencies || {});
  const devDeps = Object.keys(packageJson.devDependencies || {});
  
  console.log(`Dependencies: ${deps.length}`);
  console.log(`Dev Dependencies: ${devDeps.length}`);
  
  // Check for heavy dependencies
  const heavyDeps = [
    '@react-google-maps/api',
    'leaflet',
    'maplibre-gl',
    'react-leaflet',
    'react-map-gl'
  ];
  
  const foundHeavyDeps = deps.filter(dep => heavyDeps.includes(dep));
  if (foundHeavyDeps.length > 0) {
    console.log('⚠️  Heavy dependencies found (ensure they are lazy loaded):');
    foundHeavyDeps.forEach(dep => console.log(`  - ${dep}`));
  } else {
    console.log('✅ No heavy dependencies in main bundle');
  }
  
} catch (error) {
  console.error('❌ Dependency analysis failed:', error.message);
}

// Test 3: Code Quality Checks
console.log('\n🔍 Running Code Quality Checks...');
try {
  // Check for console.log in production build
  const distFiles = getAllJSFiles(path.join(__dirname, '../dist'));
  let consoleLogsFound = 0;
  
  distFiles.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    const matches = content.match(/console\.log/g);
    if (matches) {
      consoleLogsFound += matches.length;
    }
  });
  
  if (consoleLogsFound === 0) {
    console.log('✅ No console.log found in production build');
  } else {
    console.log(`⚠️  Found ${consoleLogsFound} console.log statements in production build`);
  }
  
  // Check for lazy loading implementation
  const appTsxPath = path.join(__dirname, '../src/App.tsx');
  const appContent = fs.readFileSync(appTsxPath, 'utf8');
  
  if (appContent.includes('lazy(') && appContent.includes('Suspense')) {
    console.log('✅ Lazy loading implemented');
  } else {
    console.log('❌ Lazy loading not found');
  }
  
  // Check for memoization
  const homePagePath = path.join(__dirname, '../src/pages/Home.tsx');
  if (fs.existsSync(homePagePath)) {
    const homeContent = fs.readFileSync(homePagePath, 'utf8');
    if (homeContent.includes('useCallback') && homeContent.includes('useMemo')) {
      console.log('✅ React memoization implemented');
    } else {
      console.log('❌ React memoization not found');
    }
  }
  
} catch (error) {
  console.error('❌ Code quality check failed:', error.message);
}

// Test 4: Network Optimization Check
console.log('\n🌐 Checking Network Optimizations...');
try {
  const networkOptimizerPath = path.join(__dirname, '../src/utils/networkOptimizer.ts');
  if (fs.existsSync(networkOptimizerPath)) {
    console.log('✅ Network optimizer implemented');
    
    // Check if utils are using the optimizer
    const utilsPath = path.join(__dirname, '../src/utils');
    const utilFiles = fs.readdirSync(utilsPath).filter(file => file.endsWith('.ts'));
    
    let optimizedUtils = 0;
    utilFiles.forEach(file => {
      const content = fs.readFileSync(path.join(utilsPath, file), 'utf8');
      if (content.includes('fetchWithCache') || content.includes('networkOptimizer')) {
        optimizedUtils++;
      }
    });
    
    console.log(`✅ ${optimizedUtils}/${utilFiles.length} utils files optimized`);
  } else {
    console.log('❌ Network optimizer not found');
  }
} catch (error) {
  console.error('❌ Network optimization check failed:', error.message);
}

// Test 5: Performance Config Check
console.log('\n⚙️  Checking Performance Configuration...');
try {
  const configPath = path.join(__dirname, '../src/config/performanceConfig.ts');
  if (fs.existsSync(configPath)) {
    console.log('✅ Performance configuration found');
    
    const configContent = fs.readFileSync(configPath, 'utf8');
    if (configContent.includes('DeviceCapability') && configContent.includes('isLowEndDevice')) {
      console.log('✅ Device capability detection implemented');
    } else {
      console.log('❌ Device capability detection not found');
    }
  } else {
    console.log('❌ Performance configuration not found');
  }
} catch (error) {
  console.error('❌ Performance config check failed:', error.message);
}

// Test Summary
console.log('\n📋 Performance Test Summary');
console.log('==========================================');
console.log('✅ = Passed, ❌ = Failed, ⚠️  = Warning');
console.log('\nRecommendations:');
console.log('1. Test on actual low-end devices');
console.log('2. Monitor loading times in production');
console.log('3. Use Chrome DevTools for performance profiling');
console.log('4. Consider implementing Service Worker for offline caching');
console.log('\n🎉 Performance testing completed!');

// Helper functions
function getBundleStats(distPath) {
  const stats = {
    totalSize: 0,
    jsFiles: [],
    cssFiles: [],
    assetFiles: [],
    jsSize: 0,
    cssSize: 0,
    assetSize: 0
  };
  
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDirectory(filePath);
      } else {
        const size = stat.size;
        stats.totalSize += size;
        
        if (file.endsWith('.js')) {
          stats.jsFiles.push(file);
          stats.jsSize += size;
        } else if (file.endsWith('.css')) {
          stats.cssFiles.push(file);
          stats.cssSize += size;
        } else {
          stats.assetFiles.push(file);
          stats.assetSize += size;
        }
      }
    });
  }
  
  if (fs.existsSync(distPath)) {
    scanDirectory(distPath);
  }
  
  return stats;
}

function getAllJSFiles(dir) {
  const jsFiles = [];
  
  function scanDirectory(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    files.forEach(file => {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDirectory(filePath);
      } else if (file.endsWith('.js')) {
        jsFiles.push(filePath);
      }
    });
  }
  
  if (fs.existsSync(dir)) {
    scanDirectory(dir);
  }
  
  return jsFiles;
}

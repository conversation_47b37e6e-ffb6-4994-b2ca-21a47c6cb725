import{r as e,j as a,I as s,J as i,K as n,M as l,P as r,L as t,v as c,o,h as d,k as m,p as u,s as h,t as j,u as x,W as g,C as p,q as b,Q as k,N as y,H as f,w as _,O as v}from"./ionic-CJlrxXsE.js";import{r as S,t as N,d as w,g as D,h as L,j as C,c as M,s as T}from"./index-BZ7jmVXp.js";import{u as A}from"./react-vendor-DCX9i6UF.js";import"./utils-W2Gk7u7g.js";import"./capacitor-DGgumwVn.js";const F=()=>{const F=JSON.parse(localStorage.getItem("user")||"{}");A();const O=e.useRef(null),I=e.useRef(null),[<PERSON>,<PERSON>]=e.useState(""),[G,R]=e.useState(""),[E,P]=e.useState(""),[U,H]=e.useState(""),[z,B]=e.useState(""),[W,q]=e.useState(!1),[Q,Y]=e.useState("mulai"),[V,X]=e.useState(null),[Z,$]=e.useState(!1),[ee,ae]=e.useState(!1),[se,ie]=e.useState(""),[ne,le]=e.useState("success"),[re,te]=e.useState(!1),[ce,oe]=e.useState(""),[de,me]=e.useState(null),[ue,he]=e.useState([]),[je,xe]=e.useState([]),[ge,pe]=e.useState(!1),be=()=>{const e=new Date,a=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return"".concat(a,"-").concat(s,"-").concat(i)},ke=()=>(new Date).toTimeString().slice(0,5),ye=()=>{V&&(V.getTracks().forEach(e=>e.stop()),X(null))},fe=e=>{Y(e),q(!0),(async()=>{try{const e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});X(e),O.current&&(O.current.srcObject=e)}catch(e){ve("Gagal mengakses kamera","danger")}})()},_e=()=>{q(!1),ye()},ve=(e,a="success")=>{ie(e),le(a),ae(!0)},Se=async()=>{try{pe(!0);const e=encodeURIComponent(F.nama||F.name||""),a="https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023".concat(e?"&nama_karyawan=".concat(e):""),s=await fetch(a,{method:"GET",headers:{"x-api-key":"absensiku_api_key_2023","Content-Type":"application/json"}});if(s.ok){const e=be(),a=await s.json(),i=Array.isArray(a)?a:Array.isArray(null==a?void 0:a.data)?a.data:Array.isArray(null==a?void 0:a.lembur)?a.lembur:[];if(!Array.isArray(i))return ve("Format data lembur tidak dikenali","danger"),me(null),void he([]);const n=i.filter(e=>!(null==F?void 0:F.nama)&&!(null==F?void 0:F.name)||e.nama_karyawan===(F.nama||F.name)),l=n.find(a=>{const s=(a.status||"").toLowerCase();return!(a.jam_selesai&&""!==a.jam_selesai||"pending"!==s&&"menunggu"!==s||a.tanggal_lembur!==e)});me(l||null),he(n.filter(e=>{const a=(e.status||"").toLowerCase();return"selesai"===a||"selesai"===a}));const r=n.filter(a=>{const s=(a.status||"").toLowerCase();return!(a.jam_selesai&&""!==a.jam_selesai||"pending"!==s&&"menunggu"!==s||a.tanggal_lembur===e)});xe(r),r.length>0&&ve("Lembur sebelumnya melewati batas hari dan telah direset. Silakan mulai lembur baru.","warning")}else{await s.text();ve("Gagal memuat data lembur","danger")}}catch(e){ve("Gagal memuat data lembur","danger")}finally{pe(!1)}},Ne=e=>e?e.slice(0,5):"--:--",we=e=>new Date(e).toLocaleDateString("id-ID",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),De=(e,a)=>{if(!e||!a)return"--:--";const s=new Date("2000-01-01 ".concat(e)),i=new Date("2000-01-01 ".concat(a)).getTime()-s.getTime(),n=Math.floor(i/36e5),l=Math.floor(i%36e5/6e4);return"".concat(n,":").concat(l.toString().padStart(2,"0"))};return e.useEffect(()=>{Se()},[]),e.useEffect(()=>{G||de||R(ke()),P(de?ke():"")},[de]),a.jsxs(s,{children:[a.jsx(i,{children:a.jsxs(n,{children:[a.jsx(l,{slot:"start",children:a.jsx(r,{defaultHref:"/home"})}),a.jsx(t,{children:"Lembur"}),a.jsx(l,{slot:"end",children:a.jsx(c,{fill:"clear",onClick:Se,disabled:ge,children:a.jsx(o,{icon:S})})})]})}),a.jsxs(d,{fullscreen:!0,children:[de&&a.jsx(m,{className:"lembur-active-card",children:a.jsxs(u,{children:[a.jsxs("div",{className:"lembur-status-header",children:[a.jsx(o,{icon:N,className:"status-icon active"}),a.jsxs("div",{children:[a.jsx("h3",{children:"Lembur Sedang Berlangsung"}),a.jsxs("p",{children:["Dimulai: ",Ne(de.jam_mulai)," - ",we(de.tanggal_lembur)]})]})]}),a.jsxs("div",{className:"lembur-details",children:[a.jsxs("p",{children:[a.jsx("strong",{children:"Keterangan:"})," ",de.keterangan]}),a.jsxs("p",{children:[a.jsx("strong",{children:"Durasi:"})," ",De(de.jam_mulai,ke())]})]})]})}),!de&&a.jsx(m,{children:a.jsxs(u,{children:[a.jsxs("div",{className:"form-header",children:[a.jsx(o,{icon:w,className:"form-icon"}),a.jsx("h2",{children:"Mulai Lembur"})]}),a.jsxs(h,{children:[a.jsx(j,{position:"stacked",children:"Tanggal Lembur"}),a.jsx(x,{value:be(),readonly:!0,className:"readonly-input"})]}),a.jsxs(h,{children:[a.jsx(j,{position:"stacked",children:"Keterangan Lembur *"}),a.jsx(g,{value:J,onIonInput:e=>K(e.detail.value),placeholder:"Masukkan keterangan pekerjaan lembur...",rows:3})]}),a.jsxs(h,{children:[a.jsx(j,{position:"stacked",children:"Jam Mulai *"}),a.jsx(x,{type:"time",value:G,readonly:!0,className:"readonly-input"})]}),a.jsxs("div",{className:"photo-section",children:[a.jsx(j,{children:"Foto Mulai Lembur *"}),U?a.jsxs("div",{className:"photo-preview",children:[a.jsx("img",{src:"data:image/jpeg;base64,".concat(U),alt:"Foto Mulai"}),a.jsx(c,{fill:"clear",size:"small",onClick:()=>H(""),className:"remove-photo-btn",children:a.jsx(o,{icon:D})})]}):a.jsxs(c,{expand:"block",fill:"outline",onClick:()=>fe("mulai"),className:"camera-btn",children:[a.jsx(o,{icon:L,slot:"start"}),"Ambil Foto Mulai"]})]}),a.jsx(c,{expand:"block",onClick:async()=>{if(J.trim())if(G)if(U)try{$(!0);const e={nama_karyawan:F.nama||F.name,keterangan:J.trim(),tanggal_lembur:be(),jam_mulai:G,foto_mulai_base64:U,status:"Menunggu"},a=await fetch("https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023",{method:"POST",headers:{"x-api-key":"absensiku_api_key_2023","Content-Type":"application/json"},body:JSON.stringify(e)}),s=await a.json();"success"===s.status?(ve("Lembur berhasil dimulai","success"),K(""),R(""),H(""),Se()):ve(s.message||"Gagal memulai lembur","danger")}catch(e){ve("Terjadi kesalahan saat memulai lembur","danger")}finally{$(!1)}else ve("Foto mulai harus diambil","warning");else ve("Jam mulai harus diisi","warning");else ve("Keterangan harus diisi","warning")},disabled:Z||!J.trim()||!G||!U,className:"submit-btn",children:Z?a.jsx(p,{name:"crescent"}):"Mulai Lembur"})]})}),de&&a.jsx(m,{children:a.jsxs(u,{children:[a.jsxs("div",{className:"form-header",children:[a.jsx(o,{icon:C,className:"form-icon finish"}),a.jsx("h2",{children:"Selesai Lembur"})]}),a.jsxs(h,{children:[a.jsx(j,{position:"stacked",children:"Jam Selesai *"}),a.jsx(x,{type:"time",value:E,readonly:!0,className:"readonly-input"})]}),a.jsxs("div",{className:"photo-section",children:[a.jsx(j,{children:"Foto Selesai Lembur *"}),z?a.jsxs("div",{className:"photo-preview",children:[a.jsx("img",{src:"data:image/jpeg;base64,".concat(z),alt:"Foto Selesai"}),a.jsx(c,{fill:"clear",size:"small",onClick:()=>B(""),className:"remove-photo-btn",children:a.jsx(o,{icon:D})})]}):a.jsxs(c,{expand:"block",fill:"outline",onClick:()=>fe("selesai"),className:"camera-btn",children:[a.jsx(o,{icon:L,slot:"start"}),"Ambil Foto Selesai"]})]}),a.jsx(c,{expand:"block",onClick:async()=>{if(de)if(E)if(z)try{$(!0);const e={id:de.id,jam_selesai:E,foto_selesai_base64:z,status:"Selesai"},a=await fetch("https://absensiku.trunois.my.id/api/lembur.php?api_key=absensiku_api_key_2023",{method:"PUT",headers:{"x-api-key":"absensiku_api_key_2023","Content-Type":"application/json"},body:JSON.stringify(e)}),s=await a.json();"success"===s.status?(ve("Lembur berhasil diselesaikan","success"),P(""),B(""),Se()):ve(s.message||"Gagal menyelesaikan lembur","danger")}catch(e){ve("Terjadi kesalahan saat menyelesaikan lembur","danger")}finally{$(!1)}else ve("Foto selesai harus diambil","warning");else ve("Jam selesai harus diisi","warning");else ve("Tidak ada lembur aktif","warning")},disabled:Z||!E||!z,className:"submit-btn finish",children:Z?a.jsx(p,{name:"crescent"}):"Selesai Lembur"})]})}),ue.length>0&&a.jsx(m,{children:a.jsxs(u,{children:[a.jsxs("div",{className:"form-header",children:[a.jsx(o,{icon:M,className:"form-icon"}),a.jsx("h2",{children:"Riwayat Lembur"})]}),a.jsx(b,{children:ue.map((e,s)=>a.jsx(h,{className:"history-item",children:a.jsxs("div",{className:"history-content",children:[a.jsxs("div",{className:"history-header",children:[a.jsx("h3",{children:we(e.tanggal_lembur)}),a.jsx(k,{color:"success",children:e.status})]}),a.jsxs("p",{children:[a.jsx("strong",{children:"Keterangan:"})," ",e.keterangan]}),a.jsxs("div",{className:"time-info",children:[a.jsxs("span",{children:["Mulai: ",Ne(e.jam_mulai)]}),a.jsxs("span",{children:["Selesai: ",Ne(e.jam_selesai||"")]}),a.jsxs("span",{children:["Durasi: ",De(e.jam_mulai,e.jam_selesai||"")]})]})]})},s))})]})}),je.length>0&&a.jsx(m,{children:a.jsxs(u,{children:[a.jsxs("div",{className:"form-header",children:[a.jsx(o,{icon:T,className:"form-icon"}),a.jsx("h2",{children:"Lembur Belum Diselesaikan"})]}),a.jsx(b,{children:je.map((e,s)=>a.jsx(h,{className:"history-item",children:a.jsxs("div",{className:"history-content",children:[a.jsxs("div",{className:"history-header",children:[a.jsx("h3",{children:we(e.tanggal_lembur)}),a.jsx(k,{color:"warning",children:"Menunggu"})]}),a.jsxs("p",{children:[a.jsx("strong",{children:"Keterangan:"})," ",e.keterangan]}),a.jsxs("div",{className:"time-info",children:[a.jsxs("span",{children:["Mulai: ",Ne(e.jam_mulai)]}),a.jsx("span",{children:"Selesai: --:--"}),a.jsx("span",{children:"Durasi: --:--"})]}),a.jsx(y,{color:"danger",children:"Anda tidak melakukan update selesai pada lembur"})]})},s))})]})}),a.jsxs(f,{isOpen:W,onDidDismiss:_e,children:[a.jsx(i,{children:a.jsxs(n,{children:[a.jsxs(t,{children:["Ambil Foto ","mulai"===Q?"Mulai":"Selesai"]}),a.jsx(l,{slot:"end",children:a.jsx(c,{fill:"clear",onClick:_e,children:a.jsx(o,{icon:D})})})]})}),a.jsx(d,{children:a.jsxs("div",{className:"camera-container",children:[a.jsx("video",{ref:O,autoPlay:!0,playsInline:!0,className:"camera-video"}),a.jsx("canvas",{ref:I,style:{display:"none"}}),a.jsx("div",{className:"camera-controls",children:a.jsxs(c,{expand:"block",onClick:()=>{if(O.current&&I.current){const e=I.current,a=O.current,s=e.getContext("2d");if(e.width=a.videoWidth,e.height=a.videoHeight,s){s.drawImage(a,0,0);const i=e.toDataURL("image/jpeg",.8).split(",")[1];"mulai"===Q?H(i):B(i),q(!1),ye(),ve("Foto berhasil diambil","success")}}},className:"capture-btn",children:[a.jsx(o,{icon:L,slot:"start"}),"Ambil Foto"]})})]})})]}),a.jsx(_,{isOpen:ee,onDidDismiss:()=>ae(!1),message:se,duration:3e3,color:ne}),a.jsx(v,{isOpen:re,onDidDismiss:()=>te(!1),header:"Informasi",message:ce,buttons:["OK"]})]})]})};export{F as default};

// Utility untuk optimasi network requests dan caching
interface CacheItem {
  data: any;
  timestamp: number;
  expiry: number;
}

class NetworkOptimizer {
  private cache = new Map<string, CacheItem>();
  private pendingRequests = new Map<string, Promise<any>>();
  
  // Cache duration dalam milliseconds
  private readonly CACHE_DURATION = {
    SHORT: 5 * 60 * 1000,    // 5 menit
    MEDIUM: 30 * 60 * 1000,  // 30 menit
    LONG: 24 * 60 * 60 * 1000 // 24 jam
  };

  /**
   * Fetch dengan caching dan deduplication
   */
  async fetchWithCache(
    url: string, 
    options: RequestInit = {}, 
    cacheDuration: number = this.CACHE_DURATION.MEDIUM
  ): Promise<any> {
    const cacheKey = this.generateCacheKey(url, options);
    
    // Cek cache terlebih dahulu
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    // Cek apakah request yang sama sedang berjalan (deduplication)
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }

    // Buat request baru
    const requestPromise = this.makeRequest(url, options)
      .then(data => {
        // Simpan ke cache
        this.setCache(cacheKey, data, cacheDuration);
        return data;
      })
      .finally(() => {
        // Hapus dari pending requests
        this.pendingRequests.delete(cacheKey);
      });

    // Simpan ke pending requests
    this.pendingRequests.set(cacheKey, requestPromise);
    
    return requestPromise;
  }

  /**
   * Fetch dengan retry logic untuk koneksi tidak stabil
   */
  async fetchWithRetry(
    url: string,
    options: RequestInit = {},
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<any> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Tambahkan timeout untuk perangkat lambat
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 detik timeout

        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        lastError = error as Error;
        
        // Jika bukan attempt terakhir, tunggu sebelum retry
        if (attempt < maxRetries) {
          await this.delay(retryDelay * Math.pow(2, attempt)); // Exponential backoff
        }
      }
    }

    throw lastError!;
  }

  /**
   * Batch multiple requests untuk efisiensi
   */
  async batchRequests<T>(
    requests: Array<() => Promise<T>>,
    batchSize: number = 3
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(request => request())
      );
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results[i + index] = result.value;
        } else {
          console.error(`Batch request ${i + index} failed:`, result.reason);
          results[i + index] = null as any;
        }
      });

      // Delay antar batch untuk tidak overload perangkat lambat
      if (i + batchSize < requests.length) {
        await this.delay(500);
      }
    }
    
    return results;
  }

  /**
   * Preload critical resources
   */
  preloadResources(urls: string[]): void {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0 // Implement hit rate tracking if needed
    };
  }

  // Private methods
  private generateCacheKey(url: string, options: RequestInit): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  private getFromCache(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  private setCache(key: string, data: any, duration: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + duration
    });
  }

  private async makeRequest(url: string, options: RequestInit): Promise<any> {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response.json();
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const networkOptimizer = new NetworkOptimizer();

// Utility functions untuk kemudahan penggunaan
export const fetchWithCache = (url: string, options?: RequestInit, cacheDuration?: number) =>
  networkOptimizer.fetchWithCache(url, options, cacheDuration);

export const fetchWithRetry = (url: string, options?: RequestInit, maxRetries?: number) =>
  networkOptimizer.fetchWithRetry(url, options, maxRetries);

export const batchRequests = <T>(requests: Array<() => Promise<T>>, batchSize?: number) =>
  networkOptimizer.batchRequests(requests, batchSize);

// Auto cleanup expired cache setiap 10 menit
setInterval(() => {
  networkOptimizer.clearExpiredCache();
}, 10 * 60 * 1000);

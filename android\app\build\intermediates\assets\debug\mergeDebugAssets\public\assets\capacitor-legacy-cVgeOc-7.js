System.register(["./ionic-legacy-DbGqp7zN.js"],function(e,t){"use strict";var n;return{setters:[e=>{n=e.d}],execute:function(){/*! Capacitor: https://capacitorjs.com/ - MIT License */var r;!function(e){e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE"}(r||(r={}));class i extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const a=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},a=n.Plugins=n.Plugins||{},o=()=>null!==t?t.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),s=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},c=new Map;return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=o,n.handleError=t=>e.console.error(t),n.isNativePlatform=()=>"web"!==o(),n.isPluginAvailable=e=>{const t=c.get(e);return!!(null==t?void 0:t.platforms.has(o()))||!!s(e)},n.registerPlugin=(e,l={})=>{const d=c.get(e);if(d)return d.proxy;const u=o(),p=s(e);let m;const h=a=>{let o;const s=(...s)=>{const c=(async()=>(!m&&u in l?m=m="function"==typeof l[u]?await l[u]():l[u]:null!==t&&!m&&"web"in l&&(m=m="function"==typeof l.web?await l.web():l.web),m))().then(t=>{const c=((t,a)=>{var o,s;if(!p){if(t)return null===(s=t[a])||void 0===s?void 0:s.bind(t);throw new i(`"${e}" plugin is not implemented on ${u}`,r.Unimplemented)}{const r=null==p?void 0:p.methods.find(e=>a===e.name);if(r)return"promise"===r.rtype?t=>n.nativePromise(e,a.toString(),t):(t,r)=>n.nativeCallback(e,a.toString(),t,r);if(t)return null===(o=t[a])||void 0===o?void 0:o.bind(t)}})(t,a);if(c){const e=c(...s);return o=null==e?void 0:e.remove,e}throw new i(`"${e}.${a}()" is not implemented on ${u}`,r.Unimplemented)});return"addListener"===a&&(c.remove=async()=>o()),c};return s.toString=()=>`${a.toString()}() { [capacitor code] }`,Object.defineProperty(s,"name",{value:a,writable:!1,configurable:!1}),s},w=h("addListener"),f=h("removeListener"),g=(e,t)=>{const n=w({eventName:e},t),r=async()=>{const r=await n;f({eventName:e,callbackId:r},t)},i=new Promise(e=>n.then(()=>e({remove:r})));return i.remove=async()=>{await r()},i},y=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return p?g:w;case"removeListener":return f;default:return h(t)}}});return a[e]=y,c.set(e,{name:e,proxy:y,platforms:new Set([...Object.keys(l),...p?[u]:[]])}),y},n.Exception=i,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},o=(e=>e.Capacitor=a(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),s=e("r",o.registerPlugin);class c{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const r=this.windowListeners[e];return r&&!r.registered&&this.addWindowListener(r),n&&this.sendRetainedArgumentsForEvent(e),Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const r=this.listeners[e];if(r)r.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new o.Exception(e,r.Unimplemented)}unavailable(e="not available"){return new o.Exception(e,r.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const r=n.indexOf(t);this.listeners[e].splice(r,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}e("W",c);const l=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),d=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class u extends c{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,r]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=d(n).trim(),r=d(r).trim(),t[n]=r}),t}async setCookie(e){try{const t=l(e.key),n=l(e.value),r=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),a=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${r}; path=${i}; ${a};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}s("CapacitorCookies",{web:()=>new u});const p=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),r=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,r,i)=>(n[r]=e[t[i]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(r.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,r]of Object.entries(e.data||{}))t.set(n,r);n.body=t.toString()}else if(r.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const r=new Headers(n.headers);r.delete("content-type"),n.headers=r}else(r.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class m extends c{async request(e){const t=p(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[r,i]=n;let a,o;return Array.isArray(i)?(o="",i.forEach(e=>{a=t?encodeURIComponent(e):e,o+=`${r}=${a}&`}),o.slice(0,-1)):(a=t?encodeURIComponent(i):i,o=`${r}=${a}`),`${e}&${o}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),r=n?`${e.url}?${n}`:e.url,i=await fetch(r,t),a=i.headers.get("content-type")||"";let o,s,{responseType:c="text"}=i.ok?e:{};switch(a.includes("application/json")&&(c="json"),c){case"arraybuffer":case"blob":s=await i.blob(),o=await(async e=>new Promise((t,n)=>{const r=new FileReader;r.onload=()=>{const e=r.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},r.onerror=e=>n(e),r.readAsDataURL(e)}))(s);break;case"json":o=await i.json();break;default:o=await i.text()}const l={};return i.headers.forEach((e,t)=>{l[t]=e}),{data:o,headers:l,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}var h,w,f;s("CapacitorHttp",{web:()=>new m}),function(e){e.Prompt="PROMPT",e.Camera="CAMERA",e.Photos="PHOTOS"}(h||(h={})),function(e){e.Rear="REAR",e.Front="FRONT"}(w||(w={})),function(e){e.Uri="uri",e.Base64="base64",e.DataUrl="dataUrl"}(f||(f={}));class g extends c{async getPhoto(e){return new Promise(async(t,n)=>{if(e.webUseInput||e.source===h.Photos)this.fileInputExperience(e,t,n);else if(e.source===h.Prompt){let r=document.querySelector("pwa-action-sheet");r||(r=document.createElement("pwa-action-sheet"),document.body.appendChild(r)),r.header=e.promptLabelHeader||"Photo",r.cancelable=!1,r.options=[{title:e.promptLabelPhoto||"From Photos"},{title:e.promptLabelPicture||"Take Picture"}],r.addEventListener("onSelection",async r=>{0===r.detail?this.fileInputExperience(e,t,n):this.cameraExperience(e,t,n)})}else this.cameraExperience(e,t,n)})}async pickImages(e){return new Promise(async(e,t)=>{this.multipleFileInputExperience(e,t)})}async cameraExperience(e,t,n){if(customElements.get("pwa-camera-modal")){const a=document.createElement("pwa-camera-modal");a.facingMode=e.direction===w.Front?"user":"environment",document.body.appendChild(a);try{await a.componentOnReady(),a.addEventListener("onPhoto",async r=>{const o=r.detail;null===o?n(new i("User cancelled photos app")):o instanceof Error?n(o):t(await this._getCameraPhoto(o,e)),a.dismiss(),document.body.removeChild(a)}),a.present()}catch(r){this.fileInputExperience(e,t,n)}}else this.fileInputExperience(e,t,n)}fileInputExperience(e,t,n){let r=document.querySelector("#_capacitor-camera-input");const a=()=>{var e;null===(e=r.parentNode)||void 0===e||e.removeChild(r)};r||(r=document.createElement("input"),r.id="_capacitor-camera-input",r.type="file",r.hidden=!0,document.body.appendChild(r),r.addEventListener("change",n=>{const i=r.files[0];let o="jpeg";if("image/png"===i.type?o="png":"image/gif"===i.type&&(o="gif"),"dataUrl"===e.resultType||"base64"===e.resultType){const n=new FileReader;n.addEventListener("load",()=>{if("dataUrl"===e.resultType)t({dataUrl:n.result,format:o});else if("base64"===e.resultType){const e=n.result.split(",")[1];t({base64String:e,format:o})}a()}),n.readAsDataURL(i)}else t({webPath:URL.createObjectURL(i),format:o}),a()}),r.addEventListener("cancel",e=>{n(new i("User cancelled photos app")),a()})),r.accept="image/*",r.capture=!0,e.source===h.Photos||e.source===h.Prompt?r.removeAttribute("capture"):e.direction===w.Front?r.capture="user":e.direction===w.Rear&&(r.capture="environment"),r.click()}multipleFileInputExperience(e,t){let n=document.querySelector("#_capacitor-camera-input-multiple");const r=()=>{var e;null===(e=n.parentNode)||void 0===e||e.removeChild(n)};n||(n=document.createElement("input"),n.id="_capacitor-camera-input-multiple",n.type="file",n.hidden=!0,n.multiple=!0,document.body.appendChild(n),n.addEventListener("change",t=>{const i=[];for(let e=0;e<n.files.length;e++){const t=n.files[e];let r="jpeg";"image/png"===t.type?r="png":"image/gif"===t.type&&(r="gif"),i.push({webPath:URL.createObjectURL(t),format:r})}e({photos:i}),r()}),n.addEventListener("cancel",e=>{t(new i("User cancelled photos app")),r()})),n.accept="image/*",n.click()}_getCameraPhoto(e,t){return new Promise((n,r)=>{const i=new FileReader,a=e.type.split("/")[1];"uri"===t.resultType?n({webPath:URL.createObjectURL(e),format:a,saved:!1}):(i.readAsDataURL(e),i.onloadend=()=>{const e=i.result;"dataUrl"===t.resultType?n({dataUrl:e,format:a,saved:!1}):n({base64String:e.split(",")[1],format:a,saved:!1})},i.onerror=e=>{r(e)})})}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(await window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch(e){throw this.unavailable("Camera permissions are not available in this browser")}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}async pickLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}async getLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}}e("C",s("Camera",{web:()=>new g})),e("G",s("Geolocation",{web:()=>n(()=>t.import("./web-legacy-vFcQ_Adn.js"),void 0).then(e=>new e.GeolocationWeb)})),function(e=!1){typeof window>"u"||(window.CapacitorUtils=window.CapacitorUtils||{},void 0===window.Capacitor||e?void 0!==window.cordova&&function(e){e.CapacitorUtils.Synapse=new Proxy({},{get:(t,n)=>e.cordova.plugins[n]})}(window):function(e){e.CapacitorUtils.Synapse=new Proxy({},{get:(t,n)=>new Proxy({},{get:(t,r)=>(t,i,a)=>{const o=e.Capacitor.Plugins[n];void 0!==o?"function"==typeof o[r]?(async()=>{try{const e=await o[r](t);i(e)}catch(e){a(e)}})():a(new Error(`Method ${r} not found in Capacitor plugin ${n}`)):a(new Error(`Capacitor plugin ${n} not found`))}})})}(window))}()}}});
